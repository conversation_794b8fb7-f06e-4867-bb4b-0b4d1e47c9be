/* pages/module-mentorShop/course-detail/components/tab.wxss */
.course {
  margin-top: 28rpx;
  border-radius: 16rpx;
  box-shadow: 0px 0px 16px 0px rgba(99, 97, 155, 0.1);
  background: #fff;
  .tab-container {
    display: flex;
    align-items: center;

    margin: 0rpx 36rpx 0;
    padding: 30rpx 0rpx 0;
    border-bottom: 2rpx solid #c4c4c4;
    .tab {
      position: relative;
      padding: 10rpx 0;
      margin-right: 48rpx;
      font-size: 30rpx;
      color: #797979;
      display: flex;
      align-items: center;
      text {
        font-size: 20rpx;
      }
      .tab-lock {
        width: 22rpx;
        height: 26rpx;
      }
      &.active {
        color: #0057ff;
        font-weight: bold;

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 48rpx;
          height: 4rpx;
          background: #0057ff;
          border-radius: 26rpx;
        }
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
  .container {
    margin-top: 30rpx;
    position: relative;
    padding-bottom: 20rpx;
    .course-desc {
      padding: 0 36rpx 20rpx;
      position: relative;
      .overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
    .no-copy {
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }
    .desc-content {
      color: #acacac;
      font-size: 26rpx;

      .desc-txt {
        padding: 0rpx 36rpx;
        margin: 16rpx 0;
      }
      image {
        width: 100%;
      }
    }
    .source {
      padding: 16rpx 36rpx;
      font-size: 26rpx;
      background-color: #f8f8f8;
      text:last-child {
        color: #0057ff;
      }
    }
    .course-list {
      padding: 0rpx 36rpx;
      padding-bottom: 30rpx;
      .course-item {
        padding: 24rpx;
        margin-bottom: 24rpx;
        display: flex;
        border-radius: 16rpx;
        align-items: flex-start;
        background: rgb(255, 255, 255);
        box-shadow: 0 0 33rpx 0 rgba(99, 97, 155, 0.1);
        &:last-child {
          border-bottom: none;
        }

        .course-image {
          width: 244rpx;
          height: 136rpx;
          border-radius: 8rpx;
          margin-right: 16rpx;
          flex-shrink: 0;
        }

        .course-right {
          flex: 1;
        }
        .course-info {
          display: flex;
          justify-content: space-between;
          .course-title {
            font-size: 26rpx;
            color: #333;
            font-weight: bold;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .course-score {
            font-size: 20rpx;
            color: #9ea3ac;
            .score {
              color: #0057ff;
            }
          }
        }
        .course-duration {
          margin: 8rpx 0 24rpx;
          font-size: 24rpx;
          color: #9ea3ac;
          flex-shrink: 0;
        }

        .course-status {
          font-size: 24rpx;
          color: #9ea3ac;
          .divider {
            margin: 0 6rpx;
          }
        }
      }
    }
  }
}
.mp-html-content {
  word-wrap: break-word;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}
