// pages/module-home/appoint/appoint-list/appoint-list.js
import { getAppointList } from '~/api/appoint'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabActive: '',
    tabList: [
      {
        id: '',
        label: '全部'
      },
      {
        id: 10,
        label: '待沟通'
      },
      {
        id: 20,
        label: '已沟通'
      },
      {
        id: 30,
        label: '已完成'
      },
      {
        id: 40,
        label: '已取消'
      },
      {
        id: 50,
        label: '已错过'
      }
    ],

    // 分页参数
    appointList: [],
    pageSize: 20,
    pageNumber: 0,
    loading: false,
    finished: false,

    showEmpty: false, // 跟列表一起使用，如果列表没数据，且这个是true的时候才展示空
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.appointLoad()
  },
  toAppointDetail(event) {
    wx.navigateTo({
      url: `/pages/module-home/appoint/appoint-detail/appoint-detail?appointId=${event.currentTarget.dataset.item.id}`,
    })
  },
  loadMore() {
    this.appointLoad()
  },
  async appointLoad() {
    if (this.data.loading) {
      return
    }
    if (this.data.finished) {
      return
    }
    this.setData({
      pageNumber: this.data.pageNumber + 1,
      loading: true
    })
    const { data } = await getAppointList({
      pageSize: this.data.pageSize,
      pageNumber: this.data.pageNumber,
      searchAppointStatus: this.data.tabActive
    })
    if (data && data.length > 0) {
      const newData = [...this.data.appointList, ...data]
      this.setData({
        appointList: newData
      })
    } else {
      this.setData({
        finished: true
      })
    }
    if (this.data.appointList && this.data.appointList.length > 0) {
      this.setData({
        showEmpty: false
      })
    } else {
      this.setData({
        showEmpty: true
      })
    }
    this.setData({
      loading: false
    })
  },
  tabChange(e) {
    this.setData({
      appointList: [],
      pageSize: 20,
      pageNumber: 0,
      loading: false,
      finished: false,
      tabActive: e.detail.name || ''
    })
    this.appointLoad()
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})