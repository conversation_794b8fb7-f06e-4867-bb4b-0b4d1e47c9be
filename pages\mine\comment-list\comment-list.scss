page{
  height: 100%;
}
.comment-list{
  background: #f8f8f8;
  padding: 0 36rpx 48rpx;
  height: 100%;
  .scrollarea{
    height: 100%;
  }
  .comment-box{
    .comment-item{
      .card-box{
        margin-top: 28rpx;
        padding: 30rpx 28rpx;
        border-radius: 16rpx;
        background-color: #FFF;
        .header{
          display: flex;
          justify-content: space-between;
          align-items: center;
          .comment-user{
            display: flex;
            align-items: center;
            flex: 1;
            image{
              width: 48rpx;
              height: 48rpx;
              margin-right: 14rpx;
              border-radius: 50%;
              object-fit: cover;
            }
            span{
              color: rgb(51, 51, 51);
              font-family: PingFang SC;
              font-size: 28rpx;
              font-weight: 500;
              line-height: 34rpx;
            }
          }
          .comment-operation{
            margin-left: 12rpx;
            // width: 56px;
            // height: 18px;
            transition: all linear .2s;
            padding: 8rpx 0;
            width: 150rpx;
            background: #0057FF;
            border-radius: 76rpx;
            position: relative;
            color: rgb(255, 255, 255);
            font-size: 24rpx;
            font-weight: 500;
            line-height: 28rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            .circle{
              width: 28rpx;
              height: 28rpx;
              border-radius: 50%;
              margin-left: 4rpx;
              background: #fff;
            }
          }
          .comment-operation-down{
            // padding-right: 7px;
            background: rgb(212, 212, 212);
            color: rgb(255, 255, 255);
            .circle{
              margin-left: 0;
              margin-right: 4rpx;
            }
          }
        }
        .content{
          margin-top: 12rpx;
          color: rgb(115, 119, 131);
          font-size: 24rpx;
          font-weight: 400;
          line-height: 36rpx;
        }
        .topic{
          background: rgb(242, 244, 255);
          border-radius: 8rpx;
          margin-top: 12rpx;
          padding: 6rpx 12rpx;
          color: rgb(51, 51, 51);
          font-size: 24rpx;
          font-weight: 400;
          line-height: 28rpx;
          display: inline-block;
          
          view{
            flex: 1;
            // @include textoverflow;
          }
        }
        .date{
          margin-top: 16rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: rgb(158, 163, 172);
          font-size: 24rpx;
          font-weight: 400;
          line-height: 34rpx;
          .like-number{
            display: flex;
            align-items: center;
            color: rgb(140, 145, 154);
            image{
              width: 30rpx;
              height: 30rpx;
            }
          }
        }
      }
    }
  }
  .empty{
    padding-top: 120rpx;
  }
}
