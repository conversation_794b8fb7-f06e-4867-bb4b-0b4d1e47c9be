page{
  height: 100%;
}
.personal-letter-list{
  height: 100%;
  padding: 16rpx;
  padding-top: 0;
  background: #eff1f7;
  scroll-view {
    height: 100%;
  }
  .list {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 28rpx 28rpx 32rpx;
    display: flex;
    align-items: center;
    margin-top: 16rpx;
    .left {
      image {
        width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
      }
    }
    .right {
      margin-left: 16rpx;
      flex: 1;
      overflow: hidden;
      .top,
      .bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .top {
        margin-bottom: 8rpx;
      }
      .name {
        font-size: 32rpx;
        color: #000000;
        line-height: 40rpx;
        font-weight: 600;
        flex: 1;
        // @include textoverflow(1);
      }
      .time {
        color: #919bae;
        font-size: 24rpx;
        line-height: 28rpx;
      }
      .txt {
        color: #919bae;
        font-size: 28rpx;
        line-height: 32rpx;
        // width: 70vw;
        flex: 1;
        margin-right: 20rpx;
      }
      .number {
        color: #ffffff;
        font-size: 24rpx;
        height: 34rpx;
        line-height: 28rpx;
        background: #ff5d39;
        border-radius: 26rpx;
        padding: 0 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  .finshed-text{
    margin-top: 20rpx;
    color: #969799;
    font-size: 32rpx;
    line-height: 100rpx;
    text-align: center;
  }
}
