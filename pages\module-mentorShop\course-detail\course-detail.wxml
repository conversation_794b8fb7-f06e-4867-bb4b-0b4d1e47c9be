<!--pages/module-mentorShop/course-detail/course-detail.wxml-->
<scroll-box focusHideBottom="{{true}}">
  <view slot="content" class="course-detail">
    <!-- 课程封面 -->
    <view class="course-banner">
      <video id="courseVideo" wx:if="{{courseInfo.courseType === 10}}" src="{{courseInfo.videoUrl}}" danmu-list="{{courseInfo.isMarquee===1?danmuList:[]}}" enable-danmu poster="{{courseInfo.courseCoverUrl}}" bindplay="handleVideoPlay" bindpause="handleVideoPause" bindended="handleVideoEnded" />
      <image wx:else src="{{courseInfo.courseCoverUrl}}" mode="aspectFill" />
    </view>

    <!-- 课程标题区域 -->
    <view class="course-header">
      <view class="title">{{courseInfo.courseName}}</view>
      <view class="meta-info">
        <view class="meta">
          <view class="tag">
            <image class="tag-img" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/{{courseInfo.courseType===10?'voice-mode':'article-mode'}}.png" mode="aspectFill"></image>
            {{courseInfo.courseType===10?'视频':'图文'}}教学
          </view>
          <view class="date"><view wx:if="{{courseInfo.courseType === 30}}">{{courseInfo.author}}</view>{{courseInfo.createdAt}}</view>
        </view>
        <view class="price-box">
          <view class="price" wx:if="{{courseInfo.saleMethod === 10}}"><text>￥</text><text>{{courseInfo.coursePrice}}</text></view>
          <view class="price" wx:else>免费</view>
          <view class="participants" wx:if="{{courseInfo.addCount>0}}">{{courseInfo.addCount}}人购买</view>
        </view>
      </view>
    </view>

    <!-- 讲师信息 -->
    <view class="instructor-section">
      <view class="section-title">讲师简介</view>
      <view class="mentor-base">
        <view class="mentor-base-info">
          <image class="avatar" src="{{mentorInfo.mentorBase.user.avatarPath}}" mode="aspectFill" />
          <view class="info">
            <view class="info-left">
              <view class="info-name">
                <view class="name textoverflow">{{mentorInfo.mentorBase.user.userName}}</view>
                <mentor-identity name="{{mentorInfo.mentorBase.user.identityName}}"></mentor-identity>
                <!-- <view class="label" wx:if="{{mentorInfo.mentorBase.user.identityName}}">{{mentorInfo.mentorBase.user.identityName}}</view> -->
              </view>
              <view class="mentor-appoint-star" wx:if="{{mentorInfo.mentorBase.score > 0}}">
                <block wx:for="{{mentorInfo.stars}}" wx:key="index" wx:for-item="starSrc">
                  <image src="{{starSrc}}" mode="aspectFit" class="star-icon">
                  </image>
                </block>
                <text>{{mentorInfo.mentorBase.score}}分</text>
              </view>
              <view class="company">
                <view wx:if="{{mentorInfo.mentorBase.companyName}}">{{mentorInfo.mentorBase.companyName}}</view>
                <view wx:if="{{mentorInfo.mentorBase.positionName}}">{{mentorInfo.mentorBase.positionName}}</view>
              </view>
            </view>
          </view>
        </view>
        <rich-text class="mentor-introduce {{unfoldClose ? '' : 'show-all-text'}}" nodes="{{htmlSnip}}"></rich-text>
        <rich-text class="mentor-introduce mentor-introduce-position" nodes="{{htmlSnip}}"></rich-text>
        <view class="unfold" wx:if="{{showUnfold}}" catch:tap="unfoldChange">
          <van-icon custom-class="{{unfoldClose ? 'arrow-bottom' : 'arrow-top'}}" name="arrow-down" color="#333" />
        </view>
      </view>
    </view>

    <!-- 学员列表 -->
    <view class="students-section">
      <view class="section-header">
        <view class="title">学员 ({{total}})</view>
        <view class="more" bind:tap="toAllStudents" wx:if="{{studentAvatars.length > 0}}">
          更多
          <van-icon color='#0057FF' name="arrow" />
        </view>
      </view>
      <view class="student-list" wx:if="{{studentAvatars.length > 0}}">
        <image wx:for="{{studentAvatars}}" wx:key="index" class="student-avatar" src="{{studentAvatars[index].userAvatar}}" mode="aspectFill"></image>
      </view>
      <view class="student-list-empty" wx:else>
        暂无学员
      </view>
    </view>

    <!-- 评论 -->
    <tab id="tabComponent" courseInfo="{{courseInfo}}" courseId="{{courseId}}" courseCommentScore="{{courseCommentScore}}" courseType="{{tabType}}" isEdit="{{isEdit}}"  bind:audioPlay="handleAudioPlay" bind:audioEnd="handleAudioEnd" bind:audioPause="handleAudioPause" />
  </view>
  <view class="course-bottom" slot="bottom">
    <van-button wx:if="{{isEdit}}" bind:tap="toEditCourse">编辑课程</van-button>
    <!-- item.loadStatus === 0 ? '上架' : '下架' -->
    <!-- <van-button bind:tap="toUpShelf" wx:else>{{courseInfo.loadStatus===0?'上架':'下架'}}</van-button> -->
    <van-button  wx:else bind:tap="toSave">保存</van-button>
  </view>
</scroll-box>