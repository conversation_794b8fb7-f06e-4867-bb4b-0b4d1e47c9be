/* pages/module-mentorShop/dynamics-release/dynamics-release.wxss */
page {
  height: 100%;
  box-sizing: border-box;
}
#myVideoLoop {
  position: fixed;
  left: 10000rpx;
}
.dynamics-release {
  margin: 30rpx 36rpx;
  padding-bottom: 30rpx;
  .dynamics-text {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx 30rpx 80rpx;
    position: relative;
    // .input-area {
    //   width: 100%;
    //   // min-height: 300rpx;
    // }

    .placeholder {
      color: #999;
      // font-size: 28rpx;
    }
    .van-cell {
      width: 100%;
      // min-height: 300rpx;
      // height: auto;
      padding: 0;
    }
    .textarea {
      font-size: 28rpx;
      // line-height: 1.6;
      overflow: hidden;
      word-wrap: break-word;
    }
    .word-count {
      position: absolute;
      bottom: 30rpx;
      right: 30rpx;
      // text-align: right;
      color: #999;
      font-size: 24rpx;
      margin-top: 20rpx;
    }

    .media-upload {
      margin-top: 40rpx;

      .media-list {
        display: flex;
        flex-wrap: wrap;
        // gap: 20rpx; // 间距
        justify-content: space-between;
      }
      .media-item,
      .upload-box {
        margin-top: 14rpx;
        // width: calc(33.33% - 13.33rpx); // 计算宽度，使 3 个元素完美排列
        width: 32%;
        aspect-ratio: 1; // 宽高相等
        overflow: hidden;
        position: relative;
        .mask {
          position: absolute;
          width: 100%;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          background: rgba(0, 0, 0, 0.6);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          color: #fff;
          font-size: 24rpx;
          view {
            margin-top: 16rpx;
          }
        }
         // 添加播放按钮样式
         .van-icon-play-circle-o {
          position: absolute;
          top: 50% !important;
          left: 46%;
          transform: translate(-50%, -50%);
          color: #fff;
          background-color: transparent !important;
          z-index: 1;
          border-radius: 8rpx;
        }

        video {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 8rpx;
        }
      }
      .media-empty{
        height: 0;
        width: 32%;
      }
      .media-item {
        image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .delete-btn {
          position: absolute;
          top: 0;
          right: 0;
          width: 40rpx;
          height: 40rpx;
          background: rgba(0, 0, 0, 0.5);
          color: #fff;
          text-align: center;
          line-height: 40rpx;
          font-size: 32rpx;
          border-bottom-left-radius: 8rpx;
        }
      }

      .upload-box {
        background: #ededed;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .voice-btn {
    padding: 10rpx 22rpx;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    border: 2rpx solid #c1c1c1;
    margin: 30rpx 0 24rpx;
    width: fit-content;
    margin-left: auto;
    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }

    text {
      font-size: 24rpx;
      color: #737783;
    }
  }
}
.course-bottom {
  padding: 24rpx 40rpx 30rpx;
  box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
  button {
    width: 100%;
    background: #0057ff;
    border: none;
    color: rgb(255, 255, 255);
    font-size: 32rpx;
    line-height: 32rpx;
    font-weight: 400;
    height: auto;
    padding: 30rpx 0;
    border-radius: 46rpx;
  }
}
.voice-record-content {
  padding: 48rpx 32rpx 88rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .timer {
    font-size: 64rpx;
    font-weight: 500;
    color: #333333;
    line-height: 1;
    margin-bottom: 16rpx;
  }

  .timer-tip {
    font-size: 24rpx;
    color: #c1c1c1;
    margin-bottom: 60rpx;
  }

  .mic-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .mic-content {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .mic-icon {
      width: 136rpx;
      height: 136rpx;
      margin-bottom: 16rpx;
    }
    .wave {
      width: 270rpx;
      height: 180rpx;
    }

    text {
      font-size: 24rpx;
      color: #333333;
    }
  }

  .control-buttons {
    display: flex;
    align-items: center;
    gap: 88rpx;
    margin-bottom: 48rpx;

    .control-btn {
      display: flex;
      flex-direction: column;
      align-items: center;

      image {
        width: 100rpx;
        height: 100rpx;
        margin-bottom: 16rpx;
      }

      text {
        font-size: 24rpx;
        color: #333333;
      }
    }
    .control-pause {
      image {
        width: 136rpx;
        height: 136rpx;
      }
    }
  }
}

.audio-player {
  padding: 20rpx 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24rpx;
  box-shadow: 0 0 32rpx 0 rgba(99, 97, 155, 0.1);
  .play-control {
    flex: 1;
    display: flex;
    align-items: center;
    margin-right: 24rpx;
    background: #4584ff;
    border-radius: 24rpx;
    padding: 10rpx;

    .play-btn {
      width: 56rpx;
      height: 56rpx;
      margin-right: 16rpx;
    }

    .progress-bar {
      flex: 1;
      // .custom-button {

      //   width: 26rpx;
      //   height: 26rpx;
      //   background-color: #fff;
      //   border-radius: 50%;
      // }
      .custom-button {
        width: 100rpx;
        height: 100rpx;
        background-color: transparent;
        border-radius: 50%;
        box-sizing: border-box;
        position: relative;
      }
      .custom-button::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 26rpx;
        height: 26rpx;
        background-color: #fff;
        border-radius: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .duration {
      color: #ffffff;
      font-size: 28rpx;
      flex-shrink: 0;
      margin-left: 12rpx;
    }
  }
}
.van-circle {
  margin-bottom: 8rpx;
  .van-circle__canvas {
    width: 136rpx !important;
    height: 136rpx !important;
  }
}

.van-circle-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  image {
    width: 96rpx !important;
    height: 96rpx !important;
  }
}
.video-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 1);
  z-index: 999;

  .video-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-btn {
    position: fixed;
    top: 40rpx;
    left: 40rpx;
    z-index: 1000;
    width: 60rpx;
    height: 60rpx;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
