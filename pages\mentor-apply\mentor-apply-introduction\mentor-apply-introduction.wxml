<view class="mentor-apply-introduction">
  <view class="content">
    <view class="process">
      <view class="title">审核流程</view>
      <view class="process-box">
        <view class="process-item">
          <view class="process-item-title">
            01
            <view class="icon"></view>
          </view>
          <view class="process-item-text">提交申请</view>
        </view>
        <view class="process-transition">
          <image src="/static/icon/arrow-bottom-blue-double.png" mode="aspectFill"/>
        </view>
        <view class="process-item">
          <view class="process-item-title">
            02
            <view class="icon"></view>
          </view>
          <view class="process-item-text">平台审核</view>
        </view>
        <view class="process-transition">
          <image src="/static/icon/arrow-bottom-blue-double.png" mode="aspectFill"/>
        </view>
        <view class="process-item">
          <view class="process-item-title">
            03
            <view class="icon"></view>
          </view>
          <view class="process-item-text">开始咨询</view>
        </view>
      </view>
    </view>
    <view class="required-materials">
      <view class="title">所需材料</view>
      <view class="required-materials-position">
        <view class="left">
          <view class="left-title">
            1.职位证明
          </view>
          <view class="left-value">
            请提供能体现职务的名片、工作证、职业资格证书、加盖公章的在职证明劳动合同和社保缴费证明等。
          </view>
        </view>
        <view class="right" bind:tap="preview">
          <image src="{{previewImage}}" mode=""/>
          <image class="example" src="/static/icon/example.png" mode=""/>
        </view>
      </view>
      <view class="required-materials-position">
        <view class="left">
          <view class="left-title">
            2.证件信息
          </view>
          <view class="left-value">
            请提供职务证明所对应人员的身份证号码、银行卡号以及银行卡开户行信息。
          </view>
        </view>
        <!-- <view class="right">
          <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/upload-example.png" mode=""/>
        </view> -->
      </view>
    </view>
    <view class="question">
      <view class="title">常见问题</view>
      <view class="subtitle">
        <view class="subtitle-item {{tabIndex === 0 ? 'active' : ''}}" bind:tap="tabChange" data-key="0">入驻导师标准</view>
        <view class="subtitle-item {{tabIndex === 1 ? 'active' : ''}}" bind:tap="tabChange" data-key="1">合作&结算模式</view>
      </view>
      <view class="value-item" wx:if="{{tabIndex === 0}}">
        <view class="icon"></view>
        <view class="value">
          在同一行业领域深耕，或者专注于某一特定岗位，拥有不低于5年的工作时间。
        </view>
      </view>
      <view class="value-item" wx:if="{{tabIndex === 0}}">
        <view class="icon"></view>
        <view class="value">
          拥有担任团队或部门负责人的工作履历，积累了丰富的管理实践经验。
        </view>
      </view>

      <view class="value-item" wx:if="{{tabIndex === 1}}">
        <view class="icon"></view>
        <view class="value">
          <view class="value-title">合作模式是怎样的？</view>
          <view class="value-text">
            导师需提交资料，由毕师父官方平台进行导师认证，认证成功后可提供咨询服务。
          </view>
        </view>
      </view>
      <view class="value-item" wx:if="{{tabIndex === 1}}">
        <view class="icon"></view>
        <view class="value">
          <view class="value-title">何时结算？</view>
          <view class="value-text">
            次月15号进行账单结算，由跃鱼云官方进行统一打款。
          </view>
        </view>
      </view>
    </view>
  </view>
  <view class="footer">
    <button class="login-accredit" wx:if="{{type === 'introduction'}}" bind:tap="toRegister" data-type="apply">
      准备好了，立即申请
    </button>
    <view class="login-box" wx:if="{{type === 'login'}}">
      <view class="exit" bind:tap="exit">
        <image src="/static/icon/logout.png" mode=""/>
        <text>退出</text>
      </view>
      <view class="btn-box">
        <button class="login-btn left-btn" bind:tap="toRegister" data-type="review">
          查看审核状态
        </button>
        <button class="login-btn right-btn" bind:tap="toRegister" data-type="apply">
          准备好了，立即申请
        </button>
      </view>
    </view>
  </view>
</view>