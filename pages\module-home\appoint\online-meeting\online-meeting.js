// pages/appoint/online-meeting/online-meeting.js
import { env } from "~/utils/env";
import { removeRtc } from '~/api/appoint'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    appointId: '',
    previewUrl: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      appointId: options.appointId
    })
    this.attendMeeting()
  },
  attendMeeting() {
    try {
      wx.showLoading({
        title: "加载中...",
      });

      // const { data } = await filePreview(this.data.config);
      const baseUrl = `${env.mentorDomainName}online-meeting`;
      const params = {
        appointLogId: this.data.appointId,
        token: wx.getStorageSync("token"),
        tenantId: env.Tenant_id
      };

      const queryString = Object.entries(params)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join("&");
      this.setData({
        previewUrl: `${baseUrl}?${queryString}`,
      });
      wx.hideLoading();
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || "进入会议失败",
        icon: "none",
      });
    }
  },
  removeRtc() {
    removeRtc({
      appointLogId: this.data.appointId
    }).then(res => {})
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.removeRtc()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})