.appoint-set{
  width: 100%;
	padding: 30rpx 28rpx;
	border-radius: 12rpx;
  background: #fff;
  box-shadow: 0 4rpx 32rpx 0 rgba(99, 97, 155, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;

  .appoint-set-card{
    width: 100%;
    margin-top: 28rpx;
    .set-time--title{
      color: rgb(51, 51, 51);
      font-size: 30rpx;
      font-weight: 700;
      line-height: 42rpx;
      margin-bottom: 10rpx;
    }
    .set-time-tips{
      display: flex;
      align-items: center;
      image{
        width: 24rpx;
        height: 24rpx;
        margin-top: 2rpx;
      }
      .set-time-tips-value{
        margin-left: 4rpx;
        color: rgb(233, 178, 99);
        font-size: 24rpx;
        font-weight: 500;
        line-height: 36rpx;
      }
    }

    // 时间设置模块
    .date-select{
      margin-top: 24rpx;
      padding: 30rpx 26rpx 32rpx;
      background: rgb(248, 249, 254);
      border-radius: 12rpx;
      .date-select-header{
        padding: 0 64rpx;
        width: 100%;
        display: flex;
        justify-content: space-between;
        display: flex;
        align-items: center;
        .date-select-header-change-box{
          width: 24rpx;
          height: 24rpx;
          .date-select-header-left{
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            image{
              width: 100%;
              height: 100%;
              transform: rotateZ(180deg);
            }
          }
          .date-select-header-right{
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            image{
              width: 100%;
              height: 100%;
            }
          }
        }
        .date-select-header-content{
          flex: 1;
          text-align: center;
          color: rgb(51, 51, 51);
          font-size: 28rpx;
          font-weight: 500;
          line-height: 40rpx;
          .date-week{
            margin-left: 20rpx;
          }
        }
      }
      .time-list{
        padding-top: 14rpx;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .time-item{
          position: relative;
          width: 128rpx;
          height: 62rpx;
          margin-top: 20rpx;
          background: rgb(255, 255, 255);
          color: rgb(27, 27, 27);
          box-shadow: 2rpx 2rpx 2rpx 2rpx rgb(219, 219, 219);
          border-radius: 12rpx;
          font-size: 28rpx;
          font-weight: 500;
          line-height: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          .repetition-week{
            position: absolute;
            width: 36rpx;
            height: 36rpx;
            background: #fff;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            top: 0;
            right: 0;
            transform: translateX(50%);
            .repetition-week-blue{
              border-radius: 50%;
              width: 30rpx;
              height: 30rpx;
              background: #0057FF;
              display: flex;
              justify-content: center;
              align-items: center;
              text{
                width: 100%;
                height: 100%;
                color: rgb(255, 255, 255);
                font-size: 24rpx;
                font-weight: 400;
                line-height: 24rpx;
                transform-origin: center center;
                transform: scale(.8);
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }
          }
        }
        .time-item-setting{
          color: rgb(255, 255, 255);
          background: #6b82f6;
          // box-shadow: unset;
        }
        .time-item-past{
          color: rgb(73, 73, 73);
          background: rgb(230, 232, 242);
          // box-shadow: unset;
        }
        .time-item-appoint{
            background: rgb(83, 214, 14);
            color: rgb(255, 255, 255);
            // box-shadow: unset;
        }
        .time-empty{
          width: 128rpx;
          height: 0;
        }
      }
      .legend{
        margin-top: 26rpx;
        display: flex;
        justify-content: center;
        .legend-item{
          display: flex;
          align-items: center;
          color: rgb(73, 73, 73);
          font-size: 28rpx;
          font-weight: 500;
          line-height: 40rpx;
          margin-left: 60rpx;
          .legend-circle{
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
            margin-right: 4rpx;
          }
          .past-due{
            background: rgb(230, 232, 242);
          }
          .setting{
            background: #6b82f6;
          }
          .appointed{
            background: rgb(83, 214, 14);
          }
          &:nth-child(1) {
            margin-left: 0;
          }
        }
      }
    }
    .set-appoint-type{
      margin-top: 24rpx;
      .set-appoint-type-item{
        margin-top: 20rpx;
        border: 2rpx solid #c6c6c6;
        padding: 32rpx 38rpx 32rpx 34rpx;
        border-radius: 12rpx;
        .item-title{
          width: 100%;
          justify-content: space-between;
          display: flex;
          align-items: center;
          .left{
            display: flex;
            align-items: center;
            image{
              width: 40rpx;
              height: 40rpx;
            }
            text{
              margin-left: 6rpx;
              color: rgb(154, 154, 154);
              font-size: 28rpx;
              font-weight: 400;
              line-height: 40rpx;
            }
          }
          .right{
            width: 40rpx;
            height: 40rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
        }
        .appoint-address{
          margin-top: 14rpx;
          .appoint-address-tips{
            display: flex;
            align-items: center;
            color: rgb(154, 154, 154);
            font-size: 24rpx;
            font-weight: 500;
            line-height: 30rpx;
            .appoint-address-tips-text{
              margin-left: 6rpx;
            }
          }
          .address-list{
            display: flex;
            flex-direction: column;
            .address-item{
              align-self: flex-start;
              margin-top: 20rpx;
              display: flex;
              align-items: center;
              color: rgb(51, 51, 51);
              font-size: 28rpx;
              font-weight: 400;
              line-height: 40rpx;
              .address-item-text{
                flex: 1;
                margin-right: 20rpx;
                word-break: break-all;
              }
              image{
                width: 40rpx;
                height: 40rpx;
              }
            }
          }
          .address-add-btn{
            margin-top: 18rpx;
            van-button{
              image{
                width: 20rpx;
                height: 20rpx;
                margin-right: 12rpx;
              }
            }
            button{
              padding: 0 22rpx;
              background: #6b82f6;
              border-radius: 64rpx;
              height: 60rpx;
            }
          }
        }
      }
      .set-appoint-type-item-active{
        border-color: #6b82f6;
        .item-title{
          .left{
            text{
              color: #6b82f6;
            }
          }
        }

      }
    }
    &:nth-child(1) {
      margin-top: 0;
    }
    .tip{
      font-size: 24rpx;
      padding: 24rpx 0;
      color: #9a9a9a;
    }
  }
  .no-margin{
    margin-top: 0;
  }
}
.appoint-time-popup-box{
  .appoint-time-popup{
    position: relative;
    .close-icon{
      position: absolute;
      top: 36rpx;
      right: 28rpx;
      width: 52rpx;
      height: 52rpx;
      z-index: 99;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .header{
      padding-top: 36rpx;
      color: #272727;
      font-size: 38rpx;
      font-weight: 600;
      line-height: 52rpx;
      text-align: center;
      position: relative;
    }
    .content{
      padding: 50rpx;
      padding-bottom: 0;
      max-height: 800rpx;
      overflow: auto;
      .date-set-select{
        .select-item{
          color: #333;
          font-size: 32rpx;
          font-weight: 400;
          line-height: 48rpx;
          padding: 26rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: 2rpx solid #dfdfdf;
          .icon{
            width: 34rpx;
            height: 34rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
        }
      }
      .date-appoint-type-box{
        .online-item{
          margin-top: 36rpx;
          border: 2rpx solid #c6c6c6;
          padding: 24rpx 32rpx;
          border-radius: 12rpx;
          display: flex;
          flex-direction: column;
          .title{
            color: rgb(51, 51, 51);
            font-size: 30rpx;
            font-weight: 700;
            line-height: 42rpx;
            margin-bottom: 10rpx;
          }
          .subtitle{
            color: rgb(51, 51, 51);
            font-size: 28rpx;
            font-weight: 400;
            line-height: 32rpx;
          }
        }
        .offline-item{
          margin-top: 36rpx;
          border: 2rpx solid #c6c6c6;
          padding: 24rpx 32rpx;
          border-radius: 12rpx;
          display: flex;
          flex-direction: column;
          .title{
            color: rgb(51, 51, 51);
            font-size: 30rpx;
            font-weight: 700;
            line-height: 42rpx;
            margin-bottom: 10rpx;
          }
          .address-list{
            .address-item{
              color: #333;
              font-size: 32rpx;
              font-weight: 400;
              line-height: 48rpx;
              padding: 20rpx;
              display: flex;
              align-items: center;
              justify-content: space-between;
              border-bottom: 2rpx solid #dfdfdf;
              .address{
                display: flex;
                align-items: center;
                .default{
                  margin-right: 12rpx;
                  padding: 4rpx 12rpx;
                  font-size: 28rpx;
                  font-weight: 400;
                  line-height: 40rpx;
                  color: #0057FF;
                  border-radius: 8rpx;
                  background: #e0dfdf;
                }
              }
              .icon{
                width: 34rpx;
                height: 34rpx;
                image{
                  width: 100%;
                  height: 100%;
                }
              }
            }
          }
        }
      }
    }
    .btn{
      padding: 50rpx;
      padding-top: 32rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      van-button{
        overflow: hidden;
        height: 100%;
        flex: 1;
        margin-right: 40rpx;
        button{
          font-size: 36rpx;
          // line-height: 48rpx;
          border-radius: 120rpx;
          width: 100%;
          height: 106rpx;
        }
        &:nth-last-child(1) {
          margin-right: 0;
        }
      }
      .default-btn{
        border: 1px solid #0057FF;
        background: #fff;
        color: #0057FF;
      }
      .info-btn{
        border: 1px solid #0057FF !important;
        background: #0057FF !important;
        color: rgb(255, 255, 255) !important;
      }
    }
  }
}
.set-address-popup{
  .address-popup{
    position: relative;
    .close-icon{
      position: absolute;
      top: 36rpx;
      right: 28rpx;
      width: 52rpx;
      height: 52rpx;
      z-index: 99;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .header{
      padding-top: 36rpx;
      color: #272727;
      font-size: 38rpx;
      font-weight: 600;
      line-height: 52rpx;
      text-align: center;
      position: relative;
    }
    .content{
      padding: 30rpx 50rpx;
      // padding-bottom: 0;
      max-height: 800rpx;
      overflow: auto;
      // background: red;
    }
    .btn{
      padding: 50rpx;
      padding-top: 32rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      van-button{
        overflow: hidden;
        height: 100%;
        flex: 1;
        margin-right: 40rpx;
        button{
          font-size: 36rpx;
          // line-height: 48rpx;
          border-radius: 120rpx;
          width: 100%;
          height: 106rpx;
        }
        &:nth-last-child(1) {
          margin-right: 0;
        }
      }
      .default-btn{
        border: 1px solid #0057FF;
        background: #fff;
        color: #0057FF;
      }
      .info-btn{
        border: 1px solid #0057FF !important;
        background: #0057FF !important;
        color: rgb(255, 255, 255) !important;
      }
    }
  }
}
.input-class{
  .van-cell{
    border-bottom: 2rpx solid #dfdfdf;
    // padding: 0 !important;
    padding: 30rpx 0;
  }
}
