<view class="login-selection">
  <view class="login-bg">
    <image class="bg" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/mentor-app/home.png" mode="widthFix" />
  </view>
  <view class="login-content">
    <button wx:if="{{checked}}" class="login-accredit" open-type="getPhoneNumber" bindgetphonenumber="bindgetphonenumber">
      一键登录
    </button>
    <button wx:else class="login-accredit" bind:tap="checkSelect">
      一键登录
    </button>
    <view class="login-phone" bindtap="phoneLogin">
      手机号安全登录
    </view>
    <view class="login-phone" bindtap="back">
      暂不登录
    </view>
    <view class="agreement">
      <van-checkbox
      value="{{ checked }}"
      bind:change="onChange"
      icon-size="28rpx"
      checked-color="#0C7AFF"
      >
        <view class="text-value">
          我已阅读并同意
          <text catchtap="toUserAgreement" data-page="agreement">《用户协议》</text>
          <text catchtap="toUserPrivacy" data-page="data-page">《隐私政策》</text>
        </view>
      </van-checkbox>
    </view>
    <view class="tips">
      <image src="/static/icon/eexclamation-mark.png" mode=""/>
      <view class="tips-value">仅供已与毕师父合作的导师使用</view>
    </view>
  </view>
</view>
