/**
 * 处理HTML内容，调整列表样式
 * @param {string} content HTML内容
 * @returns {string} 处理后的HTML内容
 */
export const processHtmlContent = (content) => {
  if (!content || typeof content !== "string") {
    return "";
  }
  
  let processedContent = content;
  
  // 为 ol 和 ul 添加样式
  processedContent = processedContent.replace(
    /<(ol|ul)>/g, 
    '<$1 style="list-style-position: inside;padding-left: 0; margin-left: 0;">'
  );
  
  // 为 li 添加样式
  processedContent = processedContent.replace(
    /<li>/g,
    '<li style="margin-left: 0; padding-left: 0;">'
  );

  return processedContent;
}; 