/* pages/module-mentorShop/edit-detail/edit-detail.wxss */
@import "./iconfont.wxss";
page{
  height: 100%;
  box-sizing: border-box;
}
.ql-active {
  color: #0057ff;
}

.iconfont {
  display: inline-block;
  width: 48rpx;
  height: 48rpx;
  cursor: pointer;
  font-size: 45rpx !important;
  margin-right: 32rpx;
}

.toolbar {
  box-sizing: border-box;
  padding: 16rpx 40rpx;
  border: none;
  box-shadow: inset 0px -0.5px 0px 0px #e8e8e8;
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #fff;
}
.ql-container {
  height: 100%;
  padding: 24rpx 36rpx;
  background: #fff;
  .ql-blank:before {
    font-style: normal;
    color: #919bae;
    line-height: 46rpx;
    font-size: 28rpx;
  }
}

.editor-wrapper {
  position: relative;
  padding-bottom: 50rpx;
  .char-count {
    position: absolute;
    bottom: -50rpx;
    right: 40rpx;
    font-size: 24rpx;
    color: #919bae;
    padding-bottom: 40rpx;
    .warn {
      color: red;
    }

    .save {
      color: #0057ff;
    }
  }
}

.bottm-button-bar {
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // right: 0;
  padding: 24rpx 40rpx 30rpx;
  box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
  background-color: #fff;
  display: flex;
  justify-content: flex-end;
  button {
    width: 260rpx;
    border: none;
    color: rgb(255, 255, 255);
    font-size: 32rpx;
    line-height: 32rpx;
    font-weight: 400;
    height: auto;
    padding: 18rpx 0;
    border-radius: 45rpx;
  }
}
