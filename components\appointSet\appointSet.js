// components/appointSet/appointSet.js
import { getUserBaseInfo, getMentorAppointDate, setMentorAppointDate, cancelMentorAppointDate, getAppointOfflineAddressList, postAppointOfflineAddress, deleteAppointOfflineAddress, mentorAppointTypeOpen, mentorAppointTypeClose } from '~/api/user'
import moment from 'moment'
Component({

  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    seleceAddressId: '', // 有线下预约方式的时候选择的预约地址
    // onlineObject: {
    //   icon: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/appoint-type/online-appoint.png',
    //   iconActive: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/appoint-type/online-appoint-active.png',

    //   selectIcon: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-icon.png',
    //   selectActiveIcon: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-active.png',
    //   label: '电话咨询',
    //   id: 10,
    //   active: false
    // },

    meetingOnlineObject: {
      icon: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/appoint/meetingOnlineAppoint.png',
      iconActive: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/appoint/meetingOnlineAppointActive.png',

      selectIcon: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-icon.png',
      selectActiveIcon: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-active.png',
      label: '线上咨询',
      id: 40,
      active: false
    },

    offlineObject: {
      icon: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/appoint-type/offline-appoint.png',
      iconActive: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/appoint-type/offline-appoint-active.png',

      selectIcon: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-icon.png',
      selectActiveIcon: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-active.png',
      label: '线下咨询',
      id: 20,
      active: false
    },
    appointTimeList: [],
    offsetDay: 0,
    appointDate: '',
    appointWeek: '',

    // 预约时间设置弹框
    showAppointTimeChangePopup: false,
    selectTimeChangeIndex: 0, // 选中的要操作的对象index
    selectSetId: '', // 设置事件、取消事件选中的id
    selectTimeSetList: [
      {
        label: '设置为单次时间点',
        appointConfigType: 10
      },
      {
        label: '设置为每周循环时间点',
        appointConfigType: 20
      }
    ],
    selectTimeCancelList: [
      {
        label: '取消单次时间点',
        id: 3,
        appointConfigType: 10
      },
      {
        label: '取消每周该时间点',
        id: 4,
        appointConfigType: 20
      }
    ],

    // 添加线下地址弹框
    showAddAddressPopup: false,
    addAddress: '',
    addressList: []
  },

  /**
   * 组件的方法列表
   */
  attached() {
    this.getUserBaseInfo()
    this.getAppointOfflineAddressList()
    this.mentorAppointDateChange()
  },
  methods: {
    // 删除线下地址
    deleteAddress(event) {
      const that = this
      const item = event.currentTarget.dataset.item
      wx.showModal({
        title: item.address,
        content: '确认删除地址？',
        async success (res) {
          if (res.confirm) {
            const { success } = await deleteAppointOfflineAddress({
              mentorOfflineAddressId: item.id
            })
            if (success) {
              wx.showToast({
                title: '删除成功',
                icon: 'none'
              })
              that.getAppointOfflineAddressList()
            }
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    },
    async handleAddAddress() {
      if (this.data.addAddress.trim() === '') {
        // this.$toast('请输入线下预约地址')
        wx.showToast({
          title: '请输入线下预约地址',
          icon: 'none'
        })
        return
      }
      const { success } = await postAppointOfflineAddress({
        address: this.data.addAddress,
        isDefault: 0
      })
      if (success) {
        // this.offlineObject.active = true
        // this.showAddAddressPopup = false
        this.setData({
          'offlineObject.active': true,
          showAddAddressPopup: false
        })
        this.getAppointOfflineAddressList()
        setTimeout(() => {
          // this.addAddress = ''
          this.setData({
            addAddress: ''
          })
        }, 300)
      }
    },
    appointChange(event) {
      const key = event.currentTarget.dataset.key
      const item = this.data[key]
      if (item.id === 20 && this.data.addressList.length <= 0 && !item.active) {
        // this.$toast('请先添加线下预约地址')
        wx.showToast({
          title: '请先添加线下预约地址',
          icon: 'none'
        })
        this.setData({
          showAddAddressPopup: true
        })
        // this.showAddAddressPopup = true
        return
      }
      this.setData({
        [`${key}.active`]: !this.data[key].active
      })
      // onlineObject
      if (key === 'meetingOnlineObject') {
        // 切换在线选择的状态
        // this.onlineChange()
        this.meetingOnlineChange()
      } else {
        // 切换取消选择的状态
        this.offlineChange()
      }
    },
    showAddAddressPopup() {
      this.setData({
        showAddAddressPopup: true
      })
    },
    onlineChange() {
      if (this.data.onlineObject.active) {
        mentorAppointTypeOpen({
          appointType: 10
        }).catch(() => {
          // nv.active = !nv.active
        })
      } else {
        mentorAppointTypeClose({
          appointType: 10
        }).catch(() => {
          // nv.active = !nv.active
        })
      }
    },
    meetingOnlineChange() {
      if (this.data.meetingOnlineObject.active) {
        mentorAppointTypeOpen({
          appointType: 40
        }).catch(() => {
          // nv.active = !nv.active
        })
      } else {
        mentorAppointTypeClose({
          appointType: 40
        }).catch(() => {
          // nv.active = !nv.active
        })
      }
    },
    offlineChange() {
      if (this.data.offlineObject.active) {
        mentorAppointTypeOpen({
          appointType: 20
        }).catch(() => {
          // nv.active = !nv.active
        })
      } else {
        mentorAppointTypeClose({
          appointType: 20
        }).catch(() => {
          // nv.active = !nv.active
        })
      }
    },
    // 时间确定的按钮
    async dateConfirm() {
      // 去设置
      if (this.data.appointTimeList[this.data.selectTimeChangeIndex].appointConfigStatus === 1) {
        let offlineAddress = ''
        if (this.data.seleceAddressId) {
          offlineAddress = this.data.addressList.find(item => {
            return item.id === this.data.seleceAddressId
          }).address
        }
        const { success } = await setMentorAppointDate({
          appointDateTime: this.communicationDate() + ' ' + (this.data.appointTimeList[this.data.selectTimeChangeIndex].startTime.hour < 10 ? `0${this.data.appointTimeList[this.data.selectTimeChangeIndex].startTime.hour}` : this.data.appointTimeList[this.data.selectTimeChangeIndex].startTime.hour) + ':00:00',
          appointConfigType: this.data.selectSetId,
          offlineAddress
        })
        if (success) {
          this.cancelAppointPopupValue()
          setTimeout(() => {
            this.getMentorAppointDate()
          }, 300)
        }
      }
      // 去取消
      if (this.data.appointTimeList[this.data.selectTimeChangeIndex].appointConfigStatus === 3) {
        const { success} = await cancelMentorAppointDate({
          mentorAppointConfigId: this.data.appointTimeList[this.data.selectTimeChangeIndex].id
        })
        if (success) {
          this.cancelAppointPopupValue()
          setTimeout(() => {
            this.getMentorAppointDate()
          }, 300)
        }
      }
    },
    // 设置弹框中的地址切换
    addressSetSelectChange(event) {
      this.setData({
        seleceAddressId: event.currentTarget.dataset.item.id
      })
    },
    dateSetSelectChange(event) {
      this.setData({
        selectSetId: event.currentTarget.dataset.item.appointConfigType
      })
    },
    cancelAppointPopupValue() {
      this.setData({
        showAppointTimeChangePopup: false
      })
      setTimeout(() => {
        this.setData({
          selectTimeChangeIndex: 0,
          selectSetId: '',
          seleceAddressId: '',
        })
      }, 300)
    },
    cancelAddressPopupValue() {
      this.setData({
        showAddAddressPopup: false
      })
    },
    // 展示时间设置弹框
    appointTimeChange(event) {
      const item = event.currentTarget.dataset.item
      const index = event.currentTarget.dataset.index
      if (item.appointConfigStatus === 2) {
        // this.$toast('该时间点已过期')
        wx.showToast({
          title: '该时间点已过期',
          icon: 'none'
        })
        return
      }
      if (item.appointConfigStatus === 4) {
        // this.$toast('该时间点已被预约')
        wx.showToast({
          title: '该时间点已被预约',
          icon: 'none'
        })
        return
      }
      // 如果是取消预约时间点，那么根据用户当前时间点的设置，直接给他默认选中select
      if (item.appointConfigStatus === 3) {
        // 3是去取消
        const selectSetId = this.data.selectTimeCancelList.filter(data => data.appointConfigType === item.appointConfigType)[0].appointConfigType
        this.setData({
          selectSetId
        })
      }
      if (item.appointConfigStatus === 1) {
        // 1是去设置
        const selectSetId = this.data.selectTimeSetList[0].appointConfigType
        this.setData({
          selectSetId
        })
      }
      // this.selectTimeChangeIndex = index
      this.setData({
        selectTimeChangeIndex: index
      })
      if (this.data.addressList && this.data.addressList.length > 0) {
        const seleceAddressId = this.data.addressList[0].id
        this.setData({
          seleceAddressId
        })
      }
      this.setData({
        showAppointTimeChangePopup: true
      })
    },
    // 日期改变时更新日期表的header
    mentorAppointDateChange() {
      const weekObject = {
        1: '星期一',
        2: '星期二',
        3: '星期三',
        4: '星期四',
        5: '星期五',
        6: '星期六',
        0: '星期日'
      }
      const appointDate = moment().add(this.data.offsetDay, 'day').format('YYYY年MM月DD')
      const appointWeek = weekObject[moment().add(this.data.offsetDay, 'day').format('d')]
      this.setData({
        appointDate,
        appointWeek
      })
      this.getMentorAppointDate()
    },
    // 日期减少一天
    offersetDayDecrease() {
      const offsetDay = this.data.offsetDay
      this.setData({
        offsetDay: offsetDay - 1
      })
      this.mentorAppointDateChange()
    },
    // 日期增加一天
    offersetDayAugment() {
      const offsetDay = this.data.offsetDay
      this.setData({
        offsetDay: offsetDay + 1
      })
      this.mentorAppointDateChange()
    },
    // 这个是选中当天的时间，格式为YYYY-MM-DD，这个其实跟appointDate是一样的，只是格式不同而已，这个用来接口传参
    communicationDate() {
      return moment().add(this.data.offsetDay, 'day').format('YYYY-MM-DD')
    },
    async getUserBaseInfo() {
      const { data } = await getUserBaseInfo()
        // if (data.onlineAppointStatus === 1) {
        //   this.setData({
        //     'onlineObject.active': true
        //   })
        // } else {
        //   this.setData({
        //     'onlineObject.active': false
        //   })
        // }

        if (data.offlineAppointStatus === 1) {
          this.setData({
            'offlineObject.active': true
          })
        } else {
          this.setData({
            'offlineObject.active': false
          })
        }

        if (data.onlineMeetingAppointStatus === 1) {
          this.setData({
            'meetingOnlineObject.active': true
          })
        } else {
          this.setData({
            'meetingOnlineObject.active': false
          })
        }
    },
    // 获取导师的线下地址列表
    async getAppointOfflineAddressList() {
      const { data } = await getAppointOfflineAddressList()
      // this.addressList = data || []
      this.setData({
        addressList: data || []
      })
      if (this.data.addressList.length <= 0) {
        this.setData({
          'offlineObject.active': false
        })
      }
    },
    // 获取当前日期的可预约时间列表
    async getMentorAppointDate() {
      const date = this.communicationDate()
      const { data } = await getMentorAppointDate({
        queryDate: date
      })
      this.setData({
        appointTimeList: data
      })
    },
  }
})