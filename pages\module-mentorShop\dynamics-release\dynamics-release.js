// pages/module-mentorShop/dynamics-release/dynamics-release.js
import { addDynamic } from '~/api/mentorShop';
import { webUpload } from '~/api/webUpload';
// import moment from 'moment'
import { navigateAndAddPage } from '~/utils/pageNavigation';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    autosize: {
      // maxHeight: 1000,
      minHeight: 120,
    },
    dynamicContent: '',
    currentLength: 0,
    dynamicImages: [],
    dynamicVideo: '',  // 视频路径（如果有）
    mediaType: '',     // 当前上传的媒体类型：'image' | 'video' | ''
    isUploading: false, // 新增上传中状态
    videoDuration: 0, //视频时长
    showPreviewVideo: false,
    show: false,
    isRecording: false,
    isPlaying: false,
    timer: null,
    recordTip: "录音时长不得超过10min",
    progressStyle: "",
    currentAudio: null,
    audioPlayObject: {
      audioProgress: 0,
      audio: null,
      audioPlayStatus: false,
      audioSrc: '',
      audioDuration: ''
    },
    tempAudioObject: {
      audioSrc: '',
      audioDuration: ''
    },
    audioContext: null,
    submitDisabled: true,
    value: 0,
    circleSize:69,
    videoContext: null // 添加视频上下文
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化录音管理器
    this.recorder();
    this.setData({
      courseId: options.id
    })

    // 获取设备宽度
    const windowWidth = wx.getWindowInfo().windowWidth;
    const circleSize = Math.round((windowWidth / 375) * 66);

    this.setData({
      circleSize: circleSize,
    });

    // 创建视频上下文
    this.setData({
      videoContext: wx.createVideoContext('evaluateVideo')
    });
  },
  previewImage(e) {
    wx.previewImage({
      current: e.currentTarget.dataset.img, // 当前显示图片的http链接
      urls: this.data.dynamicImages // 需要预览的图片http链接列表
    })
  },
  onUnload() {
    // 页面卸载时清理
    this.clearTimer();
    
    // 确保停止录音
    if (this.recorderManager) {
      try {
        if (this.data.isRecording) {
          this.recorderManager.stop();
        }
        // 不要设置为null，避免下次使用时出错
      } catch (e) {
        console.error('停止录音失败:', e);
      }
    }
    
    // 停止并销毁音频实例
    if (this.data.currentAudio) {
      try {
        this.data.currentAudio.stop();
        this.data.currentAudio.destroy();
      } catch (e) {
        console.error('销毁音频实例失败:', e);
      }
    }
    
    // 销毁音频上下文
    if (this.data.audioContext) {
      try {
        this.data.audioContext.destroy();
      } catch (e) {
        console.error('销毁音频上下文失败:', e);
      }
    }
  },
  recorder() {
    // 先尝试销毁之前可能存在的实例
    if (this.recorderManager) {
      try {
        this.recorderManager.stop();
      } catch (e) {
        console.error('停止旧录音实例失败:', e);
      }
    }
    
    this.recorderManager = wx.getRecorderManager();

    // 监听录音开始事件
    this.recorderManager.onStart(() => {
      console.log("录音开始");
      this.setData({
        isRecording: true,
        recordTip: "最长时长不超过10分钟",
        'tempAudioObject.audioSrc': "",
        'tempAudioObject.audioDuration': "00:00"
      });
      this.startTimer();

      // 开始录音时播放视频
      if (this.data.videoContext) {
        this.data.videoContext.play();
      }
    });

    // 监听录音结束事件
    this.recorderManager.onStop((res) => {
      this.clearTimer();
      this.setData({
        isRecording: false
      });
      
      // 结束录音时暂停视频
      if (this.data.videoContext) {
        this.data.videoContext.pause();
      }

      if (res.tempFilePath) {
        const audioContext = wx.createInnerAudioContext();
        audioContext.onCanplay(() => {
          // 先保存到临时对象中
          this.setData({
            'tempAudioObject.audioSrc': res.tempFilePath,
            recordTip: "录制完成" // 确保提示文本正确
          });
          audioContext.destroy();
        });
        audioContext.src = res.tempFilePath;
      } else {
        // 处理没有录音文件的情况
        this.setData({
          recordTip: "录音失败，请重试"
        });
      }
    });

    // 监听录音错误事件
    this.recorderManager.onError((res) => {
      console.error("录音错误：", res);
      wx.showToast({
        title: "录音失败",
        icon: "none",
      });
      this.clearTimer();
      this.setData({
        isRecording: false,
        'tempAudioObject.audioSrc': ""
      });
    });
    const audioContext = wx.createInnerAudioContext();
    audioContext.src = this.data.audioPlayObject.audioSrc;

    // 添加音频事件监听
    audioContext.onPlay(() => {
      this.audioPlay();
    });

    audioContext.onPause(() => {
      this.audioPause();
    });

    audioContext.onEnded(() => {
      this.audioEnded();
    });

    audioContext.onTimeUpdate(() => {
      if (!audioContext.duration) return;
      const progress = (audioContext.currentTime / audioContext.duration) * 100;
      this.setData({
        'audioPlayObject.audioProgress': progress
      });
    });

    this.setData({
      audioContext
    });
  },

  clearTimer() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
      this.setData({
        timer: null
      });
    }
  },

  startTimer() {
    let startTime = Date.now();
    this.data.timer = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const minutes = Math.floor(elapsed / 60000);
      const seconds = Math.floor((elapsed % 60000) / 1000);
      const formattedTime = `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

      this.setData({
        'tempAudioObject.audioDuration': formattedTime,
        'audioPlayObject.audioDuration': formattedTime,
        progressStyle: `width: ${(elapsed / 600000) * 100}%`,
      });

      // 10分钟后自动停止
      if (elapsed >= 600000) {
        this.stopRecord();
        // 确保UI状态正确更新
        this.setData({
          isRecording: false,
          recordTip: "录制完成，已达到最大时长"
        });
      }
    }, 10);
  },

  toggleRecord() {
    if (this.data.isRecording) {
      // 检查录音时长是否大于1秒
      const timeStr = this.data.tempAudioObject.audioDuration;
      const [minutes, seconds] = timeStr.split(':').map(Number);
      const totalSeconds = minutes * 60 + seconds;
      
      if (totalSeconds < 1) {
        wx.showToast({
          title: '语音时长需大于1秒钟',
          icon: 'none'
        });
        return;
      }
      this.stopRecord();
    } else {
      this.startRecord();
    }
  },

  startRecord() {
    if (!this.recorderManager) {
      console.error('录音管理器未初始化');
      this.recorderManager = wx.getRecorderManager();
      this.recorder(); // 重新初始化所有监听器
    }

    const recorderOptions = {
      duration: 600000,
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 48000,
      format: "mp3",
      frameSize: 50,
    };

    // 检查麦克风是否被占用
    wx.getAvailableAudioSources({
      success: (res) => {
        // 先检查是否已经有录音权限
        wx.getSetting({
          success: (res) => {
            if (res.authSetting['scope.record']) {
              // 已经有权限，直接开始录音
              try {
                this.recorderManager.start(recorderOptions);
              } catch (error) {
                console.error('开始录音失败:', error);
                wx.showToast({
                  title: '录音启动失败，请重试',
                  icon: 'none'
                });
                // 重置录音状态
                this.setData({
                  isRecording: false
                });
              }
            } else {
              // 没有权限，请求权限
              wx.authorize({
                scope: "scope.record",
                success: () => {
                  try {
                    this.recorderManager.start(recorderOptions);
                  } catch (error) {
                    console.error('开始录音失败:', error);
                    wx.showToast({
                      title: '录音启动失败',
                      icon: 'none'
                    });
                  }
                },
                fail: (err) => {
                  console.log('录音权限获取失败', err);
                  wx.showModal({
                    title: "提示",
                    content: "需要您的录音权限",
                    success: (res) => {
                      if (res.confirm) {
                        wx.openSetting();
                      }
                    },
                  });
                },
              });
            }
          },
          fail: (err) => {
            console.error('获取设置失败:', err);
            wx.showToast({
              title: '无法获取录音权限状态',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        console.error('获取可用音频源失败:', err);
        wx.showToast({
          title: '麦克风可能被占用',
          icon: 'none'
        });
      }
    });
  },

  stopRecord() {
    if (this.data.isRecording) {
      this.recorderManager.stop();
      this.clearTimer();
      this.setData({
        isRecording: false,
        recordTip: "录制完成"
      });
    }
  },

  reRecord() {
    if (this.data.isPlaying) {
      this.stopPlay();
    }
    this.setData({
      'tempAudioObject.audioSrc': "",
      'tempAudioObject.audioDuration': "00:00"
    });
  },

  togglePlay() {
    if (this.data.isPlaying) {
      this.pausePlay();
    } else {
      this.startPlay();
    }
  },

  startPlay() {
    const audioSrc = this.data.tempAudioObject.audioSrc;
    if (!audioSrc) return;

    const audioContext = wx.createInnerAudioContext();
    audioContext.src = audioSrc;

    audioContext.onPlay(() => {
      this.setData({
        isPlaying: true,
        value: 0
      });
    });

    // 添加时间更新事件监听
    audioContext.onTimeUpdate(() => {
      const currentTime = audioContext.currentTime;
      const duration = audioContext.duration;
      const progress = (currentTime / duration) * 100;

      this.setData({
        value: progress,
        'audioPlayObject.audioProgress': progress,
        progressStyle: `width: ${progress}%`
      });
    });

    audioContext.onEnded(() => {
      this.setData({
        isPlaying: false,
        progressStyle: "",
        'audioPlayObject.audioProgress': 0
      });
    });

    audioContext.play();
    this.setData({
      currentAudio: audioContext
    });
  },

  pausePlay() {
    if (this.data.currentAudio) {
      this.data.currentAudio.pause();
      // // 暂停时保持当前时间显示
      // const currentTime = this.data.currentAudio.currentTime;
      // const minutes = Math.floor(currentTime / 60);
      // const seconds = Math.floor(currentTime % 60);
      // const milliseconds = Math.floor((currentTime % 1) * 100);

      // const formattedTime = `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}:${milliseconds.toString().padStart(2, "0")}`;

      this.setData({
        isPlaying: false
      });
    }
  },

  stopPlay() {
    if (this.data.currentAudio) {
      this.data.currentAudio.stop();
      this.setData({
        isPlaying: false,
        progressStyle: "",
      });
    }
  },

  async saveRecord() {
    if (!this.data.tempAudioObject.audioSrc) return;

    // 如果正在播放，先停止播放
    if (this.data.isPlaying) {
      this.stopPlay();
    }

    // 如果还有音频实例，销毁它
    if (this.data.currentAudio) {
      this.data.currentAudio.destroy();
    }

    // 销毁旧的 audioContext
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }

    // 创建新的 audioContext
    const audioContext = wx.createInnerAudioContext();

    // 设置音频源
    audioContext.src = this.data.tempAudioObject.audioSrc;

    // 添加音频事件监听
    audioContext.onPlay(() => {
      this.audioPlay();
    });

    audioContext.onPause(() => {
      this.audioPause();
    });

    audioContext.onEnded(() => {
      this.audioEnded();
    });

    audioContext.onTimeUpdate(() => {
      if (!audioContext.duration) return;
      const progress = (audioContext.currentTime / audioContext.duration) * 100;
      this.setData({
        'audioPlayObject.audioProgress': progress
      });
    });

    this.setData({
      show: false,
      isPlaying: false,
      currentAudio: null,
      progressStyle: "",
      'audioPlayObject.audioSrc': this.data.tempAudioObject.audioSrc,
      'audioPlayObject.audioDuration': this.data.tempAudioObject.audioDuration,
      'audioPlayObject.audioProgress': 0,
      'audioPlayObject.audioPlayStatus': false,
      audioContext
    });

    const { url } = await webUpload(this.data.tempAudioObject.audioSrc, 'student');
    this.setData({
      'audioPlayObject.audioSrc': url,
    });
  },

  deleteAudio() {
    wx.showModal({
      title: '提示',
      content: '确定要删除这条录音吗？',
      success: (res) => {
        if (res.confirm) {
          // 停止并销毁当前音频实例
          if (this.data.currentAudio) {
            this.data.currentAudio.stop();
            this.data.currentAudio.destroy();
            this.setData({
              currentAudio: null
            });
          }

          // 停止并销毁音频上下文
          if (this.data.audioContext) {
            this.data.audioContext.stop();
            this.data.audioContext.destroy();
            this.setData({
              audioContext: null
            });
          }

          // 重置所有音频相关状态
          this.setData({
            'audioPlayObject.audioSrc': '',
            'audioPlayObject.audioDuration': '00:00',
            'audioPlayObject.audioProgress': 0,
            'audioPlayObject.audioPlayStatus': false,
            isPlaying: false,
            progressStyle: ""
          });
        }
      }
    });
  },

  onVoiceEvaluate() {
    // 打开弹窗前先重置所有状态
    this.resetAllStates();
    this.setData({
      show: true
    });
  },

  onCloseVoice() {
    const isRecording = this.data.isRecording;

    if (isRecording) {
      wx.showModal({
        title: '提示',
        content: '正在录音中，确定要退出吗？',
        success: (res) => {
          if (res.confirm) {
            this.recorderManager.stop();
            this.clearTimer();
            this.resetAllStates();
          }
        }
      });
      return;
    }

    if (this.data.isPlaying) {
      this.stopPlay();
    }

    if (this.data.currentAudio) {
      this.data.currentAudio.destroy();
    }

    this.resetAllStates();
  },

  resetAllStates() {
    this.clearTimer();
    this.setData({
      show: false,
      progressStyle: "",
      recordTip: "录音时长不得超过10min",
      isRecording: false,
      isPlaying: false,
      currentAudio: null,
      'tempAudioObject.audioSrc': "",
      'tempAudioObject.audioDuration': "00:00" // 修改为分秒格式
    });
  },

  // 音频进度条拖拽
  // onSliderTouch(e) {
  //   const touchX = e.touches[0].clientX;
  //   const query = wx.createSelectorQuery().in(this);
  //   query.select('.progress-bar').boundingClientRect(rect => {
  //     const { audioContext } = this.data;
  //     if (!audioContext || !audioContext.duration) return;

  //     const barLeft = rect.left;
  //     const barWidth = rect.width;

  //     const relativeX = touchX - barLeft;
  //     let percent = relativeX / barWidth;
  //     percent = Math.max(0, Math.min(1, percent)); // 限制在 0-1 之间

  //     const newTime = percent * audioContext.duration;
  //     audioContext.seek(newTime);

  //     this.setData({
  //       'audioPlayObject.audioProgress': percent * 100
  //     });
  //   }).exec();
  // },

  // 音频进度条改变
  audioChange(e) {
    const value = e.detail;
    if (!this.data.audioPlayObject.audioSrc) return;

    const { audioContext } = this.data;
    if (!audioContext) return;

    const duration = audioContext.duration;
    const currentTime = (value / 100) * duration;

    // 先暂停音频
    audioContext.pause();

    // 设置新的播放位置
    audioContext.seek(currentTime);

    // 重新开始播放
    audioContext.play();

    // 强制更新一次进度
    this.setData({
      'audioPlayObject.audioProgress': value
    });

    // 确保 timeupdate 事件能正确触发
    audioContext.onTimeUpdate(() => {
      if (!audioContext.duration) return;
      const progress = (audioContext.currentTime / audioContext.duration) * 100;
      this.setData({
        'audioPlayObject.audioProgress': progress
      });
    });
  },
  // 点击播放/暂停按钮
  recordClick() {
    const { audioPlayObject } = this.data;

    // 如果 audioContext 不存在，重新创建它
    if (!this.data.audioContext) {
      const audioContext = wx.createInnerAudioContext();
      audioContext.src = audioPlayObject.audioSrc;

      // 添加音频事件监听
      audioContext.onPlay(() => {
        this.audioPlay();
      });

      audioContext.onPause(() => {
        this.audioPause();
      });

      audioContext.onEnded(() => {
        this.audioEnded();
      });

      audioContext.onTimeUpdate(() => {
        if (!audioContext.duration) return;
        const progress = (audioContext.currentTime / audioContext.duration) * 100;
        this.setData({
          'audioPlayObject.audioProgress': progress
        });
      });

      this.setData({
        audioContext
      });
    }

    const { audioContext } = this.data;

    if (!audioPlayObject.audioPlayStatus) {
      // 先设置 src（如果不是每次都设置，可以省略）
      audioContext.src = audioPlayObject.audioSrc;

      // 监听加载完成
      audioContext.onCanplay(() => {
        audioContext.play();
        this.setData({
          'audioPlayObject.audioPlayStatus': true
        });
      });

      // 监听错误
      audioContext.onError((err) => {
        console.error("播放失败:", err.errMsg);
        // wx.showToast({
        //   title: "播放失败",
        //   icon: "none"
        // });
      });

      // 如果已经加载过（可选）
      setTimeout(() => {
        // 有些情况下 onCanplay 不触发，可以加一个延时兜底触发 play
        if (!audioPlayObject.audioPlayStatus) {
          audioContext.play();
          this.setData({
            'audioPlayObject.audioPlayStatus': true
          });
        }
      }, 300);
    } else {
      audioContext.pause();
      this.setData({
        'audioPlayObject.audioPlayStatus': false
      });
    }
  },

  // 音频播放进度更新
  audioProgress(e) {
    const audio = e.detail;
    if (!audio.duration) return;

    const progress = (audio.currentTime / audio.duration) * 100;
    this.setData({
      'audioPlayObject.audioProgress': progress,
      audioDuration: audio.duration // 保存音频总时长
    });
  },

  // 音频播放结束
  audioEnded() {
    this.setData({
      'audioPlayObject.audioProgress': 0,
      'audioPlayObject.audioPlayStatus': false
    });
  },

  // 音频暂停
  audioPause() {
    this.setData({
      'audioPlayObject.audioPlayStatus': false
    });
  },

  // 音频开始播放
  audioPlay() {
    this.setData({
      'audioPlayObject.audioPlayStatus': true
    });
  },
  handleInput(e) {
    const value = e.detail;
    this.setData({
      dynamicContent: value,
      currentLength: value.length
    }, () => {
      this.checkSubmitStatus();
    });
  },

  chooseMedia() {
    const { mediaType, dynamicImages, dynamicVideo } = this.data;

    if (mediaType === 'video' && dynamicVideo) {
      wx.showToast({ title: '只能上传一个视频', icon: 'none' });
      return;
    }

    const remainCount = 9 - dynamicImages.length;

    wx.chooseMedia({
      count: mediaType === 'image' ? remainCount : 1,
      mediaType: ['image', 'video'],
      sourceType: ['album', 'camera'],
      sizeType: ['compressed'],
      compressed: true,
      success: async (res) => {
        const tempFiles = res.tempFiles;
        const selectedType = tempFiles[0].fileType;

        // 设置上传中状态
        this.setData({
          isUploading: true,
        });

        // 首次选择媒体类型
        if (!mediaType) {
          this.setData({ mediaType: selectedType });
        } else if (mediaType !== selectedType) {
          wx.showToast({
            title: '图片和视频不能同时上传',
            icon: 'none'
          });
          this.setData({ isUploading: false });
          return;
        }

        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        try {
          if (selectedType === 'image') {
            const uploadedUrls = [];
            for (const file of tempFiles) {
              const { url } = await webUpload(file.tempFilePath, 'mentor', true);
              uploadedUrls.push(url);
            }
            this.setData({
              dynamicImages: [...dynamicImages, ...uploadedUrls]
            }, this.checkSubmitStatus);
          } else if (selectedType === 'video') {
            if (tempFiles.length > 1) {
              wx.showToast({ title: '只能上传一个视频', icon: 'none' });
              return;
            }
            const size = tempFiles[0].size;
  
            // 检查视频大小
            if (size >  500 * 1024 * 1024) {
              this.setData({ isUploading: false })
              wx.showToast({
                title: '视频大小不能超过500M',
                icon: 'none'
              })
              return
            }
            wx.getVideoInfo({
              src: tempFiles[0].tempFilePath,
              success: async (info) => {
                const videoDuration = Math.floor(info.duration);
                const { url } = await webUpload(tempFiles[0].tempFilePath, 'mentor', true);
      
                this.setData({
                  dynamicVideo: url,
                  videoDuration
                }, this.checkSubmitStatus);
              },
              fail: async (err) => {
                const videoDuration = Math.floor(tempFiles[0].duration);
                const { url } = await webUpload(tempFiles[0].tempFilePath, 'mentor', true);
      
                this.setData({
                  dynamicVideo: url,
                  videoDuration
                }, this.checkSubmitStatus);
              }
            });
          }
        } catch (error) {
          console.error('上传失败:', error);
          wx.showToast({
            title: '上传失败，请重试',
            icon: 'none'
          });
        } finally {
          this.setData({ isUploading: false });
          wx.hideLoading();
        }
      }
    });
  },
  previewVideo() {
    // 暂停缩略图视频
    const myVideoContext = wx.createVideoContext('myVideo');
    myVideoContext.pause();
    myVideoContext.seek(0);

    this.setData({
      showPreviewVideo: true
    }, () => {
      // 播放预览视频
      const previewVideoContext = wx.createVideoContext('previewVideo');
      previewVideoContext.play();
    });
  },
  closePreview() {
    // 控制预览视频
    const previewVideoContext = wx.createVideoContext('previewVideo');
    previewVideoContext.pause();
    previewVideoContext.seek(0);

    // 控制缩略图视频
    const myVideoContext = wx.createVideoContext('myVideo');
    myVideoContext.pause();
    myVideoContext.seek(0);

    this.setData({
      showPreviewVideo: false
    });
  },

  deleteMedia(e) {
    const index = e.currentTarget.dataset.index;
    const dynamicImages = [...this.data.dynamicImages];
    dynamicImages.splice(index, 1);
    this.setData({
      dynamicImages
    }, () => {
      this.checkSubmitStatus();
    });
  },
  deleteVideo() {
    this.setData({
      dynamicVideo: '',
      mediaType: ''
    });
  },
  checkSubmitStatus() {
    const hasContent = this.data.dynamicContent.trim().length > 0;
    const hasMedia = this.data.dynamicImages.length > 0;
    const hasAudio = !!this.data.audioPlayObject.audioSrc;
    const hasVideo = !! this.data.dynamicVideo;

    this.setData({
      submitDisabled: !(hasContent || hasMedia || hasAudio || hasVideo)
    });
  },

  async submitDynamic() {

    if(this.data.submitDisabled){
      return
    }

    const { dynamicContent, dynamicImages, audioPlayObject, dynamicVideo, videoDuration } = this.data;

    // 如果有录音，检查时长是否达到最短要求
    if (audioPlayObject.audioSrc && audioPlayObject.audioDuration) {
      const duration = convertTimeToSeconds(audioPlayObject.audioDuration);
      if (duration < 1) {
        wx.showToast({
          title: "录音时长最短需要1秒",
          icon: "none"
        });
        return;
      }
    }

    const params = {
      dynamicContent,
      dynamicImages,
      dynamicVideo,
      videoDuration,
      dynamicAudio: audioPlayObject.audioSrc,
      audioDuration: convertTimeToSeconds(audioPlayObject.audioDuration)
    };
    const { success } = await addDynamic(params);
    if (success) {
      wx.showModal({
        title: '发表成功！预计3个工作日内审核结束。',
        showCancel: false,
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            navigateAndAddPage('/pages/module-home/home/<USER>', { active: 2 }, 'reLaunch');
          }
        }
      });
      // wx.reLaunch({
      //   url: `/pages/module-home/home/<USER>
      // })
    }
  }
})

// 添加转换时间的辅助函数
function convertTimeToSeconds(timeStr) {
  if (!timeStr) return 0;

  const parts = timeStr.split(':');
  
  // 处理分:秒格式
  if (parts.length === 2) {
    const minutes = parseInt(parts[0], 10);
    const seconds = parseInt(parts[1], 10);
    return minutes * 60 + seconds;
  }

  return 0;
}