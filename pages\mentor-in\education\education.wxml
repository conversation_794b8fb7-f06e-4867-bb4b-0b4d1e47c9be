<mentorInBox sliderValue="{{ 28 }}" back="{{ !hideBack }}" bind:pageNext="pageNext" nextBtnValue="{{ true ? '下一步' : '稍后填写' }}">
  <view class="education-list">
    <view class="experience-box">
      <view class="experience-item" wx:for="{{ educationList }}" wx:key="{{ index }}" bind:tap="editExp" data-id="{{ item.id }}">
        <view class="experience-item-header">
          <text class="experience-item-header-left textoverflow">{{item.universityName}}</text>
          <view class="experience-item-header-edit">
            <text>编辑</text>
            <image src="/static/icon/arrow-right-gray.png" mode="aspectFill"/>
            <!-- <svg-icon name="arrow-right" size="8" color="rgb(119, 119, 119)"></svg-icon> -->
          </view>
        </view>
        <view class="experience-item-center">
          <text class="textoverflow">{{item.majorName}}</text>
          <text class="textoverflow">{{item.educationLevelText}}</text>
        </view>
        <view class="experience-item-date">
          {{item.startDate}}-{{item.endDate}}
        </view>
      </view>
      <view class="experience-add" bind:tap="editExp">
        <view class="experience-add-left">
          <view class="add-name">
            <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/mentor-in/mentor-in-education.png" mode="aspectFill"/>
            <text>教育经历</text>
          </view>
          <view class="add-tips">完善您的教育经历</view>
        </view>
        <view class="experience-add-right">
          <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/add-icon.png"  mode="aspectFill"/>
        </view>
      </view>
    </view>
  </view>
</mentorInBox>