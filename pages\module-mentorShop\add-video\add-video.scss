page {
  background: #f8f8f8;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
    display: none;
  }
}

.video-box {
  height: 100vh;
  overflow-y: auto;
  padding: 30rpx;
  padding-bottom: 130rpx;

  .section-title,
  .van-cell__title {
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 24rpx;

    text {
      color: #ff4040;
    }
  }

  // 专栏名称部分
  .section {
    background: #ffffff;
    border-radius: 12rpx;
    position: relative;
    margin-bottom: 20rpx;
    padding: 30rpx;
    padding-bottom: 10rpx;
    box-shadow: 0 0px 32px rgba(99, 97, 155, 0.1);

    .name-section {
      margin-top: 24rpx;
    }

    .van-cell--clickable {
      padding: 0;
      position: static;
      padding: 24rpx 0;
      border-top: 1rpx solid #e7ecef;

      .van-cell__title {
        margin-bottom: 0;
      }
    }
  }

  .section-input,
  .sale-price {
    position: relative;

    .van-cell {
      background-color: #f8f9fe;
      border-radius: 12rpx;
      margin-bottom: 28rpx;
      position: static;
    }

    .van-cell:after {
      border-bottom: 0;
    }

    .van-field__word-limit {
      position: absolute;
      bottom: 10rpx;
      right: 20rpx;
    }
  }

  // 封面上传部分
  .cover-section {
    padding-bottom: 24rpx;
    margin-bottom: 24rpx;
    border-bottom: 1rpx solid #e7ecef;

    .section-title {
      margin-bottom: 6rpx;
    }

    .upload-tip {
      font-size: 18rpx;
      color: #9ea3ac;
      margin-bottom: 22rpx;
    }

    .upload-area {
      width: 321rpx;
      height: 180rpx;
      position: relative;

      .upload-btn {
        width: 178rpx;
        height: 178rpx;
        border: 2rpx dashed #9ea3ac;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .upload-image {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: 8rpx;

        .van-icon {
          width: 40rpx;
          height: 40rpx;
          text-align: center;
          line-height: 40rpx;
          background-color: rgba(0, 0, 0, 0.4);
          position: absolute;
          top: 0;
          right: 0;
          z-index: 9;
        }

        // 添加播放按钮样式
        .van-icon-play-circle-o {
          position: absolute;
          top: 50% !important;
          left: 46%;
          transform: translate(-50%, -50%);
          color: #fff;
          background-color: transparent !important;
          z-index: 1;
          border-radius: 8rpx;
        }

        video {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 8rpx;
        }

        image {
          width: 100%;
          height: 100%;
          border-radius: 8rpx;
        }
      }

      .mask {
        position: absolute;
        width: 178rpx;
        height: 178rpx;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: #fff;
        font-size: 24rpx;
        view {
          margin-top: 16rpx;
        }
      }
    }
  }

  // 售卖方式部分
  .sale-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 0px 32px rgba(99, 97, 155, 0.1);

    .sale-type {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      text {
        font-size: 28rpx;
        color: #333333;
        font-weight: bold;
      }

      .title,
      .relevance {
        display: flex;
        align-items: center;

        .van-icon {
          margin-left: 10rpx;
        }
      }

      .relevance {
        color: #333333;
        font-size: 28rpx;
      }
    }

    .sale-sell {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      margin-bottom: 20rpx;
      border-bottom: 1rpx solid #e7ecef;
      padding-bottom: 24rpx;

      text {
        color: #aaaaaa;
      }

      .price-tag {
        text {
          color: #333333;
          margin-right: 18rpx;
        }

        .arrow-right {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }

  }
}

.sale-other {
  .sale-type {
    padding: 24rpx 0;
    margin-bottom: 0 !important;
    border-bottom: 1rpx solid#E7ECEF;
  }

  .sale-type:first-child {
    padding-top: 0;
  }

  .sale-type:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.bottm-button-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 40rpx 30rpx;
  box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
  background-color: #fff;
  z-index: 2;

  button {
    width: 100%;
    background: #0057ff;
    border: none;
    color: rgb(255, 255, 255);
    font-size: 32rpx;
    line-height: 32rpx;
    font-weight: 400;
    height: auto;
    padding: 30rpx 0;
    border-radius: 46rpx;
  }
}

.video-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 1);
  z-index: 999;

  .video-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-btn {
    position: fixed;
    top: 40rpx;
    left: 40rpx;
    z-index: 1000;
    width: 60rpx;
    height: 60rpx;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
#addVideo{
  position: fixed;
  left: 10000rpx;
}