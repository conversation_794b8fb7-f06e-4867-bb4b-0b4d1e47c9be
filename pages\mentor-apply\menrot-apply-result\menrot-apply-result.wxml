<view class="mentor-apply-introduction">
  <view class="content">
    <view class="process">
      <view class="title">审核流程</view>
      <view class="process-box">
        <view class="process-item process-item-active">
          <view class="process-item-title">
            01
            <view class="icon"></view>
          </view>
          <view class="process-item-text">提交申请</view>
        </view>
        <view class="process-transition">
          <image wx:if="{{ true }}" src="/static/icon/arrow-bottom-blue-double.png" mode="aspectFill"/>
          <image wx:else src="/static/icon/arrow-bottom-gray-double.png" mode="aspectFill"/>
        </view>
        <view class="process-item process-item-active">
          <view class="process-item-title">
            02
            <view class="icon"></view>
          </view>
          <view class="process-item-text">等待审核确认</view>
        </view>
        <view class="process-transition">
          <image wx:if="{{status > 10}}" src="/static/icon/arrow-bottom-blue-double.png" mode="aspectFill"/>
          <image wx:else src="/static/icon/arrow-bottom-gray-double.png" mode="aspectFill"/>
        </view>
        <view class="process-item {{status > 10 ? 'process-item-active' : ''}}">
          <view class="process-item-title">
            03
            <view class="icon"></view>
          </view>
          <view class="process-item-text">审核结果</view>
        </view>
      </view>
    </view>
    <view class="process-result">
      <image class="result-img" wx:if="{{true}}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/review-wating.png" mode="widthFix"/>
      <image class="result-img" wx:if="{{false}}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/review-fail.png" mode="widthFix"/>
      <image class="result-img" wx:if="{{false}}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/review-success.png" mode="widthFix"/>

      <view wx:if="{{status === 10}}" class="wait">
        <view class="text">待认证中......</view>
        <view class="value">
          预计
          <text>{{endDate}}</text>
          显示认证结果
        </view>
      </view>
      <view wx:if="{{status === 30}}" class="fail">
        <view class="value">
          认证失败！
          <text bind:tap="showReason">查看原因</text>
        </view>
      </view>
      <view wx:if="{{status === 20}}" class="success">
        <view class="text">认证成功！</view>
        <button class="login-accredit" bind:tap="toLogin">
          前往登录
        </button>
        <!-- <view class="value">
          请前往
          <text>毕师父</text>
          小程序进行后续操作
        </view> -->
      </view>
    </view>
  </view>
</view>
