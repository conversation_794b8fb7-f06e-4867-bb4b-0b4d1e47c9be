/* pages/module-mentorShop/all-column/all-column.wxss */
page {
  background-color: #f8f8f8;
}

.video-section {
  padding: 22rpx 36rpx;
  height: 100%;
  .item-box {
    display: flex;
    align-items: center;
  }
  .filter {
    box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(99, 97, 155, 0.1);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    color: #777777;
    font-size: 24rpx;
    position: relative;
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    margin-bottom: 40rpx;
    background-color: #fff;
    border-radius: 16rpx;
    display: flex;
    justify-content: flex-end;
    .van-icon {
      margin-left: 4rpx;
      transition: transform 0.3s ease;
    }

    .filter-dropdown {
      position: absolute;
      top: calc(100% + 20rpx);
      right: 0;
      width: 120rpx;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      border-radius: 4px;
      z-index: 100;
      display: none;
      &.show {
        display: block;
      }

      .filter-item {
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 10rpx;
        font-size: 24rpx;
        color: #777777;
        margin: 13rpx;
        background-color: #f8f9fe;
        border-radius: 8rpx;
        border: 2rpx solid transparent;
        &.active {
          color: #0057ff;
          border: 2rpx solid #0057ff;
        }

        &:hover {
          background: #f5f5f5;
        }
      }
    }
  }
  .course-list {
    padding-bottom: 150rpx;
    .course-item {
      width: 100%;
      padding: 24rpx 22rpx;
      display: flex;
      background: rgb(255, 255, 255);
      margin-bottom: 24rpx;
      border-radius: 16rpx;
      .image-wrapper {
        position: relative;
        width: 236rpx;
        height: 146rpx;
        margin-right: 20rpx;
        margin-bottom: 24rpx;
        .shadow-top,
        .shadow-top-second {
          width: 80%;
          background: #e2e2e2;
          position: absolute;
          height: 10rpx;
          border-radius: 8rpx 8rpx 0 0;
          top: 0rpx;
          z-index: 12;
          left: 50%;
          transform: translateX(-50%);
          z-index: 1;
        }
        .shadow-top-second {
          width: 90%;
          top: 10rpx;
          background-color: #b1b3b7;
        }
        .course-image {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 20rpx;
          border-radius: 8rpx;
        }
        .course-stats {
          position: absolute;
          left: 0;
          right: 0;
          bottom: -20rpx;
          padding: 8rpx 16rpx 6rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 0 0 8rpx 8rpx;
          font-size: 24rpx;
          color: #fff;
          .left-stats {
            display: flex;
            align-items: center;

            .icon {
              width: 24rpx;
              height: 24rpx;
              margin-right: 6rpx;
              filter: brightness(0) invert(1);
            }
          }
        }
      }
      .course-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .course-meta {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 10rpx;
          .course-title {
            word-break: break-all;
            color: #333333;
            font-size: 26rpx;
            font-weight: bold;
            line-height: 30rpx;
            flex: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            margin-right: 10rpx;
          }
          .rating {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            text-align: right;
            .score,
            .value {
              color: #0057ff;
              font-size: 20rpx;
            }
            .score {
              color: #9ea3ac;
            }
          }
        }
        .duration,
        .student-count {
          font-size: 24rpx;
          color: #9ea3ac;
          line-height: 28rpx;
        }

        .course-bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .price-box {
            // margin-top: 16rpx;
            display: flex;
            align-items: baseline;
            font-weight: bold;
            color: #ff0019;
            .currency {
              font-size: 24rpx;
              margin-right: 2rpx;
            }

            .price {
              font-size: 40rpx;
            }
          }
          .edit {
            display: flex;
            font-size: 22rpx;
            font-weight: 500;
            color: #fff;
            view {
              padding: 4rpx 16rpx;
              border-radius: 8rpx;
            }
            .online {
              background-color: #ff0019;
            }
            .offline {
              background-color: #0057ff;
            }
            view:nth-child(2) {
              margin-left: 12rpx;
              background-color: #0057ff;
            }
          }
        }
      }
    }
  }
  .selectedItem-list {
    padding-bottom: 0;
    max-height: 60vh;
  }
  .van-popup--bottom.van-popup--safe {
    padding-bottom: 0;
  }
  .van-action-sheet {
    bottom: 154rpx;
    .empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 42rpx 0 94rpx;
      image {
        width: 236rpx;
        height: 192rpx;
        margin-bottom: 24rpx;
      }
      text {
        color: #aaaaaa;
        font-size: 34rpx;
      }
    }
    .item-box {
      border-bottom: 2rpx solid #dfdfdf;
    }
    .course-item {
      padding: 14rpx 36rpx;
      margin-bottom: 0;
      .course-number {
        color: #aaaaaa;
        font-size: 22rpx;
        margin: 12rpx 0 22rpx;
        image {
          width: 23rpx;
          height: 23rpx;
          margin-left: 6rpx;
          vertical-align: bottom;
        }
      }
      .number {
        border: 2rpx solid #0057ff;
        background-color: #f8f9fe;
        border-radius: 8rpx;
        color: #0057ff;
        font-size: 22rpx;
        line-height: 26rpx;
        padding: 4rpx 12rpx;
      }
    }
  }
  .bottm-button-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx 40rpx;
    z-index: 1;
    button {
      box-sizing: border-box;
      width: 670rpx;
      border: none;
      font-size: 32rpx;
      line-height: 32rpx;
      font-weight: 400;
      height: auto;
      padding: 30rpx 0;
      border-radius: 46rpx;
      display: flex;
      justify-content: space-around;
      margin-bottom: 24rpx;
    }
  }

  .bottm-button-bar,
  .association {
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 4rpx 0 20rpx 0 rgba(0, 0, 0, 0.05);
  }
  .vanshow-button {
    bottom: 180rpx;
    background: transparent;
    box-shadow: none;
  }
  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 8;
    // height: 194rpx;

    .selected-count {
      font-size: 30rpx;
      color: #0057ff;
    }
    .association {
      padding: 46rpx 40rpx;
    }
    .confirm-btn {
      width: 278rpx;
      height: 70rpx;
      background: #0057ff;
      border-radius: 36rpx;
      color: #ffffff;
      font-size: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        opacity: 0.9;
      }
    }
  }
}

.float-buttons {
  position: fixed;
  right: 20rpx;
  bottom: 160rpx;
  z-index: 100;

  .float-button {
    width: 100rpx;
    height: 100rpx;
    background: #4a90e2;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(to bottom, #647cf8, #428dfe);
    box-shadow: 8rpx 12rpx 38rpx 0px #a6b7fa;
  }
}
