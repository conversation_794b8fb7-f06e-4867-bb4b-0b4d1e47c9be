import { getCourseDetail } from "~/api/mentorShop";
import { webUpload } from "~/api/webUpload"
import { navigateAndAddPage } from '~/utils/pageNavigation';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showVideDelete: false,
    autosize: {
      maxHeight: 200,
      minHeight: 40,
    },
    form: {
      auditStatus: '',
      isIndividualSale: 0, //是否单独售卖
      courseName: '', //视频名称
      coursePrice: '', //价格
      videoUrl: '', //视频
      courseCoverUrl: '', //视频封面
      isBundleSale: 0, //是否关联专栏
      isTextNoCopy: 0, //是否防复制
      isMarquee: 0, //是否防录屏跑马灯
      isPublishToDynamic: 0, //是否发布至动态
      courseDetail: '', // 视频详情
      bundleColumnsIds: null, // 关联课程
      courseType: 10,
      saleMethod: 20,
      courseDuration: 0 //视频时长
    },
    showActionSheet: false,
    currentSaleType: '免费',
    isPlaying: false,
    showCrop: false,
    imgFile: null,
    id: null,
    isUploading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const tempForm = wx.getStorageSync('tempVideoForm');
    if (tempForm) {
      this.setData({
        form: tempForm
      });
      this.setData({
        currentSaleType: tempForm.saleMethod === 10 ? '付费' : '免费'
      });
    } else if (options.id) {
      this.setData({
        id: options.id
      }, () => {
        this.getCourseDetail();
      })
    }
    wx.setNavigationBarTitle({
      title: options.id ? '编辑视频' : '添加视频'
    })
  },
  onShow() {
    // 检查并恢复表单数据
    const tempForm = wx.getStorageSync('tempVideoForm');
    if (tempForm) {
      this.setData({
        form: tempForm
      });
    }
    // () => {
    //   wx.removeStorage({
    //     key: 'tempVideoForm'
    //   });
    // }
  },


  getCourseDetail() {
    getCourseDetail({
      coursesId: this.data.id
    }).then(res => {
      // 审核失败的课程允许删除视频
      if (res.data.auditStatus === 30) {
        this.setData({
          showVideDelete: true
        });
      }
      this.setData({
        form: res.data
      });
      this.setData({
        currentSaleType: res.data.saleMethod === 10 ? '付费' : '免费'
      });
    })
  },

  // 处理裁剪完成事件
  handleCropperDone(e) {
    const {
      url
    } = e.detail;
    this.setData({
      'form.courseCoverUrl': url,
      showCrop: false, // 关闭弹窗
      imgFile: null // 清空临时图片
    });
    this.updateFormData('courseCoverUrl', url);
  },

  // 取消裁剪
  cancelShowCrop() {
    this.setData({
      showCrop: false,
      imgFile: null
    });
  },
  showActionSheet() {
    wx.showActionSheet({
      itemList: ['免费', '付费'],
      success: (res) => {
        this.setData({
          currentSaleType: res.tapIndex === 0 ? '免费' : '付费',
          'form.saleMethod': res.tapIndex === 0 ? 20 : 10
        });
        this.updateFormData('saleMethod', res.tapIndex === 0 ? 20 : 10);
        this.updateFormData('currentSaleType', res.tapIndex === 0 ? '免费' : '付费');
      }
    });
  },
  handleColumnNameChange(e) {
    let value = e.detail;
    // 去除空格和换行符
    value = value.replace(/\s+/g, '').replace(/\n/g, '');
    this.updateFormData('courseName', value);
  },
  handleRadioChange(e) {
    this.updateFormData('radio', e.detail);
  },
  handleSwitchChange(e) {
    this.updateFormData('isIndividualSale', e.detail ? 1 : 0);
  },
  handlePriceChange(e) {
    let value = e.detail;
    if (typeof value === 'object' && value.hasOwnProperty('value')) {
      value = value.value;
    }
  
    // 只保留数字和小数点
    let newValue = value.replace(/[^\d.]/g, '');
  
    // 防止多个小数点
    const parts = newValue.split('.');
    if (parts.length > 2) {
      newValue = parts[0] + '.' + parts.slice(1).join('');
    }
  
    // 小数点开头补0
    if (newValue.startsWith('.')) {
      newValue = '0' + newValue;
    }
  
    // 限制小数点后2位
    const match = newValue.match(/^(\d*)(\.?\d{0,2})/);
    const validValue = match ? match[1] + match[2] : '';
  
    // 去掉开头多余的0（比如 0002 -> 2，注意 0.25 要保留）
    let finalValue = validValue;
  
    if (finalValue === '') {
      finalValue = ''; // 如果完全删除了内容，清空
    } else if (finalValue.indexOf('.') === -1) {
      finalValue = String(Number(finalValue)); // 转成数字再转回字符串，去掉无意义0
    }
  
    this.setData({
      'form.coursePrice': finalValue
    });
  },
  
  handlePriceBlur(e) {
    let value = this.data.form.coursePrice;
  
    if (!value) {
      return;
    }
  
    let num = Number(value);
  
    if (isNaN(num) || num <= 0) {
      this.setData({
        'form.coursePrice': ''
      });
      return;
    }
  
    if (num > 100000) {
      wx.showToast({
        title: '价格不能超过100000元',
        icon: 'none'
      });
      this.setData({
        'form.coursePrice': '100000'
      });
      return;
    }
  
    // 失焦时不自动补 .00
    let finalValue = value;
    if (value.includes('.')) {
      // 如果本来就是小数，最多保留2位
      finalValue = num.toFixed(2).replace(/\.?0+$/, ''); 
    }
  
    this.setData({
      'form.coursePrice': finalValue
    });
  },
  

  handleAssociationChange(e) {
    this.updateFormData('isBundleSale', e.detail ? 1 : 0);
  },
  handleCopyChange(e) {
    this.updateFormData('isTextNoCopy', e.detail ? 1 : 0);
  },
  handleRunChange(e) {
    this.updateFormData('isMarquee', e.detail ? 1 : 0);
  },
  handleDynamicChange(e) {
    this.updateFormData('isPublishToDynamic', e.detail ? 1 : 0);
  },
  // 添加统一更新表单数据的方法
  updateFormData(key, value) {
    this.setData({
      [`form.${key}`]: value
    });
    wx.setStorageSync('tempVideoForm', this.data.form);
  },
  chooseVideo() {
    // maxDuration: 3600,  // 最大时长60分钟
    wx.chooseMedia({
      count: 1,
      mediaType: ['video'],
      sourceType: ['album'],
      camera: 'back',
      compressed: false,
      success: (res) => {
        console.log(res, 'res')
        this.setData({
          isUploading: true
        })
  
        const tempFile = res.tempFiles[0];
        const tempFilePath = tempFile.tempFilePath;
        const size = tempFile.size;
  
        // 检查视频大小
        if (size >  500 * 1024 * 1024) {
          this.setData({ isUploading: false })
          wx.showToast({
            title: '视频大小不能超过500M',
            icon: 'none'
          })
          return
        }
        //获取更精准的视频时长
        wx.getVideoInfo({
          src: tempFilePath,
          success: async (info) => {
            console.log(info,'info');
            const accurateDuration = Math.floor(info.duration);
            const { url } = await webUpload(tempFilePath, 'mentor');
            this.setData({
              isUploading: false,
              'form.courseDuration': accurateDuration,
              'form.videoUrl': url
            });
            this.updateFormData('courseDuration', accurateDuration);
            this.updateFormData('videoUrl', url);
          },
          fail: async (err) => {
            console.error('获取视频信息失败:', err);
  
            // 如果获取失败，仍可上传，但使用 chooseMedia 返回的 duration
            const fallbackDuration = Math.floor(tempFile.duration);
            const { url } = await webUpload(tempFilePath, 'mentor');
  
            this.setData({
              isUploading: false,
              'form.courseDuration': fallbackDuration,
              'form.videoUrl': url
            });
            this.updateFormData('courseDuration', fallbackDuration);
            this.updateFormData('videoUrl', url);
          }
        });
      },
      fail: (err) => {
        this.setData({
          isUploading: false
        });
        // wx.showToast({
        //   title: '视频选择失败,请重试',
        //   icon: 'none'
        // });
      }
    });
  },

  chooseVideoCover() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const file = res.tempFiles[0];
        const fileSize = file.size / 1024 / 1024;

        if (fileSize > 5) {
          wx.showToast({
            title: "图片请控制在5MB以内",
            icon: "none",
          });
          return;
        }
        const imgFile = {
          url: file.tempFilePath,
          width: 320,
          height: 180
        };
        this.setData({
          showCrop: true,
          imgFile: imgFile
        })
      },
      fail: (err) => {
        console.log('视频封面选择失败', err);
      }
    })
  },
  removeVideo() {
    this.setData({
      'form.videoUrl': ''
    })
    this.updateFormData('videoUrl', '');
  },
  removeVideoCover() {
    this.setData({
      'form.courseCoverUrl': ''
    })
    this.updateFormData('courseCoverUrl', '');
  },
  showTip(e) {
    const tipMessages = {
      association: '该商品放入专栏中进行售卖',
      run: '开启后，学生观看课程会出现个人ID，可以有效防止录屏',
      copy: '视频详情禁止复制'
    };

    const type = e.currentTarget.dataset.type;
    const message = tipMessages[type];

    if (message) {
      wx.showModal({
        title: '',
        content: message,
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },
  onAddColumn() {
    // 保存当前表单数据到缓存
    wx.setStorageSync('tempVideoForm', this.data.form);
    navigateAndAddPage('/pages/module-mentorShop/all-column/all-column',{
      type:'add'
    })
    // wx.navigateTo({
    //   url: '/pages/module-mentorShop/all-column/all-column?type=add'
    // })
  },
  onEditDetail() {
    // 保存当前表单数据到缓存
    wx.setStorageSync('tempVideoForm', this.data.form);
    navigateAndAddPage('/pages/module-mentorShop/edit-detail/edit-detail',{
      type:'video'
    })
    // wx.navigateTo({
    //   url: '/pages/module-mentorShop/edit-detail/edit-detail?type=video'
    // })
  },
  onSave() {
    if (!this.validateForm()) {
      return;
    }
    // const url = this.data.id ? `/pages/module-mentorShop/course-detail/course-detail?type=video&id=${this.data.id}` : `/pages/module-mentorShop/course-detail/course-detail?type=video`;
    wx.setStorageSync('tempVideoForm', this.data.form);
    navigateAndAddPage('/pages/module-mentorShop/course-detail/course-detail', {
      type: 'video',
      id: this.data.id  // 如果是 null/undefined，会被自动忽略（你封装时有做过滤）
    });
    // wx.navigateTo({
    //   url: url
    // })

    // const data = {
    //   ...this.data.form
    // }
    // addCourse(data).then(res => {
    //   if (res.success) {
    //     if (this.data.id) {
    //       wx.navigateTo({
    //         url: `/pages/module-mentorShop/course-detail/course-detail?type=video&id=${this.data.id}`
    //       })
    //     } else {
    //       wx.navigateTo({
    //         url: `/pages/module-mentorShop/course-detail/course-detail?type=video&id=${res.data}`
    //       })
    //     }
    //   }
    // })

  },

  validateForm() {
    // 检查视频
    if (!this.data.form.videoUrl) {
      wx.showToast({
        title: '请上传视频',
        icon: 'none'
      });
      return false;
    }

    // 检查视频封面
    if (!this.data.form.courseCoverUrl) {
      wx.showToast({
        title: '请上传视频封面',
        icon: 'none'
      });
      return false;
    }

    // 检查视频名称
    if (!this.data.form.courseName.trim()) {
      wx.showToast({
        title: '请填写视频名称',
        icon: 'none'
      });
      return false;
    }

    // 检查视频详情
    if (!this.data.form.courseDetail) {
      wx.showToast({
        title: '请填写视频详情',
        icon: 'none'
      });
      return false;
    }

    // 检查售卖方式：单独售卖和关联售卖至少选择一种
    if (this.data.form.isIndividualSale === 0 && this.data.form.isBundleSale === 0) {
      wx.showToast({
        title: '请至少选择一种售卖方式',
        icon: 'none'
      });
      return false;
    }

    // 如果选择了单独售卖，检查价格
    if (this.data.form.isIndividualSale === 1) {
      if (this.data.currentSaleType === '付费') {
        if (!this.data.form.coursePrice) {
          wx.showToast({
            title: '请填写商品金额',
            icon: 'none'
          });
          return false;
        }

        const price = parseFloat(this.data.form.coursePrice);
        if (isNaN(price) || price <= 0 || price > 100000) {
          wx.showToast({
            title: '售价范围为0.01-100000元',
            icon: 'none'
          });
          return false;
        }
      }
    }

    // 如果选择了关联售卖，检查关联课程
    // if (this.data.form.isBundleSale === 1 && !this.data.form.bundleColumnsIds) {
    //   wx.showToast({
    //     title: '请选择关联课程',
    //     icon: 'none'
    //   });
    //   return false;
    // }

    return true;
  },

  // 添加视频预览功能
  previewVideo() {
    // 暂停缩略图视频
    const myVideoContext = wx.createVideoContext('myVideo');
    myVideoContext.pause();
    myVideoContext.seek(0);

    this.setData({
      showPreviewVideo: true
    }, () => {
      // 播放预览视频
      const previewVideoContext = wx.createVideoContext('previewVideo');
      previewVideoContext.play();
    });
  },

  closePreview() {
    // 控制预览视频
    const previewVideoContext = wx.createVideoContext('previewVideo');
    previewVideoContext.pause();
    previewVideoContext.seek(0);

    // 控制缩略图视频
    const myVideoContext = wx.createVideoContext('myVideo');
    myVideoContext.pause();
    myVideoContext.seek(0);

    this.setData({
      showPreviewVideo: false
    });
  }
})
