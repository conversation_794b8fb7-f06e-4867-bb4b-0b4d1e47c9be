/* pages/appoint/mentor-appoint/components/shop/shop.wxss */

/* 课程列表页面样式 */
.course-container {
  margin-top: 28rpx;
  padding: 0 36rpx 30rpx;
  // height: 100%;
  // min-height: 400rpx;
  .special {
    background-color: #fff;
    padding: 32rpx 28rpx;
    border-radius: 16rpx;
    box-shadow: 0px 0px 32rpx 0px rgba(0, 0, 0, 0.04);

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      padding: 0 4rpx;

      .title {
        font-size: 30rpx;
        color: #333;
        font-weight: bolder;
      }

      .more {
        font-size: 24rpx;
        color: #777777;
      }
      .more-add{
        display: flex;
        align-items: flex-start;
        text{
          color: #797979;
          font-size: 28rpx;
          margin-right: 16rpx;
        }
      }
    }

    .column-section {
      // margin-top: 20rpx;
      .course-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16rpx;

        .course-item {
          margin-top: 32rpx;
          border-radius: 8rpx;
          overflow: hidden;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

          .image-wrapper {
            position: relative;
            width: 100%;
            height: 186rpx;

            .shadow-top,
            .shadow-top-second {
              width: 80%;
              background: #e2e2e2;
              position: absolute;
              height: 10rpx;
              border-radius: 8rpx 8rpx 0 0;
              top: 0rpx;
              z-index: 12;
              left: 50%;
              transform: translateX(-50%);
              z-index: 1;
            }

            .shadow-top-second {
              width: 90%;
              top: 10rpx;
              background-color: #b1b3b7;
            }

            .course-image {
              width: 100%;
              height: 100%;
              position: relative;
              top: 20rpx;
              border-radius: 8rpx;
            }

            .course-stats {
              position: absolute;
              left: 0;
              right: 0;
              bottom: 0;
              padding: 8rpx 16rpx 6rpx;
              display: flex;
              align-items: center;
              justify-content: space-between;
              background-color: rgba(0, 0, 0, 0.5);
              font-size: 24rpx;
              color: #fff;

              .left-stats {
                display: flex;
                align-items: center;

                .icon {
                  width: 24rpx;
                  height: 24rpx;
                  margin-right: 6rpx;
                  filter: brightness(0) invert(1);
                }
              }
            }
          }
        }
      }
    }
  }

  .course {
    // height: 100%;
    min-height: 300rpx;
    margin-top: 28rpx;
    border-radius: 16rpx;
    box-shadow: 0px 0px 16px 0px rgba(99, 97, 155, 0.1);
    // background: #fff;
    padding: 24rpx 30rpx 0;

    .tab-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 2rpx solid #c4c4c4;
      // padding: 20rpx 0;

      view:nth-child(1) {
        display: flex;
      }

      .filter {
        color: #777777;
        font-size: 24rpx;
        position: relative;
        display: flex;
        align-items: center;

        .van-icon {
          margin-left: 4rpx;
          transition: transform 0.3s ease;
        }

        .filter-dropdown {
          position: absolute;
          top: calc(100% + 20rpx);
          right: 0;
          width: 160rpx;
          background: #fff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          border-radius: 4px;
          z-index: 100;
          display: none;

          &.show {
            display: block;
          }

          .filter-item {
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10rpx;
            font-size: 24rpx;
            color: #777777;
            margin: 13rpx;
            background-color: #f8f9fe;
            border-radius: 8rpx;
            border: 2rpx solid transparent;
            &.active {
              color: #0057ff;
              border: 2rpx solid #0057ff;
            }

            &:hover {
              background: #f5f5f5;
            }
          }
        }
        .filter-dropdown-top{
          // top: unset;
          // bottom: calc(100% + 20rpx);
        }
      }

      .tab {
        position: relative;
        padding: 10rpx 0;
        margin-right: 48rpx;
        font-size: 30rpx;
        color: #797979;

        &.active {
          color: #0057ff;
          font-weight: bold;

          &::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 48rpx;
            height: 4rpx;
            background: #0057ff;
            border-radius: 26rpx;
          }
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .video-section {
      height: 100%;

      .course-list {
        .course-item {
          padding: 28rpx 0 28rpx;
          display: flex;
          border-bottom: 2rpx solid #c4c4c4;
          width: 100%;
          box-sizing: border-box;
          overflow: hidden;
          &:nth-child(1) {
            padding-top: 36rpx;
          }
          &:last-child {
            border: 0;
            padding-bottom: 40rpx;
          }

          .image-wrapper {
            position: relative;
            width: 244rpx;
            height: 136rpx;

            .course-image {
              width: 100%;
              height: 100%;
              border-radius: 8rpx;
            }

            .course-mask {
              border-radius: 8rpx;
              position: absolute;
              top: 0;
              left: 0;
              bottom: 0;
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0.3);
              color: #fff;
              font-weight: bolder;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 28rpx;
            }
          }

          .course-info {
            margin-left: 16rpx;
            flex: 1;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            width: calc(100% - 244rpx - 16rpx); /* 减去图片宽度和左边距 */
            box-sizing: border-box;
            overflow: hidden;

            .course-meta {
              // margin-bottom: 10rpx;
              flex: 1;
              min-width: 0;
              min-height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              .course-title {
                word-break: break-all;
                // width: 248rpx;
                color: #333333;
                font-size: 26rpx;
                font-weight: bold;
                line-height: 30rpx;font-weight: bold;
                font-size: 26rpx;
                color: #333333;
                text-align: left;
                font-style: normal;
                text-transform: none;
                line-height: 36rpx;
                flex: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                margin-right: 10rpx;
                word-break: break-all;
              }
            }

            .right {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              flex-shrink: 0;
              margin-left: 10rpx;
              min-width: 120rpx; 
              min-height: 100%;
              justify-content: space-between;
            }

            .rating {
              display: flex;
              align-items: center;
              // flex: 1;
              flex-shrink: 0;
              text-align: right;

              .score,
              .value {
                color: #0057ff;
                font-size: 20rpx;
              }

              .score {
                color: #9ea3ac;
              }
            }
            .duration{
              font-weight: 500;
              font-size: 24rpx;
              color: #9EA3AC;
              text-align: left;
              font-style: normal;
              text-transform: none;
              margin-top: 10rpx;
              line-height: 34rpx;
            }
            .student-count {
              font-size: 24rpx;
              color: #9ea3ac;
              line-height: 28rpx;
              margin-top: 14rpx;
              text-align: right;
            }

            .student-count {
              margin-top: 26rpx;
            }

            .price-box {
              margin-top: 14rpx;
              display: flex;
              align-items: baseline;
              font-weight: bold;
              color: #ff0019;
              line-height: 40rpx;
              .currency {
                font-size: 24rpx;
                margin-right: 2rpx;
              }

              .price {
                font-size: 36rpx;
              }
            }

            .right-bottom {
              margin-top: 14rpx;
              color: #ffffff;
              font-size: 24rpx;
              font-weight: 500;
              background-color: #979797;
              border-radius: 8rpx;
              text-align: center;
              padding: 2rpx 16rpx;
              box-sizing: border-box;
              // width: 100%;
              // max-width: 120rpx; /* 限制按钮最大宽度 */

              &.online {
                color: #fff;
                background-color: #ff0019;
              }

              &.offline {
                color: #fff;
                background-color: #0057ff;
              }

              &.gray {
                background-color: #979797;
              }
            }
          }
        }
      }
    }
  }
}
.course-container-min{

  min-height: 840rpx;
}
.finishedText {
  font-size: 28rpx;
  text-align: center;
  color: #666;
  padding-bottom: 20rpx;
  // padding: 66rpx;
}


.loading{
  // padding: 60rpx 0;
  height: 432rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}