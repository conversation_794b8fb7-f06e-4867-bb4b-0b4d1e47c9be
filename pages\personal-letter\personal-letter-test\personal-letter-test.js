// pages/chat/chat.js
Page({
  data: {
    spaceheight:1000,//预留高度，用与无缝向上滚动
    pagesize:20,//，每一页大小
    scrollTop:0,
    messages: [], // 当前的消息列表
    page: 1, // 当前加载的页数
    isLoading: false, // 是否正在加载
  },

  onLoad() {
    this.initData();
  },

  async initData(){
    var that=this;
    let newMessages=[];
    for(let i =1; i <= that.data.pagesize; i ++) {
      newMessages.push( `消息 第${that.data.page }页，第${i }条`)
    }
    // 将新的消息添加到现有消息的开头
    that.setData({
      messages: newMessages
    })
    that.setData({
      page: that.data.page+1
    })
    // this.page=this.page+1;
    wx.nextTick(function(){
      that.setData({
        scrollTop: 1200
      })
      // that.scrollTop=1200;
    })
  },
  // 加载更多历史消息
  async loadMoreMessages() {
  if (this.data.isLoading) return;
  // this.isLoading = true;
  this.setData({
    isLoading: true
  })
  var that=this;
  
    // 模拟异步请求，实际应用中替换成API调用
    setTimeout(() => {
      if(that.data.page>=10){
        // that.spaceheight=0;
        that.setData({
          spaceheight: 0
        })
        wx.showToast({
          title:"没有更多了",
          icon:"none"
        })
      return;
    }
    let newMessages=[];
    for(let i =1; i <= that.data.pagesize; i ++) {
      newMessages.push( `消息 第${that.data.page}页，第${i}条`);
    }
  
    // 将新的消息添加到现有消息的开头
    // that.messages = [ ...that.messages,...newMessages];
    that.setData({
      messages: [ ...that.data.messages,...newMessages]
    })
    that.setData({
      page: that.data.page + 1
    })
    // that.page += 1;
    that.setData({
      isLoading: false
    })
    // that.isLoading = false;
    }, 400);
  },
  // 滚动事件监听
  onScroll(event) {
    const { scrollTop } = event.detail;
    if (scrollTop <1200 && !this.data.isLoading) {
    // 触发上拉加载
    this.loadMoreMessages();
    }
  },
});