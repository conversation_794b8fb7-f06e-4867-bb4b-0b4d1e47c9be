/* pages/module-mentorShop/dynamics-detial/dynamics-detial.wxss */
page {
  box-sizing: border-box;
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }
}
.dynamics-detial {
  height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 支持弹性滚动 */
  .dynamics-box {
    height: 100%;
  }
  .comment-box {
    padding: 24rpx 36rpx;
    background: #fff;
    .header {
      display: flex;
      justify-content: space-between;
      .left {
        display: flex;
        align-items: center;
        .avatar {
          width: 63rpx;
          height: 63rpx;
          border-radius: 50%;
          margin-right: 14rpx;
        }
        .name {
          font-weight: 600;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 6rpx;
        }
        .time {
          color: #777777;
          font-size: 18rpx;
        }
      }

      .time-reply {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 24rpx;
        color: #8c919a;
        .icon {
          width: 24rpx;
          height: 22rpx;
          margin-right: 10rpx;
        }
      }
    }
    .comment-list {
      // padding-bottom: 100rpx;

      .title {
        color: #9ea3ac;
        font-size: 24rpx;
        margin-bottom: 24rpx;
      }
      .comment-item {
        margin-bottom: 30rpx;
        padding-bottom: 24rpx;
        border-bottom: 1rpx solid #c1c1c1;
        &:last-child {
          border-bottom: none;
        }
        &:nth-last-child(1) {
          margin-bottom: 0;
        }
        .comment-main {
          // display: flex;
          margin-bottom: 18rpx;

          .content {
            margin-top: 18rpx;
            color: #737783;
            font-size: 26rpx;
          }
        }

        .replies-container {
          box-sizing: border-box;
          padding-left: 20rpx;
          // padding-bottom: 100rpx;
          .reply-list {
            .reply-item {
              margin-bottom: 26rpx;
              .left {
                .avatar {
                  width: 48rpx;
                  height: 48rpx;
                }
                .name {
                  font-weight: 600;
                  margin-right: 10rpx;
                }
              }

              &:last-child {
                margin-bottom: 0;
              }
              .reply-content {
                margin-left: 60rpx;
                font-size: 26rpx;
                color: #737783;
                line-height: 40rpx;
                margin-top: 6rpx;
                word-break: break-all;
                // display: flex;
                .reply-tips{
                  margin-right: 4rpx;
                  color: #333;
                }
                .name {
                  color: #5B6994;
                  font-weight: 600;
                  word-break: break-all;
                }
              }
            }
          }

          .expand-replies {
            font-size: 26rpx;
            color: #0057ff;
            padding: 24rpx 0 0;
          }
        }
      }
    }

    .reply-input-container {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 20rpx;
      background: #fff;
      border-top: 1rpx solid #eee;
      display: flex;
      align-items: flex-end;
      .message-field {
        flex: 1;
        .van-cell {
          border-radius: 20rpx;
          background-color: #eff1f7;
        }
      }
      .send-btn {
        margin-bottom: 10rpx;
        width: 72rpx;
        height: 72rpx;
        margin-left: 16rpx;
      }
    }
  }
}
