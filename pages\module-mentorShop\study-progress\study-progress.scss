/* pages/module-mentorShop/study-progress/study-progress.wxss */
page {
  height: 100vh;
  &::-webkit-scrollbar {
    display: none;
  }
}
.progress-container {
  padding: 20rpx;
  background: #f5f6f7;
  height: 100%;

  .progress-list {
    .progress-item {
      background: #fff;
      padding: 20rpx 36rpx;
      margin-bottom: 16rpx;
      border-radius: 12rpx;
      .top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8rpx;
      }
      .item-left {
        display: flex;
        align-items: center;
        .index {
          font-size: 40rpx;
          color: #333;
          margin-right: 38rpx;
        }

        .avatar {
          width: 57rpx;
          height: 57rpx;
          border-radius: 50%;
          margin-right: 12rpx;
        }

        .name {
          font-size: 28rpx;
          color: #333;
        }
      }

      .item-right {
        flex: 1;
        margin-left: 30rpx;
        .time {
          font-size: 28rpx;
          color: #777777;
          text-align: right;
          text {
            color: #0057FF;
          }
        }
      }
      .progress-bar {
        width: 90%;
        height: 16rpx;
        border: 2rpx solid #0057FF;
        border-radius: 12rpx;
        margin-left: auto;
        .progress {
          height: 100%;
          background: #0057FF;
          border-radius: 12rpx;
          transition: width 0.3s ease;
        }
      }
    }
  }
}
