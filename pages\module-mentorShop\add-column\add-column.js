// pages/module-mentorShop/add-column/add-column.js
import { addOrUpdateColumn, getColumnDetail } from '~/api/mentorShop';
import { navigateAndAddPage, navigateBack } from '~/utils/pageNavigation';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    form: {
      updateSortRule: '',//内容更新排序
      checked: false,
      columnName: '',//专栏名称
      columnPrice: '',//售价
      columnCoverUrl: '',//封面图片
      columnDetail: '',//专栏详情
      saleMethod: 20, // 售卖方式：10-付费, 20-免费
    },
    showActionSheet: false,
    currentSaleType: '免费',
    showCrop: false,
    imgFile: null,
    autosize: {
      maxHeight: 200,
      minHeight: 40,
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.setNavigationBarTitle({
      title: options.id ? '编辑专栏' : '新增专栏'
    })
    if (options.id) {
      this.setData({
        id: options.id
      }, () => {
        this.getColumnDetail();
      })
    }
  },
  onShow() {
    // 检查并恢复表单数据
    const tempForm = wx.getStorageSync('tempColumnForm');
    if (tempForm) {
      this.setData({
        form: tempForm
      }, () => {
        wx.removeStorage({
          key: 'tempColumnForm'
        });
      });
    }
  },

  getColumnDetail() {
    getColumnDetail({
      columnsId: this.data.id
    }).then(res => {
      this.setData({
        form: res.data
      })
      this.setData({
        currentSaleType:res.data.saleMethod === 10 ? '付费' : '免费',
        'form.updateSortRule':String(res.data.updateSortRule)
      });
      
    })
  },

  showActionSheet() {
    wx.showActionSheet({
      itemList: ['免费', '付费'],
      success: (res) => {
        const saleMethod = res.tapIndex === 0 ? 20 : 10;
        this.setData({
          'form.saleMethod': saleMethod,
          currentSaleType: res.tapIndex === 0 ? '免费' : '付费'
        });
      }
    });
  },
  
  handleColumnNameChange(e) {
    this.setData({
      'form.columnName': e.detail
    })
  },
  handleRadioChange(e) {
    this.setData({
      'form.updateSortRule': e.detail
    })
  },
  handleSwitchChange(e) {
    this.setData({
      'form.checked': e.detail
    })
  },
  handlePriceChange(e) {
    let value = e.detail;
    if (typeof value === 'object' && value.hasOwnProperty('value')) {
      value = value.value;
    }
  
    // 只保留数字和小数点
    let newValue = value.replace(/[^\d.]/g, '');
  
    // 防止多个小数点
    const parts = newValue.split('.');
    if (parts.length > 2) {
      newValue = parts[0] + '.' + parts.slice(1).join('');
    }
  
    // 小数点开头补0
    if (newValue.startsWith('.')) {
      newValue = '0' + newValue;
    }
  
    // 限制小数点后2位
    const match = newValue.match(/^(\d*)(\.?\d{0,2})/);
    const validValue = match ? match[1] + match[2] : '';
  
    // 去掉开头多余的0（比如 0002 -> 2，注意 0.25 要保留）
    let finalValue = validValue;
  
    if (finalValue === '') {
      finalValue = ''; // 如果完全删除了内容，清空
    } else if (finalValue.indexOf('.') === -1) {
      finalValue = String(Number(finalValue)); // 转成数字再转回字符串，去掉无意义0
    }
  
    this.setData({
      'form.columnPrice': finalValue
    });
  },
  
  handlePriceBlur(e) {
    let value = this.data.form.columnPrice;
  
    if (!value) {
      return;
    }
  
    let num = Number(value);
  
    if (isNaN(num) || num <= 0) {
      this.setData({
        'form.columnPrice': ''
      });
      return;
    }
  
    if (num > 100000) {
      wx.showToast({
        title: '价格不能超过100000元',
        icon: 'none'
      });
      this.setData({
        'form.columnPrice': '100000'
      });
      return;
    }
  
    // 失焦时不自动补 .00
    let finalValue = value;
    if (value.includes('.')) {
      // 如果本来就是小数，最多保留2位
      finalValue = num.toFixed(2).replace(/\.?0+$/, ''); 
    }
  
    this.setData({
      'form.columnPrice': finalValue
    })
  },
  onEditDetail() {
    wx.setStorageSync('tempColumnForm', this.data.form);
    navigateAndAddPage('/pages/module-mentorShop/edit-detail/edit-detail',{
      type:'column'
    })
    // wx.navigateTo({
    //   url: '/pages/module-mentorShop/edit-detail/edit-detail?type=column'
    // })
  },
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const file = res.tempFiles[0];
        const fileSize = file.size / 1024 / 1024;
  
        if (fileSize > 5) {
          wx.showToast({
            title: "图片请控制在5MB以内",
            icon: "none",
          });
          return;
        }
        const imgFile = {
          url: file.tempFilePath,
          width: 320,
          height: 180
        };
        this.setData({
          showCrop: true,
          imgFile: imgFile
        })
      },
      fail: (err) => {
        console.log('图片选择失败', err);
      }
    });
  },
  removeImage() {
    this.setData({
      'form.columnCoverUrl': ''
    });
  },
  // 处理裁剪完成事件
  handleCropperDone(e) {
    const {
      url
    } = e.detail;
    this.uploadAvatar(url)
  },
  // 上传头像到服务器
  async uploadAvatar(url) {
    this.setData({
      'form.columnCoverUrl': url,
      showCrop: false, // 关闭弹窗
      imgFile: null // 清空临时图片
    });
  },
  // 取消裁剪
  cancelShowCrop() {
    this.setData({
      showCrop: false,
      imgFile: null
    });
  },
  onAddColumn() {
    if (!this.validateForm()) {
      return;
    }

    addOrUpdateColumn(this.data.form).then(res => {
      if (res.success) {
        wx.showToast({
          title: `${this.data.id ? '编辑' : '新增'}成功`,
          icon: 'none'
        })
        setTimeout(() => {
          navigateBack()
        }, 1000);
      }
    });
  },

  validateForm() {
    // 专栏名称验证
    if (!this.data.form.columnName.trim()) {
      wx.showToast({
        title: '请填写专栏名称',
        icon: 'none'
      });
      return false;
    }

    // 专栏详情验证
    if (!this.data.form.columnDetail) {
      wx.showToast({
        title: '请填写专栏详情',
        icon: 'none'
      });
      return false;
    }

    // 封面验证
    if (!this.data.form.columnCoverUrl) {
      wx.showToast({
        title: '请上传专栏封面',
        icon: 'none'
      });
      return false;
    }

    // 内容更新排序验证
    if (!this.data.form.updateSortRule) {
      wx.showToast({
        title: '请设置内容更新排序',
        icon: 'none'
      });
      return false;
    }

    // 售卖方式验证
    // if (!this.data.form.checked) {
    //   wx.showToast({
    //     title: '请选择售卖方式',
    //     icon: 'none'
    //   });
    //   return false;
    // }

    // 付费情况下的金额验证
    if (this.data.currentSaleType === '付费') {
      if (!this.data.form.columnPrice) {
        wx.showToast({
          title: '请填写商品金额',
          icon: 'none'
        });
        return false;
      }

      const price = parseFloat(this.data.form.columnPrice);
      if (isNaN(price) || price <= 0 || price > 100000) {
        wx.showToast({
          title: '售价范围为0.01-100000元',
          icon: 'none'
        });
        return false;
      }
    }

    return true;
  },
})