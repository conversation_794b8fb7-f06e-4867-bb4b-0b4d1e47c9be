/* pages/appoint/report-detail/report-detail.wxss */
page{
  min-height: 100%;
  background-color: rgb(248, 248, 248);
}
.mentor-introduce2{
  word-break: break-all;
  white-space: normal;
  margin-top: 12rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #737783;
  line-height: 36rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 控制最大行数 */
  overflow: hidden;
  &:nth-child(1) {
    margin-top: 0;
  }
}
.mentor-introduce-position2{
  word-break: break-all;
  white-space: normal;
  padding: 0 36rpx;
  width: 100vw;
  display: block;
  margin-top: 0;
  position: fixed;
  left: -9999px;
  top: 0;
}

.mentor-introduce{
  word-break: break-all;
  white-space: normal;
  margin-top: 12rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #737783;
  line-height: 36rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 控制最大行数 */
  overflow: hidden;
  &:nth-child(1) {
    margin-top: 0;
  }
}
.mentor-introduce-position{
  word-break: break-all;
  white-space: normal;
  padding: 0 36rpx;
  width: 100vw;
  display: block;
  margin-top: 0;
  position: fixed;
  left: -9999px;
  top: 0;
}
.show-all-text{
  display: block;
}
.report-detail{
  padding: 36rpx;
  .page-title{
    color: #333333;
    font-size: 32rpx;
    font-weight: 700;
    line-height: 44rpx;
  }
  .card-box{
    margin-top: 28rpx;
    background: #FFF;
    padding: 30rpx;
    border-radius: 18rpx;
    .header{
      display: flex;
      width: 100%;
      justify-content: space-between;
      align-items: center;
      color: rgb(51, 51, 51);
      font-size: 30rpx;
      font-weight: 600;
      line-height: 42rpx;
      display: flex;
      align-items: center;
      position: relative;
      padding-bottom: 24rpx;
      .title{
        flex: 1;
        display: flex;
        align-items: center;
        image{
          width: 40rpx;
          height: 40rpx;
          margin-right: 14rpx;
        }
      }
      .header-right{
        padding: 4rpx 14rpx;
        background: rgba(107, 130, 246, 0.1);
        border: 2rpx solid #0057FF;
        border-radius: 4rpx;
        color: #0057FF;
        font-size: 24rpx;
        font-weight: 400;
        line-height: 34rpx;
      }
      .no-sign-in{
        background: rgba(121, 121, 121, 0.1);
        border-color: rgb(121, 121, 121);
        color: rgb(121, 121, 121);
      }
    }
    &:nth-child(1) {
      margin-top: 0;
    }
  }
  .report-base{
    .base-box{
      display: flex;
      flex-direction: column;
      .base-box-item{
        margin-top: 12rpx;
        font-size: 26rpx;
        font-weight: 500;
        line-height: 42rpx;
        display: flex;
        .base-item-label{
          color: rgb(119, 119, 119);
          // margin-right: 28rpx;
          width: 132rpx;
          white-space: nowrap;
        }
        .base-item-value{
          flex: 1;
        }
        &:nth-child(1) {
          margin-top: 0;
        }
      }
    }
  }
  .question-directive{
    // .header{
    //   margin-bottom: 22rpx;
    //   color: rgb(51, 51, 51);
    //   font-size: 30rpx;
    //   font-weight: 600;
    //   line-height: 44tpx;
    //   display: flex;
    //   align-items: center;
    //   position: relative;
    // }
    .question-content{
      .appoint-describe{
        font-size: 26rpx;
        font-weight: 400;
        line-height: 42rpx;
        color: rgb(51, 51, 51);
        rich-text{
          line-height: 42rpx;
          color: #333;
          font-size: 28rpx;
        }
        .unfold{
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 10rpx;
          image{
            transform: rotate(90deg);
            width: 20rpx;
            height: 20rpx;
          }
          .arrow-top{
            transform: rotate(-90deg);
          }
        }
      }
      .appoint-describe-file{
        display: flex;
        margin-top: 20rpx;
        .file-icon{
          width: 40rpx;
          height: 40rpx;
          margin-right: 20rpx;
        }
        .file-name{
          color: rgb(51, 51, 51);
          font-size: 28rpx;
          font-weight: 500;
          line-height: 40rpx;
        }
      }
      .appoint-describe-image{
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        image{
          width: 300rpx;
          height: 200rpx;
          margin-top: 20rpx;
        }
      }
      .appoint-describe-supplementary{
        margin-top: 34rpx;
        .appoint-describe-supplementary-title{
          color: rgb(51, 51, 51);
          font-size: 30rpx;
          font-weight: 700;
        }
        .appoint-describe-supplementary-item{
          margin-top: 20rpx;
          font-size: 24rpx;
          .appoint-describe-supplementary-label{
            font-weight: 500;
            color: #333333;
          }
          .appoint-describe-supplementary-value{
            // color: rgb(119, 119, 119);
            color: #777777;
            margin-top: 8rpx;
            word-break: break-all;
          }
        }
      }
    }
  }
  .appoint-course{
    .appoint-course-content{
      color: rgb(51, 51, 51);
      font-size: 26rpx;
      font-weight: 400;
      line-height: 42rpx;
      rich-text{
        line-height: 42rpx;
        color: #333;
        font-size: 28rpx;
      }
      .unfold{
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 10rpx;
        image{
          transform: rotate(90deg);
          width: 20rpx;
          height: 20rpx;
        }
        .arrow-top{
          transform: rotate(-90deg);
        }
      }
    }
    .appoint-course-record{
      margin-top: 20rpx;
      border-top: 2rpx solid rgb(231, 236, 239);
      padding-top: 20rpx;
      .report-record-record-record-item{
        margin-top: 14rpx;
        .report-record-record-record-text{
          color: rgb(51, 51, 51);
          font-size: 30rpx;
          font-weight: 700;
          line-height: 42rpx;
        }
        .report-record-record-record-progress{
            margin-top: 14rpx;
            display: flex;
            align-items: center;
            width: 100%;
            .operation{
              width: 36rpx;
              height: 36rpx;
              object-fit: cover;
              border-radius: 50%;
            }
            .progress{
              margin: 0 8rpx;
              flex: 1;
              van-slider{
                .van-slider__button-wrapper{
                  width: 18rpx;
                  height: 18rpx;
                  .van-slider__button{
                    width: 100%;
                    height: 100%;
                    background: #0057FF;
                  }
                }
              }
              // ::v-deep .van-slider__button{
              //   background: #0057FF;
              // }
            }
            .record-time{
              color: rgb(51, 51, 51);
              font-size: 24rpx;
              font-weight: 400;
              line-height: 32rpx;
              margin-right: 20rpx;
            }
            .icon{
              width: 32rpx;
              height: 32rpx;
              object-fit: cover;
            }
        }
      }
    }
  }
}