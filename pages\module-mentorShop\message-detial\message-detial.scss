/* pages/module-mentorShop/message-detial/message-detial.wxss */
.message-detail {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
  .finishedText{
    padding: 10rpx;
  }
  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
  }
  
  .message-list {
    ::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      color: transparent;
    }
    
    .message-item {
      display: flex;
      margin-bottom: 30rpx;
      padding: 16rpx;
      background: #fff;
      border-radius: 12rpx;
      
      .user-avatar {
        width: 96rpx;
        height: 96rpx;
        margin-right: 20rpx;
        
        image {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      
      .message-content {
        flex: 1;
        
        .user-name {
          font-size: 32rpx;
          line-height: 40rpx;
          color: #333;
          font-weight: 600;
          margin-bottom: 6rpx;
        }
        
        .message-text {
          font-size: 28rpx;
          color: #919BAE;
          line-height: 1.4;
          margin-bottom: 6rpx;
          display: flex;
          align-items: center;
          image{
            width: 30rpx;
            height: 30rpx;
            margin-right: 8rpx;
          }
        }
        
        .message-time {
          font-size: 24rpx;
          color: #919BAE;
        }
      }
      
      .message-image {
        width: 124rpx;
        height: 124rpx;
        background-color: #EFF1F7;
        font-size: 20rpx;
        line-height: 36rpx;
        view {
          width: calc(100% - 12rpx);
          height: calc(100% - 12rpx);
          overflow: hidden;
          padding: 6rpx;
          text-align: center;
          color: #737783;
          box-sizing: border-box;
        }
        image {
          width: 100%;
          height: 100%;
          border-radius: 8rpx;
        }
      }
    }
  }
}

.message-detail-container {
  height: 100vh;
  overflow: hidden;
  
  .message-detail {
    height: 100%;
  }
}