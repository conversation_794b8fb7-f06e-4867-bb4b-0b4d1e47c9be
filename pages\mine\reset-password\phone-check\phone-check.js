const regexp = require('~/utils/regexp')
import { getPhoneCode, resetPassword } from '~/api/login'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    value: '',
    phoneCode: '',
    countDown: null,

    password: '',
    passwordRepeat: ''
  },
  getPhoneCode() {
    const regexpResult = regexp.regexpPhone(this.data.value)
    if (!regexpResult) {
      wx.showToast({
        title: '请输入正确格式的手机号',
        duration: 1500,
        icon: 'none'
      })
      return
    }
    getPhoneCode({
      phone: this.data.value
    }).then(res => {
      wx.showToast({
        title: '验证码发送成功',
        duration: 1500,
        icon: 'none'
      })
      let countDown = 60
      this.setData({
        countDown
      })
      let interval = setInterval(() => {
        if (countDown <= 1) {
          clearInterval(interval)
          this.setData({
            countDown: null
          })
          return
        }
        countDown--
        this.setData({
          countDown
        })
      }, 1000)
    })
  },
  resetPassword() {
    const regexpResult = regexp.regexpPhone(this.data.value)
    if (!regexpResult) {
      wx.showToast({
        title: '请输入正确格式的手机号',
        duration: 1500,
        icon: 'none'
      })
      return
    }
    if (this.data.phoneCode.length < 6) {
      wx.showToast({
        title: '请输入短信验证码',
        duration: 1500,
        icon: 'none'
      })
      return
    }
    if ((this.data.password ?? '') === '' || (this.data.passwordRepeat ?? '') === '') {
      wx.showToast({
        title: '请输入密码',
        duration: 1500,
        icon: 'none'
      })
      return
    }
    if (!regexp.regexLoginPassword(this.data.password)) {
      wx.showToast({
        title: '密码要求为6-16位，必须包含字母和数字',
        duration: 1500,
        icon: 'none'
      })
      return
    }
    if (this.data.password !== this.data.passwordRepeat) {
      wx.showToast({
        title: '两次输入密码必须保持一致',
        duration: 1500,
        icon: 'none'
      })
      return
    }
    resetPassword({
      phone: this.data.value,
      smsCode: this.data.phoneCode,
      password1: this.data.password,
      password2: this.data.passwordRepeat
    }).then(res => {
      wx.showToast({
        title: '密码重置成功'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1000);
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})