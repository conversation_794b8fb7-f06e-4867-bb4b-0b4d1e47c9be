/* pages/components/uploader/uploader.wxss */
@mixin img-box {
  width: 87px;
  height: 87px;
  border-radius: 8rpx;
  // margin: 16rpx 16rpx 0 0;
}

@mixin textoverflow($line) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $line;
  -webkit-box-orient: vertical;
}
// ... existing code ...

.upload-status {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-loading {
  width: 30rpx;
  height: 30rpx;
  border: 2px solid #fff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: rotate 0.8s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.uploader-box {
  display: flex;
  align-items: center;
  .image-list {
    display: flex;
    align-items: center;

    flex-wrap: wrap;
  }
}
.image-item {
  margin: 10rpx;
  border: 2rpx solid #e0e6ff;
  overflow: hidden;
  box-sizing: border-box;
  @include img-box;
  padding: 0;
  position: relative;
  .file-img {
    width: 100%;
    height: 100%;
  }
  &:nth-child(1) {
    margin-left: 0;
  }
}

.uploader-content {
  // @include img-box;
  background: rgb(248, 249, 254);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 2rpx dashed rgb(191, 191, 191);
  color: rgb(140, 140, 140);
  font-size: 24rpx;
  font-weight: 400;
  line-height: 30rpx;

  .uploader-file-icon {
    width: 36rpx;
    height: 36rpx;
    margin-bottom: 16rpx;
    object-fit: cover;
  }
}

.preview-cover {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: #edf4ff;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 8rpx;
  image {
    width: 72rpx !important;
    height: 72rpx !important;
    margin-bottom: 8rpx;
  }

  .name {
    box-sizing: border-box;
    width: 100%;
    font-size: 24rpx;
    font-weight: 400;
    color: #333333;
    line-height: 40rpx;
    @include textoverflow(1);
  }
}
.preview-cover-delete {
  top: 2rpx;
  right: 2rpx;
  width: 32rpx;
  height: 32rpx;
  position: absolute;
  background: url("https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/upload/uploader-close.png")
    center/cover no-repeat;
}
.uploader-tip {
  margin-top: 10rpx;
  color: rgb(170, 170, 170);
  font-size: 24rpx;
  font-weight: 400;
  line-height: 30rpx;
}
