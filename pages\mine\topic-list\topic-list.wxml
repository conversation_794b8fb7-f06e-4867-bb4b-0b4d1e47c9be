<view class="topic-box">
    <view class="add-topic">
      <view class="add-topic-left">
        <view class="add-topic-title">我的话题</view>
        <view class="add-topic-subtitle">
          <!-- <svg-icon name="warning" size="12" color="rgb(233, 178, 99)"></svg-icon> -->
          <image src="/static/icon/warning-circle.png" mode=""/>
          <text class="text">最多添加10个</text>
        </view>
      </view>
      <view class="add-topic-right" wx:if="{{topicList.length < 10}}" bind:tap="editExp">
        <view class="add-icon">
          <!-- <svg-icon name="add-fff" size="8" color="#FFF"></svg-icon> -->
          <image src="/static/icon/add-simplicity-weight.png" mode=""/>
        </view>
        <text class="text">最多添加10个</text>
      </view>
    </view>
    <view class="topic-list" wx:if="{{topicList && topicList.length > 0}}">
      <view boxShadow class="topic-item" wx:for="{{topicList}}" wx:key="{{index}}" bind:tap="editExp" data-id="{{ item.id }}">
        <view class="topic-title">#{{item.title}}</view>
        <!-- <view class="topic-content"  v-html="getHtmlValue(item.content)"> -->
        <view class="topic-content">
          <rich-text nodes="{{item.nodeValue}}"/>
        </view>
        <view class="topic-tips" wx:if="{{item.cueList && item.cueList.length > 0}}">
          <view class="topic-tips-item" wx:for="{{item.cueList}}" wx:key="{{index}}">
            <view class="topic-tips-item-header">
              <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/check-active-large.png" />
              <text>已设置补充信息</text>
            </view>
            <view class="topic-tips-item-value">{{item}}</view>
          </view>
        </view>
        <view class="topic-delete">
          <text class="topic-number">
            <text>已辅导</text>
            <text class="active">{{item.topicAppointNum}}</text>
            <text>人</text>
          </text>
          <view class="delete-btn">
            <van-button custom-class="info-btn" type="info" catch:tap="topicDelte" data-id="{{item.id}}">删除</van-button>
          </view>
          
        </view>
      </view>
    </view>
    <view class="empty" wx:else>
      <custom-empty-new></custom-empty-new>
    </view>
  </view>