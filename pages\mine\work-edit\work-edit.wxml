<view class="exp-edit">

  <van-popup
  show="{{ startDatePopup }}"
  position="bottom"
  bind:close="popupClose"
  >
    <van-picker bind:change="startDateChange" id="startPicker" bind:cancel="popupClose" bind:confirm="startConfirm" show-toolbar columns="{{ startColums }}" />
  </van-popup>

  <van-popup
  show="{{ endDatePopup }}"
  position="bottom"
  bind:close="popupClose"
  >
  <van-picker bind:change="endDateChange" id="endPicker" bind:cancel="popupClose" bind:confirm="endConfirm" show-toolbar columns="{{ endColums }}" />
  </van-popup>
  <view class="exp-content">
    <view class="experience-edit">
      <view class="experience-edit-box">
        <van-field input-align="right" border="{{ false }}" bind:change="onChange" data-key="companyName" model:value="{{ workObject.companyName }}" maxlength="30" label="公司" placeholder="请填写公司名称" />

      <van-field input-align="right" border="{{ false }}" bind:change="onChange" data-key="positionName" model:value="{{ workObject.positionName }}" maxlength="30" label="职位" placeholder="请填写职位名称" />

      <van-field input-align="right" border="{{ false }}" value="{{ workObject.startDate }}" readonly label="开始时间" placeholder="请选择开始时间" is-link bind:tap="showStartPopup"/>

      <van-field input-align="right" border="{{ false }}" value="{{ workObject.endDate }}" readonly label="结束时间" placeholder="请选择结束时间" is-link bind:tap="showEndPopup"/>

        <van-field
        autosize="{{ autosize }}"
        maxlength='1000'
        bind:change="onChange" data-key="professionalIntroduction"
        model:value="{{ workObject.professionalIntroduction }}"
        label="工作内容描述" 
        placeholder="请输入工作内容描述" 
        type="textarea"
        custom-class="custom-textarea"/>
      </view>
    </view>
    <copyright></copyright>
  </view>
  <view class="exp-footer">
    <van-button wx:if="{{id}}" bind:tap="expDelete" custom-class="default-btn" type="default">删除</van-button>
    <van-button wx:else bind:tap="expBack" custom-class="default-btn" type="default">取消</van-button>
    <van-button bind:tap="expEdit" custom-class="info-btn" type="info">保存</van-button>
  </view>
</view>
