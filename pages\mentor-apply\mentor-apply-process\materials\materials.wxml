<mentorInBoxApply sliderValue="{{ 100 }}" bind:pageNext="pageNext" bind:pageBack="pageBack" nextBtnValue="提交">
  <view class="qrcode-dialog" wx:if="{{ showDialog }}">
    <view class="qrcode-dialog-content">
      <view class="title">查看审核结果</view>
      <view class="qrcode">
        <image show-menu-by-longpress="{{true}}" src="{{GZHQRcode}}" mode=""/>
      </view>
      <view class="tips">长按关注，审核结果出来后，将第一时间通过公众号进行推送</view>
      <view class="btns">
        <van-button custom-class="default-btn" type="default" bind:tap="toResult">取消</van-button>
        <van-button custom-class="info-btn" type="info" bind:tap="toResult">确定</van-button>
      </view>
    </view>
  </view>
  <view class="materials">
    <view class="materials-upload">
      <view class="title">
        证明材料
      </view>
      <view class="text">
      为了审核目的，请提交以下真实有效的文件：名片（需显示您的职务）、工作证件、职业资格证书、加盖有单位公章的在职证明、劳动合同以及社保缴费记录。我们承诺对您的信息严格保密。请注意，提供虚假或无效的材料将导致审核失败，进而影响您的申请流程。（仅支持jpg、png图片格式，最多上传20张照片，每张照片大小不得超过10M）
      </view>
      <view class="quetion-file">
        <Uploader
          class="uploader"
          value="{{fileList}}"
          deletable="{{true}}"
          tip=""
          maxSize="{{10*1024*1024}}"
          accept=".jpeg,.jpg,.png,JPEG,JPG,PNG"
          bind:change="onFileChange"
          bind:after-upload="onAfterUpload"
          bind:delete="onFileDelete"
          uploadWord="上传图片"
          img-max-count='{{20}}'
          max-count='{{20}}'
          file-max-count='{{0}}'
        />
      </view>
    </view>
    <view class="commitment">
      <view class="title">服务承诺</view>
      <view class="text">
      成为导师后，需要承诺将对约见涉及内容严格保密。未经学生同意，您在交谈过程中不会进行任何录音、录像等传播或泄露学生隐私(包括学生姓名、公司名称、商业机密等)行为。
      </view>
      <view class="text">
        约见态度:在约见过程中，您承诺将全程保持真诚、尊重、主动、专业的沟通态度，积极推动约见顺利进行。
      </view>
      <view class="text">
        约见准备:您承诺将对「毕师父」约见进行充分准备，包括但限于:约见前了解学生需求并围绕其进行知识准备;准时抵达约见地点或发起约见电话;保证充分的约见时长。
      </view>
      <view class="text">
        信息真实:所有导师的简介，包括过往经历、项目案例等真实有效，不存在虚假资料，如有发现申请资料有弄虚作假将可能影响申请通过。
      </view>
      <view class="text">
        评价可靠:维护评价的真实性，不以任何形式刷单制造虚假好评、不以任何形式索要好评，一经查实可能导致警告、删除等处罚。
      </view>
    </view>
    <view class="agreement">
      <van-checkbox
      value="{{ checked }}"
      bind:change="onChange"
      icon-size="28rpx"
      checked-color="#0C7AFF"
      >
        <view class="text-value">
          我已阅读毕师父服务承诺
        </view>
      </van-checkbox>
    </view>
  </view>
</mentorInBoxApply>
