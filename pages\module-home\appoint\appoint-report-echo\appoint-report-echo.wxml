<view class="report-detail">
  <view class="card-box page-title" wx:if="{{appointDetail.user.userName}}">
    {{appointDetail.user.userName}}的咨询报告
  </view>

  <view class="card-box report-base">
    <view class="header">
      <view class="title">
        <!-- <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/report-icon.png" mode=""/> -->
        预约详情
      </view>
    </view>
    <view class="base-box">
      <view class="base-box-item">
        <text class="base-item-label">撰写人</text>
        <text class="base-item-value textoverflow">{{reportDetail.mentorName}}</text>
      </view>
      <view class="base-box-item">
        <text class="base-item-label">咨询时间</text>
        <text class="base-item-value textoverflow">{{reportDetail.appointDateTime}}</text>
      </view>
      <view class="base-box-item" wx:if="{{reportDetail.appointType === 10}}">
        <text class="base-item-label">咨询时长</text>
        <text class="base-item-value textoverflow">{{reportDetail.appointDateValue}}分钟</text>
      </view>
      <view class="base-box-item" wx:if="{{reportDetail.appointType === 20}}">
        <text class="base-item-label">咨询时长</text>
        <text class="base-item-value textoverflow">1:00:00</text>
      </view>
      <view class="base-box-item">
        <text class="base-item-label">预约方式</text>
        <text class="base-item-value textoverflow">{{reportDetail.appointTypeText}}</text>
      </view>
    </view>
  </view>

  <view class="card-box question-directive">
    <view class="header">问题描述</view>
    <view class="question-content">
      <view class="appoint-describe">
      {{reportDetail.appointLogProblem.problemDescription.other}}
      </view>
      <view class="appoint-describe-file" wx:if="{{reportDetail.appointLogProblem.accessoryName && reportDetail.appointLogProblem.accessoryUrl}}" bind:tap="previewFile">
        <image class="file-icon" src="{{reportDetail.fileIconType}}" />
        <view class="file-name">{{reportDetail.appointLogProblem.accessoryName}}</view>
      </view>
      <view class="appoint-describe-image" wx:if="{{reportDetail.appointLogProblem.picUrlList && reportDetail.appointLogProblem.picUrlList.length > 0}}">
        <image wx:for="{{reportDetail.appointLogProblem.picUrlList}}" wx:key="{{index}}" src="{{item}}" mode="aspectFill" bind:tap="showImagePreview1" data-index="{{index}}"/>
      </view>

      <view class="appoint-describe-supplementary" v-if="{{reportDetail.appointLogCueList && reportDetail.appointLogCueList.length > 0}}">
        <view class="appoint-describe-supplementary-title">补充信息</view>
        <view class="appoint-describe-supplementary-item" wx:for="{{reportDetail.appointLogCueList}}" :key="{{index}}">
          <view class="appoint-describe-supplementary-label">{{item.problem}}</view>
          <view class="appoint-describe-supplementary-value">{{item.answer}}</view>
        </view>
      </view>
    </view>
  </view>

  <view class="card-box appoint-course">
    <view class="header">咨询过程记录</view>
    <view class="appoint-course-content">
      <rich-text class="mentor-introduce {{unfoldClose ? '' : 'show-all-text'}}" nodes="{{htmlSnip}}"></rich-text>
      <rich-text class="mentor-introduce mentor-introduce-position" nodes="{{htmlSnip}}"></rich-text>
      <view class="unfold" wx:if="{{showUnfold}}" bind:tap="unfoldChange">
        <image class="{{unfoldClose ? '' : 'arrow-top'}}" src="/static/icon/arrow-bottom-blue.png" mode="aspectFill"/>
      </view>
    </view>
    <view class="appoint-course-record" wx:if="{{appointDetail.appointType === 10 && audioList.length > 0}}">
      <view class="report-record-record-record-item" wx:for="{{audioList}}" :key="{{index}}">
        <view class="report-record-record-record-text">咨询录音{{index + 1}}</view>
        <view class="report-record-record-record-progress">
          <image class="operation" bind:tap="recordClick" data-index="{{index}}" src="{{(audioPlayObject.audioPlayIndex === index) && audioPlayObject.isPlay ? 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/appoint/record-play.png' : 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/appoint/record-pause.png'}}" mode=""/>
          <div class="progress">
            <van-slider
            value="{{item.audioProgress}}" 
            bind:change="audioChange"
            data-index="{{index}}"
            bar-height="4rpx" 
            active-color="#0057FF"
            inactive-color="rgb(231, 231, 231)"
            button-size="18rpx"
            />
          </div>
          <view class="record-time">{{item.durationFormat}}</view>
          <image class="icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/appoint/record-menu.png" mode="" /> 
        </view>
        <!-- <audio @play="audioPlay(index)" @pause="audioPause(index)" @ended="audioEnded(index)" @timeupdate="audioProgress(index)" :id="'audioList' + index" class="audioListValue" :src="item.recordingUrl"></audio> -->
        <!-- <div class="report-record-record-record-progress">
          <img @click="recordClick(index)" class="operation" :src="item.audioPlayStatus ? require('@/assets/images/appoint/record-play.png') : require('@/assets/images/appoint/record-pause.png')" alt="">
          <div class="progress">
            <van-slider 
            v-model="item.audioProgress" 
            @change="value => audioChange(value, index)"
            bar-height="2px" 
            active-color="#0057FF"
            inactive-color="rgb(231, 231, 231)"
            button-size="9px"
            />
          </div>
          <div class="record-time">{{item.durationFormat}}</div>
          <img class="icon" src="~@/assets/images/appoint/record-menu.png" alt=""> 
        </div> -->
      </view>
    </view>
  </view>

  <view class="card-box question-directive">
    <view class="header">导师给出的方案或建议</view>
    <view class="question-content">
      <view class="appoint-describe">
      <!-- {{reportDetail.appointLogProblem.problemDescription.other}} -->

        <rich-text class="mentor-introduce2 {{unfoldClose2 ? '' : 'show-all-text'}}" nodes="{{htmlSnip2}}"></rich-text>
        <rich-text class="mentor-introduce2 mentor-introduce-position2" nodes="{{htmlSnip2}}"></rich-text>
        <view class="unfold" wx:if="{{showUnfold2}}" bind:tap="unfoldChange2">
          <image class="{{unfoldClose2 ? '' : 'arrow-top'}}" src="/static/icon/arrow-bottom-blue.png" mode="aspectFill"/>
        </view>
      </view>
      <view class="appoint-describe-file" wx:if="{{reportDetail.suggestionSummaryAttachmentName && reportDetail.suggestionSummaryAttachmentUrl}}" bind:tap="downLoadFileSummer">
        <image class="file-icon" src="{{reportDetail.fileIconType2}}" />
        <view class="file-name">{{reportDetail.suggestionSummaryAttachmentName}}</view>
      </view>
      <view class="appoint-describe-image" wx:if="{{reportDetail.suggestionSummaryImgUrlList && reportDetail.suggestionSummaryImgUrlList.length > 0}}">
        <image wx:for="{{reportDetail.suggestionSummaryImgUrlList}}" wx:key="{{index}}" src="{{item}}" mode="aspectFill" bind:tap="showImagePreview" data-index="{{index}}"/>
      </view>
    </view>
  </view>
</view>