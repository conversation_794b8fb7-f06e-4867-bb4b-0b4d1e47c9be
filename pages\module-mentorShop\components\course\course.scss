.course-list {
  .course-item {
    padding: 36rpx 0 28rpx;
    display: flex;
    align-items: center;
    width: 100%;
    flex: 1;
    // border-bottom: 2rpx solid #c4c4c4;
    .image-wrapper {
      position: relative;
      width: 244rpx;
      height: 136rpx;
      .course-image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }
    }

    .course-info {
      margin-left: 16rpx;
      flex: 1;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .course-meta {
        margin-bottom: 10rpx;
        flex: 1;
        flex-shrink: 0;
      
        .course-title {
          word-break: break-all;
          color: #333333;
          font-size: 26rpx;
          font-weight: bold;
          line-height: 30rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        .course-title-select {
          // width: 130rpx;
        }
      }

      .right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        flex-shrink: 0;
        margin-left: 20rpx;
      }

      .rating {
        display: flex;
        align-items: center;
        .score,
        .value {
          color: #0057ff;
          font-size: 20rpx;
        }

        .score {
          color: #9ea3ac;
        }
      }

      .duration,
      .student-count {
        font-size: 24rpx;
        color: #9ea3ac;
        line-height: 28rpx;
        margin-top: 14rpx;
      }

      .student-count {
        margin-top: 26rpx;
      }

      .price-box {
        margin-top: 16rpx;
        display: flex;
        align-items: baseline;
        font-weight: bold;
        color: #ff0019;

        .currency {
          font-size: 24rpx;
          margin-right: 2rpx;
        }

        .price {
          font-size: 40rpx;
        }
      }

      .right-bottom {
        margin-top: 14rpx;
        color: #ffffff;
        font-size: 24rpx;
        font-weight: 500;
        background-color: #ff0019;
        border-radius: 8rpx;
        text-align: center;
        padding: 2rpx 16rpx;
      }
    }
  }
}

.course-list-select {
  padding: 0rpx 36rpx;
  border-bottom: 2rpx solid #dfdfdf;
}
.course-list-select:last-child {
  border-bottom: 0;
}
.course-item-select {
  padding: 14rpx 36rpx;
  margin-bottom: 0;
  .course-number {
    color: #aaaaaa;
    font-size: 22rpx;
    margin: 12rpx 0 22rpx;
    image {
      width: 23rpx;
      height: 23rpx;
      margin-left: 6rpx;
      vertical-align: bottom;
    }
  }
  .number {
    border: 2rpx solid #0057ff;
    background-color: #f8f9fe;
    border-radius: 8rpx;
    color: #0057ff;
    font-size: 22rpx;
    line-height: 26rpx;
    padding: 4rpx 12rpx;
  }
}
