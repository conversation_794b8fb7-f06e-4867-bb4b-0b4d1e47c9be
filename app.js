// app.js
import { connectWebSocket } from "~/utils/websocket/websocketService"
import { getTenantInfo } from "~/api/user";
import { env } from '~/utils/env'
App({
  onLaunch() {
    if (wx.getStorageSync('token') && wx.getStorageSync('identity') === 'mentor') {
      this.getTenantInfo()
    }
  },
  async getTenantInfo() {
    const { data } = await getTenantInfo({ tenantId: env.Tenant_id });
    if (data && data.id) {
      wx.setStorageSync('tenantInfo', data)
      if (data && data.tenantFunction.length) {
        connectWebSocket(data.tenantFunction)
      }
    }
  },
  globalData: {
    // userInfo: null,
    // tenantInfo: null,
    websocket: null,
    pageList: []
  },
});
