<view class="dynamics-container dynamics-component" style="padding:{{padding}}">
  <view class="dynamics-list">
    <view class="dynamics-item" wx:for="{{limitedDynamicsList}}" wx:key="index" data-id="{{item.id}}" bindtap="handleDynamicsItem" data-id="{{item.id}}">
      <view class="user-info">
        <view class="left">
          <image class="avatar" src="{{item.mentorBase.user.avatarPath || item.studentAvatarPath}}" mode="aspectFill"></image>
          <view class="info">
            <view class="name">
              <text>{{item.mentorBase.user.userName|| item.studentName}}</text>
              <mentor-identity name="{{item.mentorBase.user.identityName}}"></mentor-identity>
            </view>
            <view class="title" wx:if="{{isTeacher}}">
              <view>{{item.mentorBase.companyName}}</view>
              <view>{{item.mentorBase.positionName}}</view>
            </view>
          </view>
        </view>

        <view class="right" wx:if="{{showFollowStatus}}">
          <view class="comment-operation" wx:if="{{item.showStatus === 0}}" catch:tap="handleCommentOperation" data-item="{{item}}" data-index="{{index}}">
            <text>已隐藏</text>
            <view class="circle"></view>
          </view>
          <view class="comment-operation comment-operation-down" wx:else catch:tap="handleCommentOperation" data-item="{{item}}" data-index="{{index}}">
            <view class="circle"></view>
            <text>隐藏评价</text>
          </view>
          <!-- <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/{{item.type === 'image' ?'followed':'not-followed'}}.png"></image>
          <van-button custom-class="{{item.type === 'image' ? 'interest' : ''}}" type="primary">{{item.type === 'image' ? '已关注' : '关注导师'}}</van-button> -->
        </view>
      </view>
      <view class="audio-container" wx:if="{{item.dynamicAudio}}" catch:tap="stopPropagation">
        <view class="audio-player">
          <view class="play-control">
            <view class="play-btn-wrapper" catch:tap="recordClick" data-index="{{index}}">
              <image class="play-btn" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/{{(audioPlayObject.audioPlayIndex === index) && audioPlayObject.isPlay ? 'mentorShop/pause.png' : 'mentorShop/play.png'}}"></image>
            </view>
            <view class="progress-bar">
              <van-slider custom-class="progress" use-button-slot active-color="#0057FF" inactive-color="rgb(231, 231, 231)" value="{{item.audioInfo.audioProgress}}" catch:change="audioChange" data-index="{{index}}">
                <view class="custom-button" slot="button"></view>
              </van-slider>
            </view>
            <text class="duration">{{item.formattedDuration}}</text>
          </view>
          <view class="audio-info">
            <image class="headphone-icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/headset.png"></image>
            <text class="play-count">{{item.audioPlayCount}}</text>
          </view>
        </view>
      </view>
      <view class="content" wx:if="{{item.courseId || item.dynamicContent}}">
        <view class="content-text content-text-{{index}} text-ellipsis {{expandMap[index] ? 'is-expanded' : ''}}" id="content-{{index}}">
          <text wx:if="{{item.courseId}}">这是我发布的课程，快来试试吧！</text>
          <text wx:elif="{{item.dynamicContent}}">{{ item.dynamicContent }}</text>
          <text wx:elif="{{!item.dynamicContent && !item.dynamicAudio }}">此用户没有填写评价内容</text>
        </view>
        <view class="expand-btn" catch:tap="handleExpand" data-index="{{index}}" wx:if="{{needExpand[index]}}">
          <van-icon name="arrow-down" color='#0057FF' class="{{expandMap[index] ? 'arrow-up' : ''}}" />
          {{expandMap[index] ? '收起' : '展开'}}
        </view>
      </view>
      <view class="media-list" wx:if="{{item.dynamicImages.length>0}}">
        <image class="media-item" wx:for="{{item.parsedImages}}" wx:for-item="imageUrl" wx:key="index" src="{{imageUrl}}" mode="aspectFill" data-urls="{{item.parsedImages}}" data-current="{{imageUrl}}" catch:tap="previewImage"></image>
      </view>
      <view class="video-container" wx:if="{{item.course}}">
        <view class="video-wrapper" catch:tap="handleVideoClick" data-course="{{item.course}}">
          <view class="video-card">
            <image class="video-cover" src="{{item.course.courseCoverUrl}}" mode="aspectFill"></image>
            <van-icon class="play-btn" wx:if="{{item.course.courseType === 10}}" size="30px" color='white' name="play-circle-o" />
          </view>
          <view class="video-info">
            <view class="video-title">{{item.course.courseName}}</view>
            <view class="video-desc">这是我发布的课程，快来试试吧！</view>
          </view>
        </view>
      </view>
      <view class="video-dynamicVideo" wx:if="{{item.dynamicVideo}}">
        <video id="myVideo{{index}}" src="{{item.dynamicVideo}}" show-center-play-btn="{{false}}" mode="aspectFill" object-fit="cover" controls="{{false}}"></video>
        <van-icon custom-class='van-icon-play-circle-o' name="play-circle-o" size='40px' catch:tap="previewVideo"/>
      </view>

      <view class="interaction">
        <view class="date" wx:if="{{item.videoAuditStatus === 20}}">{{item.createdAt}}
          <image wx:if="{{isDelete}}" catch:tap="handleDelete" data-id="{{item.id}}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/delete.png" mode="aspectFill" />
        </view>
        <view wx:else>
          <view class="examine" wx:if="{{item.videoAuditStatus === 10}}">
            <text>审核中</text>
            <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/webUpload/mentor/examine.png" mode="aspectFill" />
          </view>
          <view class="examine" wx:if="{{item.videoAuditStatus === 30}}">
            <text class="prompt-fail">审核失败</text>
            <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/webUpload/mentor/prompt.png" mode="aspectFill" catch:tap="promptReason"/>
          </view>
        </view>
        <view class="stats">
          <view class="stat-item" wx:if="{{isTeacher}}">
            <image class="icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/look.png"></image>
            <text>{{item.viewCount}}</text>
          </view>
          <view class="stat-item" catch:tap="handleThumbUp" data-index="{{index}}">
            <image class="icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/{{item.isThumbUp?'home/like-icon.png':'appoint/like-fff.png'}}"></image>
            <text>{{item.thumbUpCount}}</text>
          </view>
          <view class="stat-item" wx:if="{{isTeacher}}">
            <view class="stat-item" catch:tap="handleComment" data-id="{{item.id}}">
              <image class="icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/comment.png"></image>
              <text wx:if="{{totalFlag}}">{{totalReplies}}</text>
              <text wx:else>{{item.commentCount}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view wx:if="{{isCourseComment && limitedDynamicsList.length > 2}}" catch:tap="reviews" class="all">查看全部评价</view>
  </view>
  <view class="video-preview" wx:if="{{showPreviewVideo && currentVideoIndex === index}}">
    <view class="video-container">
      <view class="close-btn" catch:tap="closePreview">
        <van-icon name="cross" size="40rpx" color="#fff" />
      </view>
      <video 
      id="fullscreenVideo"
      src="{{item.dynamicVideo}}" 
      controls 
      autoplay 
      show-play-btn 
      style="width:100vw;height:100vh"
      ></video>
    </view>
  </view>
</view>