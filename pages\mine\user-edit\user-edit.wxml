<view class="user-edit">
  <van-popup show="{{ showCrop }}" bind:close="cancelShowCrop" custom-style="height: 100%; width: 100%">
    <crop imgFile="{{imgFile}}" upLoadFlag="{{true}}" bind:onCropperDone="handleCropperDone" bind:onCancel="cancelShowCrop"></crop>
  </van-popup>

  <view class="user-header">
    <view class="user-edit-left">
      <view class="user-upload" bindtap="chooseImage">
        <view class="avatar">
          <image src="{{userInfo.mentorBase.user.avatarPath}}" mode="aspectFill"/>
        </view>
        <view class="user-name">
          <view class="name textoverflow">{{userInfo.mentorBase.user.userName}}</view>
          <view class="tips">点击修改头像</view>
        </view>
      </view>
    </view>
    <view class="user-edit-right" bind:tap="toPlacardSelect">
      <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/qrcode.png" mode="aspectFill"/>
    </view>
  </view>
  <view class="card-box">
    <view class="header padding-bottom">
      <view class="name">我的资料</view>
      <view class="operation" bind:tap="editBase">
        <text>编辑</text>
        <image src="/static/icon/arrow-right-gray.png" mode=""/>
      </view>
    </view>
    <view class="base-info-content">
      <view class="base-info-item">
        <view class="base-info-item-label">真实姓名</view>
        <view class="base-info-item-value textoverflow">{{userInfo.mentorBase.user.userName || '-'}}</view>
      </view>
      <view class="base-info-item">
        <view class="base-info-item-label">擅长领域</view>
        <view class="base-info-item-value base-info-item-value-field textoverflow" wx:if="{{userInfo.mentorSkillFieldList && userInfo.mentorSkillFieldList.length > 0}}">
          <!-- <text>1111</text> -->
          <text wx:for="{{userInfo.mentorSkillFieldList}}" wx:key="{{item.id}}">
            <text>{{item.skillFieldName}}</text>
            <text wx:if="{{index < userInfo.mentorSkillFieldList.length - 1}}">/</text>
          </text>
        </view>
        <view class="base-info-item-value base-info-item-value-field" wx:else>-</view>
      </view>
      <view class="base-info-item">
        <view class="base-info-item-label">工作单位</view>
        <view class="base-info-item-value textoverflow">{{userInfo.mentorBase.companyName || '-'}}</view>
      </view>
      <view class="base-info-item">
        <view class="base-info-item-label">职位名称</view>
        <view class="base-info-item-value textoverflow">{{userInfo.mentorBase.positionName || '-'}} </view>
      </view>
      <view class="base-info-item">
        <view class="base-info-item-label">身份证号</view>
        <view class="base-info-item-value textoverflow">{{userInfo.mentorBase.identityNumber || '-'}}</view>
      </view>
      <view class="base-info-item">
        <view class="base-info-item-label">银行卡号</view>
        <view class="base-info-item-value textoverflow">{{userInfo.mentorBase.bankCardNumber || '-'}}</view>
      </view>
      <view class="base-info-item">
        <view class="base-info-item-label">银行卡开户行</view>
        <view class="base-info-item-value textoverflow">{{userInfo.mentorBase.bankCardOpeningBank || '-'}}</view>
      </view>
      <view class="base-info-item base-info-item-textarea">
        <view class="base-info-item-label">自我介绍</view>
        <!-- <view class="base-info-item-textarea" v-if="userInfo.mentorBase" v-html="getHtmlValue(userInfo.mentorBase.introduction)"></view> -->
        <view class="base-info-item-textarea-value" wx:if="{{userInfo.mentorBase}}">
          <rich-text nodes="{{userInfo.mentorBase.nodeValue}}"/>
        </view>
      </view>
    </view>
  </view>
  <view class="card-box">
    <view class="header">
      <view class="name">工作经历</view>
      <view class="add-more" bind:tap="editWork">
        <image src="/static/icon/add-btn-fill.png" mode=""/>
        <text>添加</text>
      </view>
    </view>
    <view class="experience-content" wx:if="{{ userInfo.mentorProfessionalList && userInfo.mentorProfessionalList.length > 0 }}">
      <view class="experience-item" wx:for="{{ userInfo.mentorProfessionalList }}" wx:key="{{ index }}" bind:tap="editWork" data-id="{{ item.id }}">
        <view class="experience-item-title">
          <view class="experience-item-title-value">{{item.companyName}}</view>
          <view class="experience-item-title-edit">
            <text class="text">编辑</text>
            <!-- <svg-icon name="arrow-right" size="8"></svg-icon> -->
            <image src="/static/icon/arrow-right-gray.png" mode=""/>
          </view>
        </view>
        <view class="experience-item-position">
          <!-- {{item.positionName}} -->
          {{item.positionName}}
        </view>
        <view class="experience-item-date">{{item.startDate}}-{{item.endDate}}</view>
        <view class="experience-item-discribe">{{item.professionalIntroduction}}</view>
      </view>
    </view>
    <view class="empty" wx:else>
      <custom-empty-new></custom-empty-new>
    </view>
  </view>

  <view class="card-box">
    <view class="header">
      <view class="name">教育经历</view>
      <view class="add-more" bind:tap="editEducation">
        <image src="/static/icon/add-btn-fill.png" mode=""/>
        <text>添加</text>
      </view>
    </view>
    <view class="experience-content" wx:if="{{ userInfo.mentorEducationList && userInfo.mentorEducationList.length > 0 }}">
      <view class="experience-item" wx:for="{{ userInfo.mentorEducationList }}" wx:key="{{ index }}" bind:tap="editEducation" data-id="{{ item.id }}">
        <view class="experience-item-title">
          <view class="experience-item-title-value">{{item.universityName}}</view>
          <view class="experience-item-title-edit">
            <text class="text">编辑</text>
            <!-- <svg-icon name="arrow-right" size="8"></svg-icon> -->
            <image src="/static/icon/arrow-right-gray.png" mode=""/>
          </view>
        </view>
        <view class="experience-item-position">
          <view class="major">{{item.majorName}}</view>
          <view class="level">{{item.educationLevelText}}</view>
        </view>
        <view class="experience-item-date">{{item.startDate}}-{{item.endDate}}</view>
      </view>
    </view>
    <view class="empty" wx:else>
      <custom-empty-new></custom-empty-new>
    </view>
  </view>
  
  <view class="copyright">
    <copyright></copyright>
  </view>
  <view class="button">
    <van-button custom-class="info-btn" type="info" bind:tap="toResetPassword">设置密码</van-button>
    <van-button custom-class="info-btn" type="info" bind:tap="handleLoginOut">退出登录</van-button>
  </view>
</view>