<view class="appoint-detail">

  <view class="appoint-detail-content">

    <view class="card-box page-title" wx:if="{{appointDetail.user.userName}}">
      {{appointDetail.user.userName}}的咨询报告
    </view>

    <view class="card-box" wx:if="{{appointDetail.mentorTopicSnapshot}}">
      <view class="header">
        <view class="title">
          <!-- <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/report-icon.png" mode=""/> -->
          预约详情
        </view>
      </view>
      <view class="appoint-base">
        <view class="appoint-base-item">
          <view class="appoint-base-item-label">撰写人</view>
          <view class="appoint-base-item-value" wx:if="{{appointDetail.mentorBase}}">{{appointDetail.mentorBase.user.userName}}</view>
        </view>
        <view class="appoint-base-item">
          <view class="appoint-base-item-label">咨询时间</view>
          <view class="appoint-base-item-value">
            {{appointDetail.appointDateTime}}
          </view>
        </view>
        <view class="appoint-base-item" wx:if="{{appointDetail.appointType === 10 && appointDetail.secretReport}}">
          <view class="appoint-base-item-label">咨询时长</view>
          <view class="appoint-base-item-value">{{Math.ceil(appointDetail.secretReport.sumDuration / 60)}}分钟</view>
        </view>
        <view class="appoint-base-item" wx:if="{{appointDetail.appointType === 20}}">
          <view class="appoint-base-item-label">咨询时长</view>
          <view class="appoint-base-item-value">1:00:00</view>
        </view>
        <view class="appoint-base-item">
          <view class="appoint-base-item-label">预约方式</view>
          <view class="appoint-base-item-value">{{appointDetail.appointTypeText}}</view>
        </view>
      </view>
    </view>

    <view class="card-box" wx:if="{{appointDetail.appointLogProblem}}">
      <view class="header">
        <view class="title">
          问题描述
        </view>
      </view>
      <view class="appoint-detail-file">
        <view class="question-directive" wx:if="{{appointDetail.appointLogProblem && appointDetail.appointLogProblem.problemDescription}}">{{appointDetail.appointLogProblem.problemDescription.other}}</view>
        <view class="file-type" bind:tap="previewFile" wx:if="{{appointDetail.appointLogProblem.accessoryName && appointDetail.appointLogProblem.accessoryUrl}}">
          <image src="{{appointDetail.fileTypeImage}}" mode="aspectFill"/>
          <text wx:if="{{appointDetail.appointLogProblem}}">{{appointDetail.appointLogProblem.accessoryName}}</text>
        </view>
        <view class="image-type-list" wx:if="{{appointDetail.appointLogProblem && appointDetail.appointLogProblem.picUrlList && appointDetail.appointLogProblem.picUrlList.length > 0}}">
          <view class="image-type" wx:for="{{appointDetail.appointLogProblem.picUrlList}}" wx:key="{{index}}" bind:tap="imgPreview" data-item="{{item}}">
            <image src="{{item}}" mode="aspectFill"/>
          </view>
        </view>

        <view class="appoint-describe-supplementary" wx:if="{{appointDetail.mentorTopicSnapshot && appointDetail.mentorTopicSnapshot.appointLogCueList && appointDetail.mentorTopicSnapshot.appointLogCueList.length > 0}}">
            <view class="appoint-describe-supplementary-title">补充信息</view>
            <view class="appoint-describe-supplementary-item" wx:for="{{appointDetail.mentorTopicSnapshot.appointLogCueList}}" :key="{{index}}">
              <view class="appoint-describe-supplementary-label">{{item.problem}}</view>
              <view class="appoint-describe-supplementary-value">{{item.answer}}</view>
            </view>
          </view>
      </view>
    </view>

    <view class="card-box">
      <view class="header required">
        <view class="title">
          咨询过程记录
        </view>
      </view>
      <view class="appoint-history">
        <van-field
            autosize="{{ autosize }}"
            maxlength='2000'
            model:value="{{ appointHistory }}"
            placeholder="请输入咨询过程中的关键信息" 
            type="textarea"
            custom-class="custom-textarea"
          />
      </view>
    </view>

    <view class="card-box">
      <view class="header required">
        <view class="title">
          请针对咨询问题给出您的建议
        </view>
      </view>
      <view class="appoint-history">
        <van-field
            autosize="{{ autosize }}"
            maxlength='1000'
            model:value="{{ suggest }}"
            placeholder="请输入建议内容" 
            type="textarea"
            custom-class="custom-textarea"
          />
      </view>
      <view class="quetion-file">
        <Uploader
          class="uploader"
          value="{{fileList}}"
          deletable="{{true}}"
          tip="最多上传3张图片与1份附件"
          maxCount="{{3}}"
          maxSize="{{10*1024*1024}}"
          accept=".jpeg,.jpg,.png,.pdf,.doc,.docx,.xls,.xlsx"
          bind:change="onFileChange"
          bind:after-upload="onAfterUpload"
          bind:delete="onFileDelete"
          uploadWord="上传图片/附件"
          img-max-count='{{3}}'
          file-max-count='{{1}}'
        />
      </view>
    </view>
  </view>
  <view class="appoint-detail-footer">
    <van-button custom-class="info-btn" type="info" bind:tap="sendReport">发送报告</van-button>
  </view>
</view>