// pages/appoint/personal-letter/personal-letter.js
import { msgSend, msgDetail } from '~/api/im'
import { getStudentInfo } from '~/api/user'
// import dayjs from "dayjs";
import moment from 'moment'
import { getAndResetNewMessageFlag } from '~/utils/websocket/globalMessageWatcher';
import eventBus from '~/utils/websocket/eventBus';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    inProgress: false,
    toView: '',
    msgList: [],
    total: 0,
    loading: true,
    finished: false,
    page: {
      pageNumber: 0,
      pageSize: 100
    },
    studentId: '',
    studentInfo: {},
    scrollTop: 999999,
    fieldAutoSize: {
      maxHeight: 150
    },
    msgValue: '',
    img: 'https://img0.baidu.com/it/u=855192693,2269837233&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1737046800&t=f984fff45d7bce5ff5a31bc2999a1f02'
  },
  toStudentDetail() {
    wx.navigateTo({
      url: `/pages/personal-letter/student-detail/student-detail?studentId=${this.data.studentId}`,
    })
  },
  // 文件预览
  msgClick(event) {
    if (event.currentTarget.dataset.value.showFileType) {
      wx.downloadFile({
        url: event.currentTarget.dataset.value.content,
        success: (res) => {
          if (res.statusCode === 200) {
            wx.openDocument({
              filePath: res.tempFilePath,
              showMenu: true,
              success: () => {
                // resolve({
                //   success: true,
                //   filePath: res.tempFilePath
                // });
              },
              fail: (err) => {
                // reject(new Error('打开文档失败：' + err.errMsg));
              }
            });
          } else {
            // reject(new Error('下载失败，状态码：' + res.statusCode));
          }
        },
        fail: (err) => {
          // reject(new Error('下载失败：' + err.errMsg));
        }
      });
    } else if(event.currentTarget.dataset.value.showImageType) {
      const arr = []
      this.data.msgList.forEach(item => {
        if (item.showImageType) {
          arr.unshift(item.content)
        }
      })
      wx.previewImage({
        current: event.currentTarget.dataset.value.content, // 当前显示图片的http链接
        urls: arr
      })
    } else {
      return
    }
  },
  // 获取导师详情
  async getStudentInfo() {
    const { data } = await getStudentInfo({
      userId: this.data.studentId
    })
    this.setData({
      studentInfo: data || {}
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      studentId: options.toUserId
    })
    this.getStudentInfo()
    
    this.getRecordList(true)
    // if (getAndResetNewMessageFlag()) {
    //   this.getRecordList(true)
    // }
    // 继续监听新消息
    eventBus.on('onNewMessage', (message) => {
      // this.getRecordList(true)
      if (message.data === Number(this.data.studentId)) {
        this.getNewMessage()
      }
    });
  },
  getFileIcon(fileUrl) {
    if (fileUrl.includes('doc') || fileUrl.includes('docx')) {
      return 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/job-icon-word.png'
    } else if (fileUrl.includes('pdf')) {
      return 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/job-icon-word.png'
    } else if (fileUrl.includes('xls') || fileUrl.includes('xlsx')) {
      return 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/job-icon-word.png'
    } else if (fileUrl.includes('ppt') || fileUrl.includes('pptx')) {
      return 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/job-icon-word.png'
    }
  },
  // scrollToTop() {
  //   this.getRecordList()
  // },
  // 滚动事件监听
  onScroll(event) {
    const { scrollTop } = event.detail;
    if (scrollTop < 3000 && !this.data.loading && !this.data.finished) {
      // 触发上拉加载
      // this.loadMoreMessages();
      this.getRecordList()
    }
  },
  // cancelNodePosition() {
  //   this.setData({
  //     toView: ''
  //   });
  // },
  toNodePosition() {
    this.setData({
      toView: `node${this.data.page.pageNumber - 1}`
    });
    this.setData({
      toView: ''
    });
  },
  toBottom() {
    
    wx.nextTick(() => {
      this.setData({
        scrollTop: 99999 + Math.round(Math.random() * 1000000)
      })
      // this.setData({
      //   toView: `scroll-bottom`
      // });
      // this.setData({
      //   toView: ''
      // });
    })
  },
  messageChange(event) {
    this.setData({
      msgValue: event.detail
    })
  },
  sendMessage() {
    if (!this.data.msgValue) {
      wx.showToast({
        title: '请输入内容',
        icon: 'none'
      })
      return
    }
    if (this.data.inProgress) {
      wx.showToast({
        title: '消息发送中',
        icon: 'none'
      })
      return
    }
    this.send()
  },
  // 发送消息
  async send(fileName, fileUrl) {

    try {
      this.setData({
        inProgress: true
      })
      const { success } = await msgSend({
        toUserId: this.data.studentId,
        content: this.data.msgValue,
        msgType: 10
      })
      if (success) {
        const obj = {
          id: Math.random(),
          msgType: 10,
          content: this.data.msgValue,
          isSelf: true,
          createdAt: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          type: 1
        }
        const arr = this.data.msgList
        arr.unshift(obj)
        // this.msgList.push(obj)
        this.setData({
          msgList: arr,
          msgValue: ''
        })
        wx.nextTick(() => {
          this.setData({
            inProgress: false
          })
        })
      } else {
        wx.nextTick(() => {
          this.setData({
            inProgress: false
          })
        })
      }
    } catch (error) {
      console.log(error)
      wx.nextTick(() => {
        this.setData({
          inProgress: false
        })
      })
    }
    this.toBottom()
    // const type = typeof fileUrl === 'string'
    // try {
    //   const content = type ? fileUrl : this.data.msgValue
    //   const msgType = type ? 20 : 10
    //   const contentDesc = type ? fileName : ''
    //   const { success } = await msgSend({
    //     toUserId: this.data.studentId,
    //     content,
    //     msgType,
    //     contentDesc
    //   })
    //   if (success) {
    //     const obj = {
    //       id: Math.random(),
    //       content,
    //       msgType,
    //       contentDesc,
    //       isSelf: true,
    //       createdAt: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    //       type: 1
    //     }
    //     if (obj.msgType == 20 && /\.(doc|docx|pdf|xls|xlsx|ppt|pptx)$/.test(obj.content)) {
    //       try {
    //         obj.showFileType = true
    //         obj.fileTypeIcon = this.getFileIcon(obj.content)
    //         obj.fileDate = dayjs(obj.createdAt).format('YYYY.MM.DD')
    //       } catch(err) {
    //         console.log(err, 'err')
    //       }
    //     }
    //     if (obj.msgType === 20 && /\.(jpg|jpeg|png)$/.test(obj.content)) {
    //       obj.showImageType = true
    //     }
    //     const arr = this.data.msgList
    //     arr.push(obj)
    //     this.setData({
    //       msgList: arr
    //     })
    //     if (!type) {
    //       this.setData({
    //         msgValue: ''
    //       })
    //     }
    //   }
    // } catch (error) {}
    // this.toBottom()
  },
  async getNewMessage() {
    const { data, paging } = await msgDetail({ 
      pageNumber: 1,
      pageSize: 10,
      toUserId: this.data.studentId
    })
    let value = data[data.length - 1]
    if (value.msgType == 20 && /\.(doc|docx|pdf|xls|xlsx|ppt|pptx)$/.test(value.content)) {
      value.showFileType = true
      value.fileTypeIcon = this.getFileIcon(value.content)
      value.fileDate = moment(value.createdAt).format('YYYY.MM.DD')
    }
    if (value.msgType === 20 && /\.(jpg|jpeg|png)$/.test(value.content)) {
      value.showImageType = true
    }
    this.setData({
      total: paging.total + 1
    })
    this.setData({
      msgList: [value, ...this.data.msgList]
    })
    this.toBottom()
  },
  // 获取聊天记录
  async getRecordList(insertAtEnd) {
    try {
      if (!insertAtEnd) {
        if (this.data.loading) {
          return
        }
      }
      if (this.data.finished) {
        return
      }
      this.setData({
        'page.pageNumber': this.data.page.pageNumber + 1,
        loading: true
      })
      // 调用接口获取新消息
      const { data, paging } = await msgDetail({ ...this.data.page, toUserId: this.data.studentId })
      // this.total = paging.total

      if (data && data.length > 0) {
        data.forEach((item, index) => {
          // if (index === 0) {
          //   item.postionNodeId = `node${this.data.page.pageNumber}`
          // }
          if (item.msgType == 20 && /\.(doc|docx|pdf|xls|xlsx|ppt|pptx)$/.test(item.content)) {
            item.showFileType = true
            item.fileTypeIcon = this.getFileIcon(item.content)
            item.fileDate = moment(item.createdAt).format('YYYY.MM.DD')
          }
          if (item.msgType === 20 && /\.(jpg|jpeg|png)$/.test(item.content)) {
            item.showImageType = true
          }
        })
        this.setData({
          total: paging.total
        })
        // 检查并去除重复数据
        // const msgList = this.data.msgList.filter(existingMsg => existingMsg.type !== 1)
        // this.setData({
        //   msgList: msgList
        // })
        const uniqueNewData = data.filter(newMsg => !this.data.msgList.some(existingMsg => existingMsg.id === newMsg.id))
        const newUniqueNewData = uniqueNewData.reverse()
        if (insertAtEnd) {
          this.setData({
            msgList: [...this.data.msgList, ...newUniqueNewData]
          })
          this.toBottom()
        } else {
          // this.toNodePosition()
          this.setData({
            msgList: [...this.data.msgList, ...newUniqueNewData]
          })
          // this.toNodePosition()
        }
        wx.nextTick(() => {
          this.setData({
            loading: false
          })
        })
      } else {
        wx.nextTick(() => {
          this.setData({
            spaceheight: 0,
            loading: false,
            finished: true
          })
        })
      }
    } catch (error) {
      console.log('records:', error)
    }
  },
  isSameDate(date1, date2) {
    const d1 = new Date(date1)
    const d2 = new Date(date2)
    return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth() && d1.getDate() === d2.getDate()
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('unload');
    // 继续监听新消息
    eventBus.off('onNewMessage')
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})