page{
  height: 100%;
}
.topic-box{
  height: 100%;
  overflow: auto;
  background: rgb(248, 248, 248);
  padding: 28rpx 36rpx 60rpx;
  .add-topic{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .add-topic-left{
      color: rgb(51, 51, 51);
      font-size: 28rpx;
      font-weight: 700;
      line-height: 40rpx;
      display: flex;
      align-items: center;
      .add-topic-title{
        margin-right: 10rpx;
      }
      .add-topic-subtitle{
        color: rgb(233, 178, 99);
        font-size: 24rpx;
        font-weight: 500;
        line-height: 40rpx;
        display: flex;
        align-items: center;
        image{
          width: 24rpx;
          height: 24rpx;
        }
        .text{
          margin-left: 4rpx;
        }
      }
    }
    .add-topic-right{
      display: flex;
      align-items: center;
      color: #0057FF;
      font-size: 24rpx;
      font-weight: 400;
      line-height: 40rpx;
      .add-icon{
        background: #0057FF;
        width: 28rpx;
        height: 28rpx;
        border-radius: 50%;
        // width: ;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 4rpx;
        image{
          width: 16rpx;
          height: 16rpx;
        }
      }
    }
  }
  .topic-item{
    margin-top: 28rpx;
    box-shadow: 0 36rpx 28rpx 0 rgba(99, 97, 155, 0.1);
    padding: 30rpx 28rpx;
    background-color: #FFF;
    border-radius: 12rpx;
    .topic-title{
      color: #0057FF;
      font-size: 30rpx;
      font-weight: 500;
      line-height: 42rpx;
    }
    .topic-content{
      margin-top: 16rpx;
      color: rgb(115, 119, 131);
      font-size: 28rpx;
      font-weight: 400;
      line-height: 40rpx;
      rich-text{
        color: rgb(115, 119, 131);
        font-size: 28rpx;
        font-weight: 400;
        line-height: 40rpx;
      }
    }
    .topic-tips{
      .topic-tips-item{
        margin-top: 24rpx;
        background: rgb(248, 249, 254);
        border-radius: 12rpx;

        color: rgb(115, 119, 131);
        font-size: 28rpx;
        font-weight: 400;
        line-height: 40rpx;
        padding: 20rpx 16rpx 16rpx;
        .topic-tips-item-header{
          display: flex;
          align-items: center;
          image{
            width: 44rpx;
            height: 44rpx;
            margin-right: 4rpx;
          }
        }
        .topic-tips-item-value{
          margin-top: 16rpx;
        }
      }
    }
    .topic-delete{
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 24rpx;
      .topic-number{
        color: rgb(115, 119, 131);
        font-size: 24rpx;
        font-weight: 400;
        line-height: 40rpx;
        .active{
          color: #0057FF;
        }
      }
      .delete-btn{
        display: flex;
        align-items: center;
        van-button{
          overflow: hidden;
          height: 100%;
          flex: 1;
          button{
            font-size: 24rpx;
            // line-height: 48rpx;
            border-radius: 8rpx;
            width: 100%;
            height: 100%;
            padding: 6rpx 50rpx;
          }
        }
        .info-btn{
          border: 1px solid #0057FF !important;
          background: #0057FF !important;
          color: rgb(255, 255, 255) !important;
        }
      }

    }
  }
  .empty{
    padding-top: 100rpx;
  }
}