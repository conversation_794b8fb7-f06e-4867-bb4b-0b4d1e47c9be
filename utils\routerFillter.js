import { loginCheck } from './loginAbout'

// const appGlobalData = getApp().globalData;
/**
 * routerFillter --全局路由拦截器
 * @function
 * @param{Object} pageObj 当前页面的page对象
 * @param{Boolean} flag 是否开启权限判断
 */
export const routerFillter1 = function (pageObj, flag = false) {
  if (flag) {
    pageObj.onShow = function () {
      loginCheck(null, false)
    }
  }
  return Page(pageObj)
}
export const routerFillter = function (pageObj, flag = false) {
  if (flag) {
    const originalOnShow = pageObj.onShow; // 保存原来的 onShow

    pageObj.onShow = function () {
      loginCheck(() => {
        if (originalOnShow) {
          originalOnShow.apply(this, arguments); // 只有登录成功后才执行原来的 onShow
        }
      }, false);
    };
  }
  return Page(pageObj);
};


