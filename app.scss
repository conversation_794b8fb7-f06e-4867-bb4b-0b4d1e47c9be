/**app.wxss**/
rich-text{
  word-break: break-all;
}
page,text,view,scroll-view{
  box-sizing: border-box;
  // word-break: break-all;
  word-break: break-all;
}
.van-button--disabled{
  background: #acacac !important;
  color: #fff !important;
  border-color: #acacac !important;
}
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}
/* 去除默认滚动条效果 */
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}
.protocol-popup {
  width: 554rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: #fff;
  background: #fff
    url("https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/personal/protocal-bg.png")
    no-repeat -200rpx -360rpx/1086rpx 644rpx;
  padding: 40rpx 0;
}

.protocol-popup .title {
  text-align: center;
  color: rgb(51, 51, 51);
  font-size: 32rpx;
  font-weight: 700;
  line-height: 48rpx;
}

.protocol-popup .protocol-popup-value {
  padding: 0 50rpx;
  margin-top: 14rpx;
  max-height: 60vh;
  padding-bottom: 40rpx;
  display: flex;
  box-sizing: border-box;
}

.protocol-popup .protocol-popup-value .box {
  flex: 1;
  overflow: scroll;
}
.protocol-popup .protocol-popup-value .agreement-box{
  font-size: 28rpx;
  line-height: 32rpx;
}
.protocol-popup .protocol-popup-value .box .h2 {
  font-size: 36rpx;
  font-weight: 600;
  margin: 16rpx 0;
}

.protocol-popup .protocol-popup-value .box .h3 {
  font-size: 30rpx;
  font-weight: 600;
}

.protocol-popup .protocol-popup-value .box .text {
  font-size: 24rpx;
  margin: 16rpx 0;
}
.protocol-popup .protocol-popup-value .end-name,

.end-date {
  text-align: right;
  font-size: 24rpx;
}

.textoverflow{
  display: block !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}
.finishedText {
  font-size: 28rpx;
  text-align: center;
  color: #666;
  padding: 10rpx 0 20rpx;
}