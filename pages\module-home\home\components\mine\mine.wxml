<view class="mine-component">
  <view class="mentor-label">
    <view class="mentor-label-item">
      <view class="header">
        <image src="/static/icon/field.png" alt="" />
        <text>擅长领域</text>
      </view>
      <view class="value" wx:if="{{userFullInfo.mentorSkillFieldList}}" wx:key="{{index}}">
        <text wx:for="{{userFullInfo.mentorSkillFieldList}}" wx:key="{{index}}"><text>{{ item.skillFieldName }}</text></text>
      </view>
      <view class="value" wx:else="">
        <text>-</text>
      </view>
      <!-- <view class="value" v-else>-</view> -->
    </view>
    <view class="mentor-label-item">
      <view class="header">
        <image src="/static/icon/discribe.png" alt="" />
        <text>个人简介</text>
      </view>
      <view class="value" wx:if="{{userFullInfo.mentorBase.introduction}}">
        <rich-text nodes="{{userFullInfo.mentorBase.introduction}}"></rich-text>
      </view>
      <view class="value" wx:else>-</view>
    </view>
  </view>
  <view class="card-box">
    <view class="header">
      <view class="name">预约明细</view>
      <view class="operation" bind:tap="toAppointList">
        <text>更多</text>
        <image src="/static/icon/arrow-right-gray.png" mode=""/>
      </view>
    </view>
    <view class="appoint-content" wx:if="{{ appointList.length > 0 }}">
      <view class="appoint-item" wx:for="{{appointList}}" wx:key="{{item}}" bind:tap="toAppointDetail" data-item="{{item}}">
        <view class="appoint-item-header">
          <view class="appoint-user-info">
            <image src="{{item.user && item.user.avatarPath}}" mode="aspectFill" />
            <text class="textoverflow">{{ item.user && item.user.userName }}</text>
          </view>
          <appointStatusIcon
            appointStatus="{{ item.appointStatus }}"
            reportStatus="{{ item.appointReportStatus }}"
          ></appointStatusIcon>
        </view>
        <view class="appoint-item-topic">
          <view>#{{item.topicTitle}}</view>
        </view>
        <view class="appoint-item-date">{{item.appointTimePeriod}}</view>
      </view>
    </view>
    <view class="empty" wx:else>
      <custom-empty-new></custom-empty-new>
    </view>
  </view>
  <view class="card-box">
    <view class="header">
      <view class="name">我的话题</view>
      <view class="operation" bind:tap="toTopicList">
        <text>编辑</text>
        <image src="/static/icon/arrow-right-gray.png" mode=""/>
      </view>
    </view>
    <view class="topic-list" wx:if="{{ topicList.length > 0 }}">
      <view class="topic-item" wx:for="{{topicList}}" wx:key="{{index}}" bind:tap="editExp" data-id="{{ item.id }}">
        <view class="icon">#</view>
        <view class="value textoverflow">{{ item.title }}</view>
      </view>
    </view>
    <view class="empty" wx:else>
      <custom-empty-new></custom-empty-new>
    </view>
  </view>
  <view class="card-box">
    <view class="header">
      <view class="name">咨询设置</view>
      <view class="operation" bind:tap="toAppointDateSet">
        <text>编辑</text>
        <image src="/static/icon/arrow-right-gray.png" mode=""/>
      </view>
    </view>
    <view class="appoint-set" wx:if="{{ appointDateList.length > 0 }}">
      <view class="appoint-set-date">
        <view class="appoint-set-date-item" wx:for="{{appointDateList}}" wx:key="{{index}}" bind:tap="toAppointDateSet">
          <view class="appoint-set-date-item-left">
            <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/clock.png"/>
            <text>{{ item.startDate }} {{ item.setTime }}</text>
          </view>
          <view class="appoint-set-date-item-right">
            {{item.appointTypeText}}
            <!-- {{ item.appointConfigStatus | filterDateStatus }} -->
          </view>
        </view>
      </view>
    </view>
    <view class="empty" wx:else>
      <custom-empty-new></custom-empty-new>
    </view>
  </view>
  <view class="card-box">
    <view class="header">
      <view class="name">评价管理</view>
      <view class="operation" bind:tap="toCommentList">
        <text>更多</text>
        <image src="/static/icon/arrow-right-gray.png" mode=""/>
      </view>
    </view>
    <view class="comment-list" wx:if="{{ commentList.length > 0 }}">
      <view class="comment-item" wx:for="{{commentList}}" wx:key="{{index}}" bind:tap="toCommentList">
        <view class="comment-item-header">
          <view class="comment-item-header-left">
            <image src="{{item.studentAvatarPath}}" mode="aspectFill" />
            <text class="textoverflow">{{ item.studentName }}</text>
          </view>
          <view class="comment-item-header-right" wx:if="{{ item.status === 1 }}">
            <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/hidden.png" alt="" />
            <text>评价已隐藏</text>
          </view>
          <!-- <view class="comment-item-header-right" :class="{ 'comment-hide': item.status !== 1 }">
          {{ item.up === 1 ? '上架显示' : '下架隐藏' }}
        </view> -->
        </view>
        <view class="comment-item-value">{{ item.content }}</view>
        <view class="comment-item-topic">#{{ item.topicTitle }}</view>
        <view class="comment-item-bottom">
          <view class="comment-item-bottom-date">{{ item.createdAtText }}</view>
          <view class="comment-item-bottom-like">
            <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/like-plain.png" alt="" />
            <text>{{ item.thumbUpNum || 0 }}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="empty" wx:else>
      <custom-empty-new></custom-empty-new>
    </view>
  </view>
  <copyright></copyright>
</view>