// pages/mentor-apply/menrot-apply-result/menrot-apply-result.js
import { getTouristAuthenticationInfo } from '~/api/user'
import { loginAfter } from '~/utils/loginAbout'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    status: null, // 10 ,20 ,30
    endDate: '',
    rejectionReason: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getTouristAuthenticationInfo()
  },
  showReason() {
    wx.showModal({
      title: '失败原因',
      content: this.data.rejectionReason || '-',
      confirmText: '前往修改',
      complete: (res) => {
        if (res.cancel) {
          
        }
    
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/mentor-apply/mentor-apply-process/base-info/base-info',
          })
        }
      }
    })
  },
  toLogin() {
    loginAfter(wx.getStorageSync('token'))
  },
  async getTouristAuthenticationInfo() {
    getTouristAuthenticationInfo().then(res => {
      if (res.data) {
        this.setData({
          status: res.data.reviewStatus,
          endDate: res.data.expectedTime.split(' ')[0],
          rejectionReason: res.data.rejectionReason
        })
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})