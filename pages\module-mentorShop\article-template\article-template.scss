/* pages/module-mentorShop/article-template/article-template.wxss */
.tab-container{
  height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
    display: none;
  }
}
.tab-header {
  display: flex;
  justify-content: space-between;
  background: rgb(255, 255, 255);
  padding: 20rpx;
  height: 88rpx;
  box-shadow: 0 0 33rpx 0 rgba(99, 97, 155, 0.1);

  .tab-item {
    flex: 1;
    position: relative;
    font-size: 32rpx;
    color: #777777;
    display: flex;
    justify-content: space-around;

    &.active {
      color: #333;
      font-weight: bold;
      font-size: 36rpx;

      &::after {
        content: "";
        position: absolute;
        bottom: -16rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 62rpx;
        height: 10rpx;
        background: #0057ff;
        border-radius: 16rpx;
      }
    }
  }
}
image{
  width: 100%;
  height: 100%;
  display: block;
  max-height: none;
  object-fit: contain;
}