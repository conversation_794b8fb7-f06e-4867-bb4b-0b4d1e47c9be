<view class="mentor-in-box">
  <view class="mentor-in-content">
    <view class="schedule">
      <view class="slider-box">
        <!-- 0 28 50 73 100 -->
        <van-slider
        disabled="{{ true }}"
        value="{{ sliderValue }}"
        bar-height="8rpx"
        active-color="rgb(71, 99, 244)"
        inactive-color="rgb(217, 223, 255)"
        use-button-slot
        >
          <view slot="button" class="custom-slide-img">
            <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/slide-button.png" />
          </view>
        </van-slider>
      </view>
      <view class="step-items">
        <view class="step-item">基本信息</view>
        <view class="step-item">教育经历</view>
        <view class="step-item">工作经历</view>
        <view class="step-item">话题信息</view>
        <view class="step-item">辅导设置</view>
      </view>
    </view>
    <view class="slot-box">
      <slot></slot>
    </view>
    <copyright></copyright>
  </view>
  <view class="mentor-in-footer">
    <van-button bind:tap="pageBack" custom-class="default-btn" type="default" wx:if="{{ back }}">上一步</van-button>
    <van-button bind:tap="pageNext" custom-class="info-btn" type="info">下一步</van-button>
  </view>
</view>