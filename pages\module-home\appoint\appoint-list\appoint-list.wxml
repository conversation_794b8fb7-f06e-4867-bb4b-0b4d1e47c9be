<view class="appoint-list">
  <view class="tabs">
    <van-tabs color="#0057FF" line-height="10rpx" swipeable="{{ true }}" swipe-threshold="{{ 4 }}" active="{{ active }}" bind:change="tabChange">
      <van-tab wx:for="{{ tabList }}" wx:key="{{index}}" name="{{item.id}}" title="{{item.label}}"></van-tab>
    </van-tabs>
  </view>
  <view class="appoint-data">
    <scroll-view class="scrollarea" scroll-y type="list" bindscrolltolower="loadMore">
      <view class="appoint-data-list" wx:if="{{appointList && appointList.length > 0}}">
        <view class="appoint-item" bind:tap="toAppointDetail" data-item="{{item}}" wx:for="{{appointList}}" wx:key="{{index}}">
          <view class="mentor-info">
            <image class="appoint-type-image" wx:if="{{ item.appointType === 10 }}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/online-appoint-icon.png" mode="aspectFill" />
            <image class="appoint-type-image" wx:if="{{ item.appointType === 20 }}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/offline-appoint-icon.png" mode="aspectFill" />
            <image class="mentor-info-avatar" src="{{item.user.avatarPath}}" mode="aspectFill" />
            <view class="mentor-info-base">
              <view class="name textoverflow">
                {{item.user.userName}}
                <!-- 我是名字 -->
              </view>
              <view class="address" wx:if="{{item.appointType === 20 && item.address}}">
              <!-- <view class="address" wx:if="{{ true }}"> -->
                <!-- <svg-icon size="15" name="position-border" color="rgb(115, 119, 131)"></svg-icon> -->
                <image src="/static/icon/position.png" mode=""/>
                <text class="address-value textoverflow">预约地点：{{item.address}}</text>
              </view>
            </view>
          </view>
          <view class="appoint-topic textoverflow">#{{item.topicTitle}}</view>
          <view class="appoint-status">
            <view class="appoint-date">咨询时间：{{item.appointTimePeriod}}</view>
            <!-- <AppointStatusIcon :appointStatus="item.appointStatus" :reportStatus="item.appointReportStatus"></AppointStatusIcon> -->
            <appointStatusIcon
              appointStatus="{{ item.appointStatus }}"
              reportStatus="{{ item.appointReportStatus }}"
            ></appointStatusIcon>
          </view>
        </view>
      </view>
      <view class="empty" wx:if="{{(!appointList || appointList.length <= 0) && showEmpty}}">
        <custom-empty-new></custom-empty-new>
      </view>
    </scroll-view>
  </view>
</view>