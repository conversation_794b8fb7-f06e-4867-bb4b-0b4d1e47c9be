// pages/module-mentorShop/all-reviews/all-reviews.js
import { getCourseCommentList,getCourseCommentScore } from '~/api/mentorShop'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    dynamicsList: [],
    courseId: '',
    courseCommentScore: {},
    loading: false,
    page: {
      pageSize: 20, // 每页数量
      pageNumber: 1, // 当前页码
    },
    finished: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      courseId: options.courseId
    })
    this.getCourseCommentScore()
    this.getCourseCommentList()
  },


  async getCourseCommentScore() {
    const {
      data
    } = await getCourseCommentScore({
      courseId: this.data.courseId
    })
    this.setData({
      courseCommentScore: data
    })
  },

  async getCourseCommentList() {
    if (this.data.loading) return;

    this.setData({
      loading: true,
    });

    const {
      data
    } = await getCourseCommentList({
      ...this.data.page,
      courseId: this.data.courseId
    });

    if (data.length < this.data.page.pageSize) {
      this.setData({
        finished: true
      });
    }

    // 避免重复数据
    const newList = this.data.page.pageNumber === 1 ?
      data :
      [...this.data.dynamicsList, ...data];

    this.setData({
      dynamicsList: newList,
      loading: false,
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  loadMore() {
    if (this.data.loading || this.data.finished) return;
    this.setData(
      {
        'page.pageNumber': this.data.page.pageNumber + 1,
      },
      () => {
        this.getCourseCommentList();
      }
    );
  },

})