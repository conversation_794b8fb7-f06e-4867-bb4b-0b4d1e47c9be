page{
  height: 100%;
}
.mentor-in-box{
  background: #FFF;
  height: 100%;
  display: flex;
  flex-direction: column;
  .mentor-in-content{
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .edit-box{
      flex: 1;
      padding: 10rpx 36rpx 22rpx;
      .base-info{
        width: 100%;
        padding: 0 28rpx 30rpx;
        border-radius: 12rpx;
        background: #fff;
        box-shadow: 0px 2px 16px 0px rgba(99, 97, 155, 0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
        .user-portrait {
          width: 152rpx;
          height: 152rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          background: #fff;
          box-shadow: 0 6rpx 34rpx 0 rgba(181, 181, 181, 0.73);
          border-radius: 50%;
          margin: 0 auto;
          // margin-bottom: 22rpx;
          position: relative;
      
          image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
      
            &.icon {
              width: 40rpx;
              height: 40rpx;
              position: absolute;
              right: 0;
              top: 20rpx;
            }
          }
        }
        .appoint-info-content{
          width: 100%;
          // padding-top: 22rpx;
          // padding: 0 30rpx;
          .required{
            .van-cell__title{
              &::after{
                margin-left: -2rpx;
                padding-top: 6rpx;
                box-sizing: border-box;
                content: "*";
                font-size: 32rpx;
                color: rgb(233, 54, 54);
                line-height: 32rpx;
              }
            }
          }
          .van-cell{
            padding: 24rpx 0;
            border-bottom: 2rpx solid #dfdfdf;
            .van-cell__title{
              width: 200rpx !important;
              margin: 0 !important;
              max-width: 200rpx !important;
              min-width: 200rpx !important;
              font-weight: 400;
            }
          }
          .custom-textarea{
            border: none;
            flex-direction: column;
            padding-top: 24rpx;
            padding-bottom: 30rpx;
            .van-field__control{
              margin-top: 8rpx;
              background: rgb(248, 249, 254);
              border-radius: 12rpx;
              padding: 26rpx;
            }
            &::after{
              display: none;
            }
          }
          van-field{
            &:nth-last-child(1) {
              .van-cell{
                padding-bottom: 0;
                border: none;
              }
            }
          }
        }
      }
    }
  }
  .mentor-in-footer{
    box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0,0,0,0.05);
    background: #FFF;
    height: 160rpx;
    padding: 24rpx 36rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    van-button{
      overflow: hidden;
      height: 100%;
      flex: 1;
      margin-right: 40rpx;
      button{
        font-size: 36rpx;
        // line-height: 48rpx;
        border-radius: 120rpx;
        width: 100%;
        height: 100%;
      }
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .default-btn{
      border: 1px solid #0057FF;
      background: #fff;
      color: #0057FF;
    }
    .info-btn{
      border: 1px solid #0057FF !important;
      background: #0057FF !important;
      color: rgb(255, 255, 255) !important;
    }
  }
}

.field-select-box{
  .field-select{
    .header{
      padding-top: 36rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #272727;
      font-size: 38rpx;
      font-weight: 600;
      line-height: 54rpx;
      text-align: center;
      position: relative;
    }
    .field-list{
      padding: 0 26rpx 12rpx;
      padding-top: 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      .field-item{
        margin-top: 22rpx;
        width: 32%;
        height: 84rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgb(244, 243, 243);
        border-radius: 10rpx;
        color: rgb(39, 39, 39);
        font-size: 28rpx;
        font-weight: 400;
        line-height: 40rpx;
      }
      .field-item-active{
        background: #0057FF;
        color: rgb(255, 255, 255);
      }
      &::after{
        content: '';
        width: 32%;
        height: 0;
      }
    }
    .field-button{
      padding-bottom: 32rpx;
      // height: 160rpx;
      padding: 24rpx 26rpx 32rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      van-button{
        overflow: hidden;
        height: 90rpx;
        flex: 1;
        margin-right: 30rpx;
        button{
          font-size: 32rpx;
          // line-height: 48rpx;
          border-radius: 120rpx;
          width: 100%;
          height: 100%;
        }
        &:nth-last-child(1) {
          margin-right: 0;
        }
      }
      .default-btn{
        border: none;
        background: #fff;
        color: #333333;
      }
      .info-btn{
        border: 1px solid #0057FF !important;
        background: #0057FF !important;
        color: rgb(255, 255, 255) !important;
      }
    }
  }
}