// 就是配置当前小程序项目的环境变量
// 获取当前小程序的账号信息
const { miniProgram } = wx.getAccountInfoSync()
// 获取小程序的版本
const { envVersion } = miniProgram;

let env = {
  apiBaseUrl: "", // 接口地址
  studentDomainName: "", // 学生端域名
  mentorDomainName: "", // 导师端域名
}

switch (envVersion) {
    case 'develop':
        // 开发版
        // env.apiBaseUrl = "http://*************:8888"
        // env.apiBaseUrl = "https://5xq2398jr917.vicp.fun"
        env.apiBaseUrl = "https://api-test.leapingcarp.com:8887"
        env.studentDomainName = "https://test-student.leapingcarp.com/#/"
        env.mentorDomainName = "https://test-mentor.leapingcarp.com/#/",
        // env.mentorDomainName = "https://**************:8088/#/",
        env.wsApi= 'wss://api-test.leapingcarp.com:8887',
        env.Tenant_id = 9999
        break
    case 'trial':
        // 体验版
        // env.apiBaseUrl = "https://api.leapingcarp.com:8888"
        // env.studentDomainName = "https://student.leapingcarp.com/#/"
        // env.mentorDomainName = "https://mentor.leapingcarp.com/#/",
        // env.wsApi= 'wss://api.leapingcarp.com:8888',
        // env.Tenant_id = 106

        env.apiBaseUrl = "https://api-test.leapingcarp.com:8887"
        env.studentDomainName = "https://test-student.leapingcarp.com/#/"
        env.mentorDomainName = "https://test-mentor.leapingcarp.com/#/",
        env.wsApi= 'wss://api-test.leapingcarp.com:8887',
        env.Tenant_id = 9999
        break
    case 'release':
        // 正式版
        env.apiBaseUrl = "https://api.leapingcarp.com:8888"
        env.studentDomainName = "https://student.leapingcarp.com/#/"
        env.mentorDomainName = "https://mentor.leapingcarp.com/#/",
        env.wsApi= 'wss://api.leapingcarp.com:8888',
        env.Tenant_id = 106
        break
    default:
        break
}
export { env }