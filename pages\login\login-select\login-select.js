// pages/login-selection/login-selection.js
import { getUserPhoneNumber, userLogin } from '../../../api/login'
import { loginAfter } from '../../../utils/loginAbout'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    checked: false,
    functionList: [
      {
        img: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/login/phone.png',
        name: '电话咨询预约'
      },
      {
        img: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/login/message.png',
        name: '即时消息咨询'
      },
      {
        img: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/login/appoint.png',
        name: '线下咨询签到'
      },
      {
        img: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/login/job.png',
        name: '校招岗位预览'
      },
      {
        img: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/login/resume.png',
        name: '简历上传与管理'
      }
    ]
  },
  onChange(event) {
    this.setData({
      checked: event.detail
    })
  },
  userAgreement() {

  },
  toUserAgreement(e) {
    wx.navigateTo({
      url: `/pages/login/user-agreement/user-agreement`
    })
  },
  toUserPrivacy() {
    wx.navigateTo({
      url: `/pages/login/privacy-policy/privacy-policy`
    })
  },
  checkSelect() {
    wx.showToast({
      title: '请勾选《用户协议》、《隐私政策》',
      duration: 1500,
      icon: 'none'
    })
  },
  bindgetphonenumber(e) {
    if (e.detail.errMsg.indexOf('ok') !== -1) {
      getUserPhoneNumber({
        code: e.detail.code
      }).then(phoneData => {
        wx.login({
          success (loginData) {
            if (loginData.code) {
              userLogin({
                code: loginData.code,
                phone: phoneData.data.phoneNumber
              }).then(res => {
                loginAfter(res.data.accessToken)
              })
            } else {
            }
          },
          fail (err) {
          }
        })
      })
    }
  },
  phoneLogin() {
    wx.navigateTo({
      url: `/pages/login/login-phone/login-phone`
    })
  },
  back() {
    wx.navigateBack()
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})