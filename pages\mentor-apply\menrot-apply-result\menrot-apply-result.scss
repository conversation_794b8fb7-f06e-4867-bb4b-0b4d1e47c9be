.mentor-apply-introduction{
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #F7F8FB;
  .content{
    padding: 24rpx 36rpx;
    flex: 1;
    overflow: auto;
    .process{
      width: 100%;
      // margin-top: -70rpx;
      background: #FFFFFF;
      box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(99,97,155,0.1);
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      padding: 30rpx 30rpx 38rpx;
      .title{
        font-weight: bold;
        font-size: 32rpx;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .process-box{
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-top: 16rpx;
        padding: 0 30rpx;
        .process-item{
          display: flex;
          flex-direction: column;
          align-items: center;
          .process-item-title{
            font-weight: 600;
            font-size: 48rpx;
            color: #AAA;
            text-align: center;
            font-style: normal;
            text-transform: none;
            position: relative;
            .icon{
              position: absolute;
              background: rgba(0,87,255,0.2);
              border-radius: 4rpx 4rpx 4rpx 4rpx;
              width: 48rpx;
              height: 8rpx;
              bottom: 10rpx;
              left: 50%;
              transform: translateX(-50%);
            }
          }
          .process-item-text{
            margin-top: 8rpx;
            font-weight: 500;
            font-size: 26rpx;
            color: #AAA;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
        }
        .process-item-active{
          .process-item-title{
            color: #333333;
            .icon{
              background: rgba(0,87,255,0.6);
            }
          }
          .process-item-text{
            color: #333333;
          }
        }
        .process-transition{
          padding-top: 8rpx;
          color: rgba(0,87,255,0.8);
          image{
            height: 22rpx;
            width: 22rpx;
          }
        }
      }
    }
    .process-result{
      padding-top: 140rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      .result-img{
        width: 375rpx;
      }
      .wait{
        display: flex;
        flex-direction: column;
        align-items: center;
        .text{
          margin-top: 24rpx;
          font-weight: bold;
          font-size: 29rpx;
          color: #333333;
          line-height: 57rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .value{
          margin-top: 24rpx;
          font-weight: bold;
          font-size: 29rpx;
          color: #333333;
          line-height: 57rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
          text{
            color: #0057FF;
          }
        }
      }
      .fail{
        .value{
          margin-top: 24rpx;
          font-weight: bold;
          font-size: 29rpx;
          color: #333333;
          line-height: 57rpx;
          text-align: center;
          font-style: normal;
          text-transform: none;
          text{
            color: #165DFF;
          }
        }
      }
      .success{
        display: flex;
        flex-direction: column;
        align-items: center;
        .text{
          margin-top: 24rpx;
          font-weight: bold;
          font-size: 29rpx;
          color: #333333;
          line-height: 57rpx;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
        .login-accredit{
          margin-top: 48rpx;
          width: 622rpx;
          height: 96rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #0057FF;;
          border-radius: 62rpx;
          font-weight: 600;
          font-size: 32rpx;
          color: #FFFFFF;
          line-height: 32rpx;
          text-align: center;
          font-style: normal;
          margin-left: 0;
          margin-right: 0;
        }
        .value{
          margin-top: 24rpx;
          font-weight: bold;
          font-size: 29rpx;
          color: #333333;
          line-height: 57rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
          text{
            color: #0057FF;
          }
        }
      }
    }
  }
}
