// globalMessageWatcher.js
import eventBus from './eventBus';

let globalMessageWatcher = null;
let hasNewMessage = false;
let lastEmitTime = 0;
const THROTTLE_INTERVAL = 200; // 200ms 节流

export function initGlobalMessageWatcher(createMessageWatcher) {
  if (!globalMessageWatcher) {
    globalMessageWatcher = createMessageWatcher(
      // 回调函数
      (message) => {
        const now = Date.now();
        if (now - lastEmitTime > THROTTLE_INTERVAL) {
          lastEmitTime = now;
          hasNewMessage = true;
          eventBus.emit('onNewMessage', message);
        } else {
          console.log('消息被节流，未触发 emit');
        }
      },
      // 过滤条件
      (message) => message.sig === 103
    );
    globalMessageWatcher.listen();
  }
  return globalMessageWatcher;
}

export function getAndResetNewMessageFlag() {
  const flag = hasNewMessage;
  hasNewMessage = false;
  return flag;
}

export function clearGlobalMessageWatcher() {
  if (globalMessageWatcher) {
    globalMessageWatcher.clear();
    globalMessageWatcher = null;
  }
}
