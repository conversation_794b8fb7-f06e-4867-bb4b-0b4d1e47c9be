// pages/module-home/home/<USER>
import { getUserFullInfo, getMentorStatistics } from '~/api/user'
import { unreadCount } from '~/api/im'
import { getDynamicList, getDynamicMessageUnreadCount } from '~/api/mentorShop';
import { getAndResetNewMessageFlag } from '~/utils/websocket/globalMessageWatcher';
import eventBus from '~/utils/websocket/eventBus';
import { navigateAndAddPage } from '~/utils/pageNavigation';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    active: 0,
    rate: [],
    dynamicsList: [],
    // 浮动按钮位置
    floatButtonTop: 400,
    floatButtonLeft: 300,
    // 触摸相关变量
    targetStartY: 0,
    startX: 0,
    startY: 0,
    moveX: 0,
    moveY: 0,

    userFullInfo: {
      mentorBase: {
        user: {}
      },
      mentorEducationList: [],
      mentorProfessionalList: [],
      mentorSkillFieldList: [],
      mentorTradeList: []
    },
    appointInfo: [
      {
        label: '已辅导次数',
        number: 0,
        numberText: '次',
        field: 'appointedNum'
      },
      {
        label: '预约咨询时长',
        number: 0,
        numberText: '小时',
        field: 'appointDuration'
      },
      {
        label: '待辅导次数',
        number: 0,
        numberText: '次',
        field: 'waitAppointNum'
      }
    ],
    loading: false,
    page: {
      pageSize: 20, // 每页数量
      pageNumber: 1, // 当前页码
    },
    finished: false,
    dynamicsList: [],//动态列表
    messageNumber: 0,
    personalLetterNumber: 0,
    add: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.active) {
      const activeIndex = parseInt(options.active);
      this.setData({
        active: activeIndex
      });
    }
    const tempForm = wx.getStorageSync('tempVideoForm');
    if (tempForm) {
      wx.removeStorage({
        key: 'tempVideoForm'
      });
    }

    // 初始化浮动按钮位置
    this.initFloatButtonPosition();
    this.updateHandler = this.updateDynamicItem.bind(this);
    eventBus.on('updateDynamicItem', this.updateHandler);
  },

  onShow() {
    this.selectComponent('#myComponent').onPageShow()
    this.getMentorStatistics()
    this.getUserFullInfo()
    this.getUnreadCount()
    this.getDynamicMessageUnreadCount();
    // 继续监听新消息
    eventBus.on('onNewMessage', (message) => {
      this.getUnreadCount()
    });
    if (this.data.active === 1) {
      // 获取shop组件
      const shopComponent = this.selectComponent('#shop');
      console.log(shopComponent, 'shopComponentshopComponentshopComponent');
      if (shopComponent) {
        // 每次返回页面时都调用onShow方法，确保专栏列表刷新
        shopComponent.onShow();

        // 如果需要刷新课程列表，才调用activeChange
        // if (shopComponent.data.needRefresh) {
        //   this.activeChange();
        // }
      }
    }
    else if (this.data.active === 2) {
      const dynamicId = wx.getStorageSync('dynamicId');
      if(dynamicId) {
        const dynamicsList = this.data.dynamicsList.filter(item => item.id !== dynamicId);
        this.setData({
          dynamicsList
        },() => {
          wx.removeStorageSync('dynamicId');
        });
      }
    }
    const tempForm = wx.getStorageSync('tempVideoForm');
    if (tempForm) {
      wx.removeStorage({
        key: 'tempVideoForm'
      });
    }
  },
  showAvatar() {
    wx.previewImage({
      current: this.data.userFullInfo.mentorBase.user.avatarPath, // 当前显示图片的http链接
      urls: [this.data.userFullInfo.mentorBase.user.avatarPath] // 需要预览的图片http链接列表
    })
  },
  updateDynamicItem(updatedItem) {
    const newList = this.data.dynamicsList.map(item => {
      return item.id === updatedItem.id ? { ...item, ...updatedItem } : item;
    });
    this.setData({ dynamicsList: newList });
  },

  async getUnreadCount() {
    const { data } = await unreadCount()
    // this.messageNumber = data
    this.setData({
      personalLetterNumber: data
    })
  },
  async getMentorStatistics(startTime, endTime) {
    const { data } = await getMentorStatistics({ startTime, endTime })
    const appointInfo = this.data.appointInfo
    appointInfo.forEach(item => {
      if (item.field === 'appointDuration') {
        item.number = Math.ceil(data[item.field] / 60)
      } else {
        item.number = data[item.field]
      }
    })
    this.setData({
      appointInfo
    })
  },
  async getUserFullInfo() {
    const { data } = await getUserFullInfo()
    this.getStars(data.mentorBase.score)
    this.setData({
      userFullInfo: data
    })
  },
  getStars(score) {
    const starCount = 5 // 总星数为5颗
    const starImages = {
      full: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/star.png', // 满星
      half: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/half-star.png', // 半星
      empty: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/not-star.png' // 灰色星
    }

    const stars = []
    const integerPart = Math.floor(score) // 整数部分
    const decimalPart = score - integerPart // 小数部分

    for (let i = 0; i < starCount; i++) {
      if (i < integerPart) {
        stars.push(starImages.full) // 满星
      } else if (i === integerPart && decimalPart > 0.5) {
        stars.push(starImages.full) // 满星
      } else if (i === integerPart && decimalPart > 0 && decimalPart <= 0.5) {
        stars.push(starImages.half) // 半星
      } else {
        stars.push(starImages.empty) // 空星
      }
    }
    this.setData({
      rate: stars
    })
    return stars
  },
  toPersonalLetter() {
    navigateAndAddPage('/pages/personal-letter/personal-letter-list/personal-letter-list')
    // wx.navigateTo({
    //   url: '/pages/personal-letter/personal-letter-list/personal-letter-list',
    // })
  },
  toPlacardSelect() {
    navigateAndAddPage('/pages/mine/placard/placard', {mentorId: this.data.userFullInfo.userId})
    // wx.navigateTo({
    //   url: '/pages/mine/placard/placard',
    // })
  },
  userEdit() {
    navigateAndAddPage('/pages/mine/user-edit/user-edit')
    // wx.navigateTo({
    //   url: '/pages/mine/user-edit/user-edit',
    // })
  },

  async getDynamicList() {
    if (this.data.loading) return;

    this.setData({
      loading: true,
    });

    try {
      const { data } = await getDynamicList({
        ...this.data.page,
      });

      if (data.length < this.data.page.pageSize) {
        this.setData({
          finished: true
        });
      }

      this.setData({
        dynamicsList: this.data.page.pageNumber === 1 ? data : [...this.data.dynamicsList, ...data],
        loading: false
      });
    } catch (error) {
      this.setData({
        loading: false
      });
      console.error('获取动态列表失败：', error);
    }
  },

  async activeChange() {
    const tempForm = wx.getStorageSync('tempVideoForm');
    if (tempForm) {
      wx.removeStorage({
        key: 'tempVideoForm'
      });
    }
    const shopComponent = this.selectComponent('#shop');
    console.log(shopComponent, 'shopComponent')
    if (shopComponent) {
      // 设置需要刷新的标志
      // shopComponent.setNeedRefresh();
      // 然后触发onShow方法，其中已包含了getColumnList和getCourseList的调用
      shopComponent.onShow();
    }
  },

  // 停止动态页面的音频播放
  stopDynamicsAudio() {
    if (this.data.active === 2) {
      const dynamicsComponent = this.selectComponent('#dynamics');
      if (dynamicsComponent && typeof dynamicsComponent.stopAudioAndReset === 'function') {
        dynamicsComponent.stopAudioAndReset();
      }
    }
  },

  tabChange(e) {
    console.log('tabChangetabChangetabChange');
    const index = parseInt(e.detail.index);
    const previousIndex = this.data.active;

    // 如果从动态页面切换到其他页面，停止音频播放
    if (previousIndex === 2 && index !== 2) {
      this.stopDynamicsAudio();
    }

    this.setData({
      active: index
    });
    console.log(index, 'indexindexindexindexindex');
    if (index === 1) {
      this.activeChange();
    } else if (index === 2) {
      this.setData({
        'page.pageNumber': 1,
        finished: false,
        dynamicsList: [],
        loading: false,
      });
      this.getDynamicList();
      this.getDynamicMessageUnreadCount();
    }
  },
  loadMore() {
    if (this.data.active === 1) {
      const shopComponent = this.selectComponent('#shop');
      if (shopComponent) {
        shopComponent.loadMore();
      }
    } else if (this.data.active === 2) {
      if (this.data.loading || this.data.finished) return;
      this.setData({
        'page.pageNumber': this.data.page.pageNumber + 1,
      }, () => {
        this.getDynamicList();
      });
    }
  },
  toDynamic() {
    // 如果当前在动态页面，先停止音频播放
    this.stopDynamicsAudio();
    navigateAndAddPage('/pages/module-mentorShop/dynamics-release/dynamics-release')
    // wx.navigateTo({
    //   url: '/pages/module-mentorShop/dynamics-release/dynamics-release',
    // })
  },
  toDynamicMessage() {
    // 如果当前在动态页面，先停止音频播放
    this.stopDynamicsAudio();
    navigateAndAddPage('/pages/module-mentorShop/message-detial/message-detial')
    // wx.navigateTo({
    //   url: '/pages/module-mentorShop/message-detial/message-detial',
    // })
  },

  async getDynamicMessageUnreadCount() {
    const { data } = await getDynamicMessageUnreadCount();
    this.setData({
      messageNumber: data
    })
  },
  onAddColumn() {
    this.setData({
      add: !this.data.add
    });
  },
  onAddArticle() {
    // 先切换状态，再跳转页面
    this.setData({
      add: false
    }, () => {
      navigateAndAddPage('/pages/module-mentorShop/add-article/add-article')
      // wx.navigateTo({
      //   url: '/pages/module-mentorShop/add-article/add-article'
      // });
    });
  },
  onAddVideo() {
    // 先切换状态，再跳转页面
    this.setData({
      add: false
    }, () => {
      navigateAndAddPage('/pages/module-mentorShop/add-video/add-video')
      // wx.navigateTo({
      //   url: '/pages/module-mentorShop/add-video/add-video'
      // });
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    eventBus.off('onNewMessage')
    // 如果当前在动态页面，先停止音频播放
    this.stopDynamicsAudio();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 如果当前在动态页面，先停止音频播放
    this.stopDynamicsAudio();
    eventBus.off('updateDynamicItem', this.updateHandler);
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 浮动按钮触摸开始事件
  onFloatButtonStart(e) {
    // 记录起始位置
    console.log(e.touches[0].clientY, 'e.touches[0].clientY');
    this.setData({
      startX: e.touches[0].clientX,
      startY: e.touches[0].clientY,
      // 添加一个标记，表示这是一个新的触摸事件
      isDragging: false,
      targetStartY: e.touches[0].clientY
    });

    // 记录触摸开始时间，用于区分点击和拖动
    this.touchStartTime = Date.now();
  },

  // 浮动按钮触摸移动事件
  onFloatButtonMove(e) {
    // 标记正在拖动
    this.setData({
      isDragging: true
    });

    // 只计算Y轴的移动距离，X轴保持不变
    const moveY = e.touches[0].clientY - this.data.startY + this.data.floatButtonTop;

    // 使用新的API获取窗口信息
    const windowInfo = wx.getWindowInfo();
    const maxWidth = windowInfo.windowWidth;
    const maxHeight = windowInfo.windowHeight;

    // 计算rpx转换为px的比例
    const rpxToPxRatio = maxWidth / 750;

    // 获取浮动按钮宽高（准确计算rpx转换为px）
    const buttonWidth = 100 * rpxToPxRatio; // 100rpx转换为px
    const buttonHeight = 100 * rpxToPxRatio; // 100rpx转换为px

    // 限制按钮不超出屏幕范围（只限制Y轴）
    const limitedY = Math.max(0, Math.min(moveY, maxHeight - buttonHeight));

    // 更新按钮位置（X轴始终保持在右边）
    this.setData({
      floatButtonLeft: maxWidth - buttonWidth, // 始终贴在右边
      floatButtonTop: limitedY
    });

    // 更新起始位置，使下次移动从当前位置开始计算
    this.setData({
      startX: e.touches[0].clientX,
      startY: e.touches[0].clientY
    });
  },

  // 浮动按钮触摸结束事件
  onFloatButtonEnd(e) {
    const deltaY = Math.abs(e.changedTouches[0].clientY - this.data.targetStartY)
    if (deltaY <= 20) {
      this.onAddColumn(); // 识别为点击就执行展开/收起
    }
    // 重置状态
    this.setData({
      isDragging: false
    });
  },
  // 初始化浮动按钮位置
  initFloatButtonPosition() {
    // 使用新的API获取窗口信息
    const windowInfo = wx.getWindowInfo();
    const windowWidth = windowInfo.windowWidth;
    const windowHeight = windowInfo.windowHeight;

    // 计算rpx转换为px的比例
    const rpxToPxRatio = windowWidth / 750;

    // 按钮宽度（100rpx转换为px）
    const buttonWidth = 100 * rpxToPxRatio;

    // 设置浮动按钮的初始位置（右下角）
    this.setData({
      floatButtonLeft: windowWidth - buttonWidth, // 右边贴边
      floatButtonTop: windowHeight - 200 // 下边距离屏幕边缘 200px
    });
  }
})