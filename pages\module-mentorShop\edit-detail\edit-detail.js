// pages/module-mentorShop/edit-detail/edit-detail.js
import {
  webUpload
} from "~/api/webUpload"
import { navigateBack } from '~/utils/pageNavigation';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    editorCtx: null,
    showImagePicker: false,
    formats: {},
    editorContent: "",
    currentLength: 0,
    maxLength: '3000',
    placeholder: '',
    isContentChanged: false,
    ready: false,
    type: '',
    isSaving: false, // 标记是否正在保存
  },
  onLoad(options) {
    // 根据type设置导航栏标题
    const titleMap = {
      video: '视频详情',
      article: '文章详情',
      imageText: '图文详情',
    };
    const placeholderMap = {
      video: '填写内容尽量突出与课程特点与优势，还可以从多个角度进行详细描述，以吸引潜在学员的兴趣并增强课程的吸引力。',
      article: '填写内容尽量突出与课程特点与优势，还可以从多个角度进行详细描述，以吸引潜在学员的兴趣并增强课程的吸引力。',
      imageText: '填写内容尽量突出与课程特点与优势，还可以从多个角度进行详细描述，以吸引潜在学员的兴趣并增强课程的吸引力。'
    };
    this.setData({
      placeholder: placeholderMap[options.type] || '填写内容尽量突出与专栏特点与优势，还可以从多个角度进行详细描述，以吸引潜在学员的兴趣并增强专栏的吸引力。'
    });
    wx.setNavigationBarTitle({
      title: titleMap[options.type] || '专栏详情'
    });
    
    this.setData({
      type: options.type
    })


    // 获取临时表单数据
    const storageKey = this.data.type === 'column' ? 'tempColumnForm' : 'tempVideoForm';
    const tempForm = wx.getStorageSync(storageKey);
    const contentKey = this.data.type === 'column' ? 'columnDetail' : 'courseDetail';
    if (tempForm?.[contentKey]) {
      this.setData({
        editorContent: tempForm[contentKey]
      });
    }

  },

  onEditorReady() {
    const that = this
    wx.createSelectorQuery().select('#editor').context(function (res) {
      that.editorCtx = res.context
      that.setData({ ready: true }, () => {
        if (that.data.editorContent) {
          that.editorCtx.setContents({
            html: that.data.editorContent,
            success: () => {
              // 获取编辑器内容
              that.editorCtx.getContents({
                success: (res) => {
                  const plainText = res.text.replace(/\s/g, '').trim();
                  that.setData({
                    currentLength: plainText.length
                  });
                }
              });
            },
            fail: (err) => {
              console.error('设置内容失败：', err);
            }
          })
        }
      })
    }).exec()
  },
  blur() {
    this.editorCtx.blur()
  },
  format(e) {
    let {
      name,
      value
    } = e.target.dataset
    if (!name) return
    this.editorCtx.format(name, value)
  },
  onStatusChange(e) {
    const formats = e.detail
    this.setData({
      formats
    })
  },

  onContentChange(e) {
    const {
      text,
      html
    } = e.detail;
    const plainText = text.replace(/\s/g, '').trim();
    this.setData({
      currentLength: plainText.length,
      editorContent: html,
      isContentChanged: true
    });

    // 当内容变化时，根据是否有改动来开启或关闭返回提示
    if (this.data.isContentChanged) {
      wx.enableAlertBeforeUnload({
        message: "已编辑的内容还未保存，确定离开吗？"
      })
    }
  },

  insertImage() {
    const that = this
    wx.showActionSheet({
      itemList: ['拍摄', '从相册选择'],
      success: function (res) {
        const sourceType = res.tapIndex === 0 ? ['camera'] : ['album']
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: sourceType,
          success: async function (res) {
            const imageId = `img_${Date.now()}_${Math.random().toString(36).slice(2)}`
            const result = await webUpload(res.tempFiles[0].tempFilePath, 'mentor')
            if (result && result.url) {
              that.editorCtx.insertImage({
                src: result.url,
                data: {
                  id: imageId,
                  role: 'god',
                  src: result.url
                },
                width: '100%',
                success: async function () {
                  // 获取编辑器内容并更新缓存
                  that.editorCtx.getContents({
                    success: (res) => {
                      const html = res.html;
                      that.setData({
                        editorContent: html,
                        isContentChanged: true
                      });
                      // 更新缓存
                      const storageKey = that.data.type === 'column' ? 'tempColumnForm' : 'tempVideoForm';

                      // 获取缓存数据
                      let tempForm = wx.getStorageSync(storageKey);

                      if (tempForm) {
                        // 更新相应的字段
                        if (that.data.type === 'column') {
                          tempForm.columnDetail = html;
                        } else {
                          tempForm.courseDetail = html;
                        }

                        // 重新存入缓存
                        wx.setStorageSync(storageKey, tempForm);
                      }

                    }
                  });
                }
              })
            }
          }
        })
      }
    })
  },
  // 修改保存方法
  saveBonusContent() {
    if (this.data.currentLength > this.data.maxLength) {
      wx.showToast({
        title: '字符超出限制',
        icon: 'none'
      });
      return;
    }

    // 标记正在保存，避免清空缓存
    this.setData({
      isSaving: true
    });

    // 获取当前临时表单数据
    const storageKey = this.data.type === 'column' ? 'tempColumnForm' : 'tempVideoForm';
    const tempForm = wx.getStorageSync(storageKey);
    if (tempForm) {
      const contentKey = this.data.type === 'column' ? 'columnDetail' : 'courseDetail';
      tempForm[contentKey] = this.data.editorContent;
      wx.setStorageSync(storageKey, tempForm);
    }
    this.setData({
      isContentChanged: false
    })

    // 保存成功后关闭返回提示
    wx.disableAlertBeforeUnload();

    navigateBack()
  },
  // 添加页面卸载拦截
  onUnload() {
    // 页面卸载时清理
    wx.disableAlertBeforeUnload();

    // 如果不是通过保存按钮离开，并且内容有变化，则清空缓存
    if (!this.data.isSaving && this.data.isContentChanged) {
      const storageKey = this.data.type === 'column' ? 'tempColumnForm' : 'tempVideoForm';
      const tempForm = wx.getStorageSync(storageKey);

      if (tempForm) {
        const contentKey = this.data.type === 'column' ? 'columnDetail' : 'courseDetail';
        // 清空详情内容，但保留其他字段
        tempForm[contentKey] = '';
        wx.setStorageSync(storageKey, tempForm);
      }
    }
  },
  setEditorContent() {
    if (this.editorCtx && this.data.editorContent) {
      this.editorCtx.setContents({
        html: this.data.editorContent,
        success: () => {
          // this.checkContent();
        },
        fail: () => {
          // 处理失败情况
        }
      });
    }
  },
})