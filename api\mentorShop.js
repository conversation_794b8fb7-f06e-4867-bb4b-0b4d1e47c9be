import request from '../utils/request';

// 查看专栏列表
export function getColumnList(data) {
  return request({
    url: `/user-front-service/getColumnsList`,
    method: "get",
    data,
  });
}

// 查看课程列表
export function getCourseList(data) {
  return request({
    url: `/user-front-service/courses/getCoursesList`,
    method: "get",
    data,
  });
}

// 新增课程
export function addCourse(data) {
  return request({
    url: `/user-front-service/courses/addOrUpdate`,
    method: "post",
    data,
  });
}

// 查看课程详情
export function getCourseDetail(data) {
  return request({
    url: `/user-front-service/courses/getCoursesDetail/${data.coursesId}`,
    method: "get",
    data,
  });
}

// 课程评价分数
export function getCourseCommentScore(data) {
  return request({
    url: `/user-front-service/student/course/reviews/score/studentOrMentor/${data.courseId}`,
    method: "get",
    data,
  });
}

// 课程评价列表
export function getCourseCommentList(data) {
  return request({
    url: `/user-front-service/student/course/reviews/list/studentOrMentor/${data.courseId}`,
    method: "get",
    data,
  });
}

// 获取学员列表
export function getStudentList(data) {
  return request({
    url: `/user-front-service/student/courses/progress/${data.courseId}`,
    method: "get",
    data,
  });
}

// 添加动态音频播放次数
export function addDynamicVoicePlayCount(data) {
  return request({
    url: `/user-front-service/student/course/reviews/add/playCount/${data.courseReviewsId}`,
    method: "put",
    data,
  });
}

// 修改课程评价显示状态
export function updateCourseCommentStatus(data) {
  return request({
    url: `/user-front-service/student/course/reviews/showStatus/${data.courseReviewsId}/${data.showStatus}`,
    method: "put",
    data,
  });
}

// 点赞评论/取消点赞评论
export function likeCourseComment(data) {
  return request({
    url: `/common-service/user/action/${data.business_type}/${data.business_id}/${data.operation}/state`,
    method: "post",
    data,
  });
}

// 课程上下架
export function updateCourseStatus(data) {
  return request({
    url: `/user-front-service/courses/updateCoursesStatus/${data.coursesId}/${data.loadStatus}`,
    method: "put",
    data,
  });
}

// 更新专栏上下架状态
export function updateColumnStatus(data) {
  return request({
    url: `/user-front-service/updateColumnsStatus/${data.columnsId}/${data.loadStatus}`,
    method: "put",
    data,
  });
}

// 新增或更新专栏
export function addOrUpdateColumn(data) {
  return request({
    url: `/user-front-service/addOrUpdateColumns`,
    method: "post",
    data,
  });
}

// 查看专栏详情
export function getColumnDetail(data) {
  return request({
    url: `/user-front-service/getColumnsDetail/${data.columnsId}`,
    method: "get",
    data,
  });
}

// 根据专栏id获取课程列表
export function getCourseListByColumnId(data) {
  return request({
    url: `/user-front-service/courses/getCoursesListByColumnsIds`,
    method: "get",
    data,
  });
}

// 专栏新增课程时--获取导师课程列表
export function getMentorCourseList(data) {
  return request({
    url: `/user-front-service/courses/getCoursesListByUserIdRelateColumns`,
    method: "get",
    data,
  });
}

// 移除课程从我的专栏
export function removeCourseFromColumn(data) {
  return request({
    url: `/user-front-service/removeCoursesFromColumns/${data.columnsId}/${data.coursesId}`,
    method: "DELETE",
    data,
  });
}

// 添加课程到我的专栏
export function addCourseToColumn(data) {
  return request({
    url: `/user-front-service/addCoursesToColumns`,
    method: "post",
    data,
  });
}

// 获取课程已关联的专栏列表
export function getCourseColumnList(data) {
  return request({
    url: `/user-front-service/getColumnsList/coursesId`,
    method: "get",
    data,
  });
}

// 动态分页列表
export function getDynamicList(data) {
  return request({
    url: `/user-front-service/mentor/dynamic/list/`,
    method: "get",
    data,
  });
}

// 导师动态语音播放次数 + 1
export function addDynamicAudioVoicePlayCount(data) {
  return request({
    url: `/user-front-service/student/dynamic/audio/play/${data.dynamicId}`,
    method: "post",
    data,
  });
}

// 点赞动态
export function thumbUpDynamic(data) {
  return request({
    url: `/user-front-service/student/dynamic/thumbUp/${data.dynamicId}`,
    method: "post",
    data,
  });
}

// 取消点赞动态
export function cancelThumbUpDynamic(data) {
  return request({
    url: `/user-front-service/student/dynamic/thumbDown/${data.dynamicId}`,
    method: "post",
    data,
  });
}

// 发布动态
export function addDynamic(data) {
  return request({
    url: `/user-front-service/mentor/dynamic/add`,
    method: "post",
    data,
    loading: true
  });
}

// 动态消息未读数量
export function getDynamicMessageUnreadCount(data) {
  return request({
    url: `/user-front-service/dynamicNotifications/unread/count`,
    method: "get",
    data,
  });
}

// 动态消息列表
export function getDynamicMessageList(data) {
  return request({
    url: `/user-front-service/dynamicNotifications/list`,
    method: "get",
    data,
  });
}

// 动态标记已读
export function markDynamicAsRead() {
  return request({
    url: `/user-front-service/dynamicNotifications/read`,
    method: "PUT",
  });
} 

// 动态详情
export function getDynamicDetail(data) {
  return request({
    url: `/user-front-service/dynamic/detail/${data.dynamicId}`,
    method: "get",
    data,
  });
}

// 动态评论列表
export function getDynamicCommentList(data) {
  return request({
    url: `/user-front-service/dynamic/comments/list`,
    method: "get",
    data,
  });
}

// 添加动态评论
export function addDynamicComment(data) {
  return request({
    url: `/user-front-service/dynamic/comment/add`,
    method: "post",
    data,
  });
}

// 回复动态评论
export function replyDynamicComment(data) {
  return request({
    url: `/user-front-service/dynamic/reply/add`,
    method: "post",
    data,
  });
}

// 动态评论回复列表
export function getDynamicReplyList(data) {
  return request({
    url: `/user-front-service/dynamic/reply/list`,
    method: "get",
    data,
  });
}

// 删除动态
export function deleteDynamic(data) {
  return request({
    url: `/user-front-service/mentor/dynamic/delete/${data.dynamicId}`,
    method: "DELETE",
    data,
  });
}

// 编辑课程时获取已选择专栏列表--导师端
export function getSelectedColumnList(data) {
  return request({
    url: `/user-front-service/getColumnsList/columnsIds`,
    method: "post",
    data,
  });
}