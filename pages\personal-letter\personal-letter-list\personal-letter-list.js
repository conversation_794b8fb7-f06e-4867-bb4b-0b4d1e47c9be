// pages/personal-letter/personal-letter-list/personal-letter-list.js
import { getMsgList } from '~/api/im'
import eventBus from '~/utils/websocket/eventBus';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 分页参数
    messageList: [],
    pageSize: 20,
    pageNumber: 0,
    loading: false,
    finished: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },
  filterFileType(value) {
    let newValue = ''
    if (/\.(doc|docx|pdf|xls|xlsx|ppt|pptx)$/.test(value)) {
      newValue = '[文件]'
    } else if (/\.(jpg|jpeg|png)$/.test(value)) {
      newValue = '[图片]'
    } else {
      newValue = value
    }
    return newValue
  },
  async handleNewMessage(studentId) {
    try {
      const { data } = await getMsgList({ pageNumber: 1, pageSize: this.data.pageSize })
      if (data && data.length) {
        const newValue = data.filter(item => item.senderId === studentId)[0]
        const newMessageList = this.data.messageList.filter(item => item.senderId !== studentId )
        newValue.showText = this.filterFileType(newValue.content)
        newValue.unReadCount = Number(newValue.unReadCount)
        newMessageList.unshift(newValue)
        this.setData({
          messageList: newMessageList
        })
      }
    } catch (error) {
      console.log(error, 'error');
    } finally {
    }
  },
  toLetterDetail(event) {
    this.setData({
      [`messageList[${event.currentTarget.dataset.index}].unReadCount`]: 0
    })
    wx.navigateTo({
      url: `/pages/personal-letter/personal-letter-page/personal-letter-page?toUserId=${this.data.messageList[event.currentTarget.dataset.index].senderId}`,
    })
  },
  loadMore() {
    this.messageLoad()
  },
  async messageLoad() {
    if (this.data.loading) {
      return
    }
    if (this.data.finished) {
      return
    }
    this.setData({
      pageNumber: this.data.pageNumber + 1,
      loading: true
    })
    const { data } = await getMsgList({
      pageSize: this.data.pageSize,
      pageNumber: this.data.pageNumber
    })
    if (data && data.length > 0) {
      data.forEach(item => {
        if (/\.(doc|docx|pdf|xls|xlsx|ppt|pptx)$/.test(item.content)) {
          item.showText = '[文件]'
        } else if (/\.(jpg|jpeg|png)$/.test(item.content)) {
          item.showText = '[图片]'
        } else {
          item.showText = item.content
        }
        item.unReadCount = Number(item.unReadCount)
      })
      
      const newData = [...this.data.messageList, ...data]
      this.setData({
        messageList: newData
      })
    } else {
      wx.nextTick(() => {
        this.setData({
          finished: true
        })
      })
    }
    wx.nextTick(() => {
      this.setData({
        loading: false
      })
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.messageLoad()
    const that = this
    eventBus.on('onNewMessage', (message) => {
      // this.getUnreadCount()
      that.handleNewMessage(message.data)
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    eventBus.off('onNewMessage')
    this.setData({
      messageList: [],
      pageNumber: 0,
      finished: false,
      loading: false,
    })
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})