<!--pages/appoint/mentor-appoint/components/shop/shop.wxml-->
<view class="course-container {{ courses.length <= 1 ? 'course-container-min' : '' }}">

  <view class="special">
    <!-- 专栏标题 -->
    <view class="header">
      <text class="title">专栏</text>
      <view class="more" bindtap="onMoreTap" wx:if="{{total >= 1}}">
        更多
        <van-icon color='#777777' name="arrow" />
      </view>
      <view class="more more-add" bindtap="onCreateColumn" wx:else>
        <text>添加专栏</text>
        <van-icon color='#0057FF' name="add-o" size="20px" />
      </view>
    </view>

    <!-- 专栏课程列表 -->
    <view class="column-section" wx:if="{{columnCourses.length > 0}}">
      <view class="course-grid">
        <view class="course-item" wx:for="{{columnCourses}}" wx:key="id" bindtap="onCourseClick" data-id="{{item.id}}" data-type="course">
          <view class="image-wrapper">
            <view class="shadow-top"></view>
            <view class="shadow-top-second"></view>
            <image class="course-image" src="{{item.columnCoverUrl}}" mode="aspectFill" />
            <view class="course-stats">
              <view class="left-stats">
                <image class="icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/file-icon.png" />
                <text>{{item.courseCount}}</text>
              </view>
              <text class="student-count" wx:if="{{item.addCount>0}}">{{item.addCount}}人添加</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>



  <view class="course">
    <view class="tab-container">
      <view>
        <view class="tab {{activeTab === 'video' ? 'active' : ''}}" bindtap="switchTab" data-type="video">
          视频
        </view>
        <view class="tab {{activeTab === 'article' ? 'active' : ''}}" bindtap="switchTab" data-type="article">
          图文
        </view>
      </view>
      <view class="filter" bindtap="onFilterTap">
        {{filterText}}
        <van-icon name="{{showFilter ? 'arrow-up' :'arrow-down'}}" />
        <view class="filter-dropdown {{showFilter ? 'show' : ''}} {{ courses.length <= 1 ? 'filter-dropdown-top' : '' }}">
          <view class="filter-item {{courseStatus === '' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="">全部</view>
          <view class="filter-item {{courseStatus === '1' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="1">已上架</view>
          <view class="filter-item {{courseStatus === '0' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="0">已下架</view>

          <view class="filter-item {{courseStatus === '2' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="2">审核中</view>
          <view class="filter-item {{courseStatus === '3' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="3">审核失败</view>
        </view>
      </view>
    </view>

    <!-- 视频课程列表 -->
    <view class="video-section" wx:if="{{courses.length > 0}}">
      <view class="course-list">
        <view class="course-item" wx:for="{{courses}}" wx:key="id" bindtap="onCourseClick" data-id="{{item.id}}">
          <view class="image-wrapper">
            <image class="course-image" src="{{item.courseCoverUrl}}" mode="aspectFill" />
            <view class="course-mask" wx:if="{{item.auditStatus===20 && item.loadStatus===0}}">已下架</view>
            <view class="course-mask" wx:if="{{item.auditStatus===10}}">审核中</view>
            <view class="course-mask" wx:if="{{item.auditStatus===30}}">审核失败</view>
          </view>
          <view class="course-info">
            <view class="course-meta">
              <view class="course-title">{{item.courseName}}</view>
              <!-- <view class="duration" wx:if="{{activeTab === 'article'}}" style="opacity: 0;">111</view> -->
              <view class="duration" wx:if="{{activeTab === 'video'}}">{{item.courseDuration}}</view>
              <view class="price-box">
                <text class="currency" wx:if="{{item.coursePrice>0}}">¥</text>
                <text class="price">{{item.saleMethod === 10 ? item.coursePrice:'免费'}}</text>
              </view>
            </view>
            <view class="right">
              <view class="rating">
                <text class="score">评分：</text>
                <text class="value">{{item.avgScore>0?item.avgScore:'暂无评'}}分</text>
              </view>
              <view class="btn-box">
                <view class="student-count" wx:if="{{item.addCount>0}}">{{item.addCount}}人购买</view>
                
                <view wx:if="{{ item.auditStatus === 20 && item.loadStatus === 0 && item.shelfReason }}" class="right-bottom gray" catch:tap="showReason" data-course-id="{{item.id}}" data-course-type="{{item.courseType}}" data-status="{{item.loadStatus}}" data-index="{{index}}" data-shelf-reason="{{item.shelfReason}}">
                  <block>
                    查看原因
                  </block>
                </view>
                <view wx:if="{{ item.auditStatus === 20 && item.loadStatus === 0 && !item.shelfReason }}" class="right-bottom offline" catch:tap="showReason" data-course-id="{{item.id}}" data-course-type="{{item.courseType}}" data-status="{{item.loadStatus}}" data-index="{{index}}" data-shelf-reason="{{item.shelfReason}}">
                  <block>
                    上架
                  </block>
                </view>
                <view wx:if="{{ item.auditStatus === 20 && item.loadStatus === 1}}" class="right-bottom online" catch:tap="showReason" data-course-id="{{item.id}}" data-course-type="{{item.courseType}}" data-status="{{item.loadStatus}}" data-index="{{index}}" data-shelf-reason="{{item.shelfReason}}">
                  <block>
                    下架
                  </block>
                </view>

                <view wx:if="{{ item.auditStatus === 30 }}" class="right-bottom gray" catch:tap="reviewFailedReason" data-course-id="{{item.id}}" data-course-type="{{item.courseType}}" data-course-type="{{item.courseType}}" data-shelf-reason="{{item.auditFailReason}}">
                  <block>
                    查看原因
                  </block>
                </view>
              </view>

              <!-- <view class=" right-bottom {{item.shelfReason?'gray':item.loadStatus === 0 ? 'offline' : item.loadStatus === 1 ? 'online' :''}}" catch:tap="showReason" data-course-id="{{item.id}}" data-course-type="{{item.courseType}}" data-status="{{item.loadStatus}}" data-index="{{index}}" data-shelf-reason="{{item.shelfReason}}">
                <block wx:if="{{item.shelfReason}}">
                  查看原因
                </block>
                <block wx:else>
                  {{item.loadStatus === 0 ? '上架' : '下架'}}
                </block>
              </view> -->
            </view>
          </view>
        </view>
      </view>
      <view class="finishedText" wx:if="{{loading}}">
        <text>加载中...</text>
        <!-- <text wx:elif="{{finished && courses.length > 0}}">暂无更多</text> -->
      </view>
    </view>

    <van-loading class="loading" type="spinner" vertical color="#0057FF" size="25px" wx:elif="{{loading && courses.length === 0}}"></van-loading>

    <view class="video-section" wx:elif="{{!loading && courses.length === 0}}">
      <custom-empty-new padding='66rpx' describe="{{activeTab === 'video' ? '暂无视频' : '暂无图文'}}" />
    </view>
  </view>

</view>