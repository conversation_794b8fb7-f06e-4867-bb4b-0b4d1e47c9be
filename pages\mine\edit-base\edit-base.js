// pages/mentor-in/base-info/base-info.js
import { getUserFullInfo, getIdList, updateUserBaseInfo, getFieldList } from '~/api/user'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfoPopupValue: {
      showId: false, // 展示身份选择弹框
      fieldPopup: false, // 展示选择擅长领域弹框
    },

    autosize: {
      maxHeight: 1000,
      minHeight: 120,
    },


    identitySelectList: [], // 导师全部身份的列表
    fieldList: [], // 擅长领域全部列表
    idDefaultIndex: 0,	
    userInfo: {
      avatarPath: '', // 导师头像
      userName: '', // 导师姓名
      phone: '', // 导师电话
      expertise: '', // 擅长领域的文本
      expertiseIdList: [], // 擅长领域的id集合
      identityName: '', // 导师身份
      identityId: '', // 导师身份id
      companyName: '', // 工作单位
      positionName: '', // 职位名称
      identityNumber: '', // 身份证号
      bankCardNumber: '', // 银行卡号
      bankCardOpeningBank: '', // 银行卡开户行
      introduction: '' // 个人简介
    }
  },
  // 擅长领域相关
  fieldChange(event) {
    const item = event.currentTarget.dataset.item
    const index = event.currentTarget.dataset.index
    if (item.active) {
      // item.active = false
      this.setData({
        [`fieldList[${index}].active`]: false
      })
    } else {
      const activeLength = this.data.fieldList.filter(item => item.active).length
      if (activeLength >= 3) {
        // this.$toast('擅长领域最多选择三个')
        wx.showToast({
          title: '擅长领域最多选择三个',
          icon: 'none'
        })
      } else {
        // item.active = true
        this.setData({
          [`fieldList[${index}].active`]: true
        })
      }
    }
  },
  // 重置选择
  resetField() {
    const fieldList = this.data.fieldList
    if (fieldList.length > 0) {
      fieldList.forEach(item => {
        item.active = false
        this.data.userInfo.expertiseIdList.forEach(value => {
          if (item.skillFieldId === value.skillFieldId) {
            item.active = true
          }
        })
      })
    }
    this.setData({
      fieldList: fieldList
    })
  },
  fieldConfirm() {
    // this.userInfo.expertiseIdList = this.fieldList.filter(item => item.active)
    const expertiseIdList = this.data.fieldList.filter(item => item.active)
    this.setData({
      'userInfo.expertiseIdList': expertiseIdList
    })
    this.getExpertise()
    this.popupClose()
  },
  getExpertise() {
    const nv = this.data.userInfo.expertiseIdList
    if (nv.length > 0) {
      let expertiseStr = ''
      nv.forEach((item, index) => {
        if (index === 0) {
          expertiseStr = item.skillFieldName
        } else {
          expertiseStr += '/' + item.skillFieldName
        }
      })
      // this.userInfo.expertise = expertiseStr
      this.setData({
        'userInfo.expertise': expertiseStr
      })
    } else {
      this.setData({
        'userInfo.expertise': ''
      })
      // this.userInfo.expertise = ''
    }
  },
  IdConfirm(event) {
    this.setData({
      'userInfo.identityId': event.detail.value.id,
      'userInfo.identityName': event.detail.value.dataValue
    })
    this.popupClose()
  },
  popupClose() {
    this.setData({
      'userInfoPopupValue.showId': false,
      'userInfoPopupValue.fieldPopup': false
    })
  },
  showFieldPopup() {
    this.setData({
      'userInfoPopupValue.fieldPopup': true
    })
  },
  showIdPopup() {
    this.setData({
      'userInfoPopupValue.showId': true
    })
  },
  onChange(event) {
    this.setData({
      [`userInfo.${event.currentTarget.dataset.key}`]: event.detail
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getUserFullInfo()
    // this.getIdList()
  },
  // 获取身份列表
  async getIdList() {
    const { data } = await getIdList({
      dataType: 'mentor_identity'
    })
    if (this.data.userInfo.identityId) {
      const defaulutSelectIndex = data.findIndex(item => {
        return item.id === this.data.userInfo.identityId
      })
      this.setData({
        idDefaultIndex: defaulutSelectIndex
      })
    }
    this.setData({
      identitySelectList: data
    })
  },
  // 获取所有的用户基本信息
  async getUserFullInfo() {
    const { data } = await getUserFullInfo()
    const baseInfo = data.mentorBase
    if (baseInfo) {
      for(const key in this.data.userInfo) {
        if (baseInfo[key] || baseInfo.user[key]) {
          this.setData({
            [`userInfo.${key}`]: baseInfo[key] || baseInfo.user[key]
          })
          // this.userInfo[key] = baseInfo[key] || baseInfo.user[key]
        }
      }
    }
    // 擅长领域相关
    this.setData({
      'userInfo.expertiseIdList': data.mentorSkillFieldList
    })
    this.getExpertise()
    this.getFieldList()
    this.getIdList()
  },
  async getFieldList() {
    const { data } = await getFieldList()
    if (data.length > 0) {
      data.forEach(item => {
        item.skillFieldId = item.id
        item.active = false
        const expertiseIdList = this.data.userInfo.expertiseIdList
        expertiseIdList.forEach(value => {
          if (item.skillFieldId === value.skillFieldId) {
            item.active = true
          }
        })
        this.setData({
          'userInfo.expertiseIdList': expertiseIdList
        })
        // this.getExpertise()
      })
    }
    this.setData({
      fieldList: data
    })
    // this.fieldList = data
  },
  editCancel() {
    wx.navigateBack()
  },
  async editBaseInfo() {
    for(const key in this.data.userInfo) {
      if (key === 'identityNumber' || key === 'bankCardNumber' || key === 'bankCardOpeningBank') {
        if (this.data.userInfo['identityNumber'] && this.data.userInfo['identityNumber'].length < 18) {
          // this.$toast('请填写正确格式的身份证号码')
          wx.showToast({
            title: '请填写正确格式的身份证号码',
            icon: 'none'
          })
          return
        }
      } else {
        if (Object.prototype.toString.call(this.data.userInfo[key]) === '[object Array]') {
          if (this.data.userInfo[key].length <= 0) {
            // this.$toast('请完善基本信息')
            wx.showToast({
              title: '请完善基本信息',
              icon: 'none'
            })
            return
          }
        } else if (Object.prototype.toString.call(this.data.userInfo[key]) === '[object String]') {
          if (this.data.userInfo[key].trim() === '') {
            // this.$toast('请完善基本信息')
            wx.showToast({
              title: '请完善基本信息',
              icon: 'none'
            })
            return
          }
        } else {
          if ((this.data.userInfo[key] ?? '') === '') {
            // this.$toast('请完善基本信息')
            wx.showToast({
              title: '请完善基本信息',
              icon: 'none'
            })
            return
          }
        }
      }
    }
    const fieldList = []
    this.data.userInfo.expertiseIdList.forEach(item => {
      fieldList.push(item.skillFieldId)
    })
    const submitObject = this.data.userInfo
    submitObject.skillFieldIdList = fieldList
    const { success } = await updateUserBaseInfo(submitObject)
    if (success) {
      wx.showToast({
        title: '修改成功',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1000);
      // wx.navigateTo({
      //   url: '/pages/mentor-in/education/education',
      // })
      // this.$router.push({ name: 'mentorInEducation' })
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})