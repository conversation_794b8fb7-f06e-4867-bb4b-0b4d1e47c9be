// pages/module-home/home/<USER>/mine/mine.js
import { getUserFullInfo, getMentorTopicList } from '~/api/user'
import { getAppointList, getMentorRecentlyAppointDate, getMentorCommentList } from '~/api/appoint'
import { unreadCount } from '~/api/im'
import moment from 'moment'
Component({

  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    showBusinessCardPopup: false,
    showAppointSetType: 1,
    appointTypeList: [
      {
        label: '电话咨询',
        id: 1
      },
      {
        label: '线下咨询',
        id: 2
      }
    ],
    commentList: [], // 评论的列表
    appointDateList: [], // 最近三个可预约的时间段
    appointList: [], // 预约列表
    topicList: [], // 话题列表
    userFullInfo: {
      mentorBase: {
        user: {}
      },
      mentorEducationList: [],
      mentorProfessionalList: [],
      mentorSkillFieldList: [],
      mentorTradeList: []
    },
    htmlContent: '',
    messageNumber: null,
    btnList: [],
    searchCard: {
      startTime: '',
      endTime: '',
      selectedId: null,
      selectedIndex: 0,
      selected: 0
    },
    showPicker: false,
    sheetShow: false,
    currentDate: new Date(),
    minDate: new Date(2020, 0, 1),
    maxDate: new Date(2030, 11, 31),
    filterName: '全部数据'
  },
  attached() {
    // this.getUserFullInfo()
    // this.getAppointList()
    // this.getMentorTopicList()
    // this.getMentorRecentlyAppointDate()
    // this.getMentorCommentList()
  },
  /**
   * 组件的方法列表
   */
  methods: {
    onPageShow() {
      console.log('页面初始化');
      this.getUserFullInfo()
      this.getAppointList()
      this.getMentorTopicList()
      this.getMentorRecentlyAppointDate()
      this.getMentorCommentList()
    },
    getHtmlValue(value) {
      const checkOption = value.replace(/\n/g, '<br/>')
      return checkOption
    },
    toTopicList() {
      wx.navigateTo({
        url: '/pages/mine/topic-list/topic-list',
      })
    },
    toCommentList() {
      wx.navigateTo({
        url: '/pages/mine/comment-list/comment-list',
      })
    },
    toAppointDateSet() {
      wx.navigateTo({
        url: '/pages/module-home/appoint/appoint-dateset/appoint-dateset',
      })
    },
    editExp(event) {
      if ((event.currentTarget.dataset.id ?? '') === '') {
        wx.navigateTo({
          url: `/pages/mine/topic-edit/topic-edit`,
        })
      } else {
        wx.navigateTo({
          url: `/pages/mine/topic-edit/topic-edit?id=${event.currentTarget.dataset.id}`,
        })
      }
    },
    toAppointDetail(event) {
      wx.navigateTo({
        url: `/pages/module-home/appoint/appoint-detail/appoint-detail?appointId=${event.currentTarget.dataset.item.id}`,
      })
    },
    async getUserFullInfo() {
      const { data } = await getUserFullInfo()
      if (data.mentorBase.introduction) {
        data.mentorBase.introduction = this.getHtmlValue(data.mentorBase.introduction)
      }
      this.setData({
        userFullInfo: data
      })
    },
    async getAppointList() {
      const { data } = await getAppointList({
        pageSize: 3,
        pageNumber: 1
      })
      this.setData({
        appointList: data
      })
    },
    async getMentorTopicList() {
      const { data } = await getMentorTopicList()
      this.setData({
        topicList: data.slice(0, 3)
      })
    },
    async getMentorRecentlyAppointDate() {
      const { data } = await getMentorRecentlyAppointDate()
      const obj = {
        10: '未设置',
        20: '已过期',
        30: '已设置',
        40: '已预约'
      }
      data.forEach(item => {
        item.setTime = item.startTime.hour < 10 ? '0' + item.startTime.hour + ':00' : item.startTime.hour + ':00'
        item.appointTypeText = obj[item.appointConfigStatus] || '已设置'
      })
      this.setData({
        appointDateList: data
      })
    },
    async getMentorCommentList() {
      const { data } = await getMentorCommentList({
        pageSize: 3,
        pageNumber: 1
      })
      if (data && data.length > 0) {
        data.forEach(item => {
          item.createdAtText = item.createdAt.split(' ')[0]
        })
      }
      this.setData({
        commentList: data || []
      })
    },
    toAppointList() {
      wx.navigateTo({
        url: '/pages/module-home/appoint/appoint-list/appoint-list',
      })
    },
  }
})