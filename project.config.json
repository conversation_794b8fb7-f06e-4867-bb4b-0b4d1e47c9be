{"compileType": "miniprogram", "libVersion": "trial", "setting": {"useCompilerPlugins": ["sass"], "coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./"}], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "ignoreUploadUnusedFiles": true}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "wxeb142aea5c19aa5f", "packOptions": {"ignore": [], "include": []}, "projectname": "small-program-mentor"}