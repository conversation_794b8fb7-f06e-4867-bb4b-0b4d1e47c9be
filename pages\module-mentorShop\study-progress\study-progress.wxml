<!--pages/module-mentorShop/study-progress/study-progress.wxml-->
<scroll-view scroll-y enhanced show-scrollbar="{{false}}" bounces="{{true}}" bindscrolltolower="loadMore" class="progress-container">

  <view class="progress-list" wx:if="{{studentList.length <= 0 && !loading}}">
    <empty text="暂无学员"></empty>
  </view>
  <view class="progress-list" wx:else>
    <view class="progress-item" wx:for="{{studentList}}" wx:key="index">
      <view class="top">
        <view class="item-left">
          <view class="index">{{index + 1}}</view>
          <image class="avatar" src="{{item.userAvatar}}" mode="aspectFill" />
          <view class="name">{{item.userName}}</view>
        </view>
        <view class="item-right">
          <view class="time" wx:if="{{item.videoDuration}}"><text>{{item.watchDurationFormatted}}</text>/{{item.videoDurationFormatted}}</view>
          <view class="time" wx:else><text>{{item.textReadProgress}}%</text></view>
        </view>
      </view>

      <view class="progress-bar">
          <view class="progress" wx:if="{{item.videoDuration}}" style="width:{{item.progress + '%'}}"></view>
          <view class="progress" wx:else style="width:{{ item.textReadProgress + '%'}}"></view>
        </view>
    </view>
    <view class="finishedText">
      <text wx:if="{{!finished}}">加载中...</text>
      <text wx:else>暂无更多</text>
    </view>
  </view>
</scroll-view>