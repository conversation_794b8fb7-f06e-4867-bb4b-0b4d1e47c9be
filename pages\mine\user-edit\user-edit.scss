.van-uploader{
  width: 100%;
}
.user-edit{
  padding: 28rpx 36rpx 0;
  .user-header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .user-edit-left{
      flex: 1;
      overflow: hidden;
      .user-upload{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        .avatar{
          width: 148rpx;
          height: 148rpx;
          border-radius: 50%;
          margin-right: 24rpx;
          image{
            border-radius: 50%;
            width: 100%;
            height: 100%;
          }
        }
        .user-name{
          flex: 1;
          overflow: hidden;
          .name{
            width: 100%;
            color: rgb(51, 51, 51);
            font-family: PingFang SC;
            font-size: 28rpx;
            font-weight: 700;
            line-height: 40rpx;
          }
          .tips{
            margin-top: 16rpx;
            color: rgb(115, 119, 131);
            font-size: 24rpx;
            font-weight: 500;
            line-height: 34rpx;
          }
        }
      }
    }
    .user-edit-right{
      width: 80rpx;
      height: 80rpx;
      margin-left: 24rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
  }
  .card-box{
    margin-top: 28rpx;
    padding: 30rpx;
    box-shadow: 0px 2px 16px 0px rgba(99, 97, 155, 0.1);
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    .header{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 24rpx;
      .name{
        font-weight: 600;
        font-size: 30rpx;
        color: #333333;
        line-height: 43rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .operation{
        font-weight: 400;
        font-size: 24rpx;
        color: #777777;
        line-height: 28rpx;
        text-align: center;
        font-style: normal;
        text-transform: none;
        image{
          margin-left: 2rpx;
          width: 20rpx;
          height: 20rpx;
        }
      }
      .add-more{
        display: flex;
        align-items: center;
        image{
          margin-left: 2rpx;
          width: 26rpx;
          height: 26rpx;
        }
        text{
          color: #0057FF;
          font-size: 24rpx;
          font-weight: 400;
          line-height: 34rpx;
        }
      }
    }
    .padding-bottom{
      padding-bottom: 32rpx;
      border-bottom: 2rpx solid #e7ecef;
    }
    .base-info-content{
      border-top: 2rpx solid rgb(231, 236, 239);
      .base-info-item{
        margin-top: 24rpx;
        display: flex;
        justify-content: space-between;
        color: rgb(119, 119, 119);
        font-size: 28rpx;
        font-weight: 400;
        line-height: 44rpx;
        margin-right: 16rpx;
        .base-info-item-value{
          margin-left: 20rpx;
          text-align: right;
          flex: 1;
          color: rgb(51, 51, 51);
        }
        .base-info-item-value-field{
          span{
            &::after{
              content: '/'
            }
            &:nth-last-child(1) {
              &::after{
                display: none;
              }
            }
          }
        }
      }
      .base-info-item-textarea{
        display: flex;
        flex-direction: column;
        .base-info-item-textarea-value{
          text-align: left;
          color: rgb(51, 51, 51);
          margin-top: 12rpx;
          rich-text{
            color: rgb(51, 51, 51);
            word-break: break-all;
          }
        }
      }
    }
    .experience-content{
      .experience-item{
        margin-top: 20rpx;
        background: rgb(248, 249, 254);
        border-radius: 12rpx;
        padding: 24rpx;
        .experience-item-title{
          display: flex;
          justify-content: space-between;
          .experience-item-title-value{
            color: rgb(51, 51, 51);
            font-size: 28rpx;
            font-weight: 400;
            line-height: 40rpx;
          }
          .experience-item-title-edit{
            display: flex;
            align-items: center;
            color: rgb(119, 119, 119);
            font-size: 24rpx;
            font-weight: 400;
            line-height: 34rpx;
            .text{
              margin-right: 8rpx;
              white-space: nowrap;
            }
            image{
              width: 20rpx;
              height: 20rpx;
            }
          }
        }
        .experience-item-position{
          margin-top: 18rpx;
          color: rgb(51, 51, 51);
          font-size: 24rpx;
          font-weight: 500;
          line-height: 34rpx;
          display: flex;
          align-items: center;
          .major{
            max-width: 60%;
            // @include textoverflow
          }
          .level{
            margin-left: 40rpx;
            max-width: 60%;
            // @include textoverflow
          }
        }
        .experience-item-date{
          margin-top: 6rpx;
          color: rgb(51, 51, 51);
          font-size: 24rpx;
          font-weight: 500;
          line-height: 34rpx;
        }
        .experience-item-discribe{
          margin-top: 18rpx;
          color: rgb(115, 119, 131);
          font-size: 24rpx;
          font-weight: 500;
          line-height: 34rpx;
        }
        &:nth-child(1) {
          margin-top: 0;
        }
      }
    }
    .empty{
      padding: 20rpx 0 60rpx;
    }
    // &:nth-last-child(2) {
    //   margin-bottom: 24rpx;
    // }
  }
  .copyright{
    margin-top: 24rpx;
  }
  .button{
    display: flex;
    align-items: center;
    padding: 24rpx 0 48rpx;
    van-button{
      overflow: hidden;
      width: 100%;
      // height: 100rpx;
      box-sizing: border-box;
      flex: 1;
      margin-right: 30rpx;
      button{
        font-size: 36rpx;
        // line-height: 48rpx;
        border-radius: 120rpx;
        width: 100%;
        height: 100rpx;
      }
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .info-btn{
      border: 1px solid #0057FF !important;
      background: #0057FF !important;
      color: rgb(255, 255, 255) !important;
    }
  }
}