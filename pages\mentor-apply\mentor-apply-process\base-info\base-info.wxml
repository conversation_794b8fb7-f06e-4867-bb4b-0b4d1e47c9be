<mentorInBoxApply previousBtnValue="导入简历" sliderValue="{{ 0 }}" back="{{ true }}" bind:pageNext="pageNext" bind:pageBack="pageBack">

  <van-popup show="{{ showCrop }}" bind:close="cancelShowCrop" custom-style="height: 100%; width: 100%">
    <crop imgFile="{{imgFile}}" upLoadFlag="{{true}}" bind:onCropperDone="handleCropperDone" bind:onCancel="cancelShowCrop"></crop>
  </van-popup>

  <van-popup
  show="{{ userInfoPopupValue.showId }}"
  position="bottom"
  bind:close="popupClose"
  >
    <van-picker value-key="dataValue" bind:cancel="popupClose" bind:confirm="IdConfirm" show-toolbar columns="{{ identitySelectList }}" default-index="{{ idDefaultIndex }}" />
  </van-popup>

  <van-popup
  show="{{ userInfoPopupValue.fieldPopup }}"
  position="bottom"
  bind:close="popupClose"
  round
  custom-class="field-select-box"
  >
    <view class="field-select">
      <view class="header">擅长领域</view>
      <view class="field-list">
        <view bind:tap="fieldChange" data-item="{{item}}" data-index="{{index}}" class="field-item {{item.active ? 'field-item-active' : '' }}" wx:for="{{ fieldList }}" wx:key="{{ index }}">{{ item.skillFieldName }}</view>
      </view>
      <view class="field-button">
        <van-button bind:tap="resetField" custom-class="default-btn" type="default">重置</van-button>
        <van-button bind:tap="fieldConfirm" custom-class="info-btn" type="info">确定</van-button>
      </view>
    </view>
  </van-popup>

  <view class="base-info">
    <!-- <van-uploader bind:after-read="afterRead"> -->
    <view class="user-portrait" bindtap="chooseImage">
      <image src="{{ userInfo.avatarPath }}" mode="aspectFill"></image>
    </view>
    <!-- </van-uploader> -->
    <view class="appoint-info-content">
      <van-field custom-class="required" input-align="right" border="{{ false }}" bind:change="onChange" data-key="userName" model:value="{{ userInfo.userName }}" maxlength="20" label="导师姓名" placeholder="请输入导师姓名" />

      <van-field custom-class="required" input-align="right" border="{{ false }}" bind:change="onChange" data-key="phone" model:value="{{ userInfo.phone }}" maxlength="20" label="手机号码" placeholder="请输入手机号码" readonly />

      <van-field custom-class="required" input-align="right" border="{{ false }}" value="{{ userInfo.identityName }}" readonly label="导师身份" placeholder="请选择身份" is-link bind:tap="showIdPopup"/>

      <van-field custom-class="required" input-align="right" border="{{ false }}" value="{{ userInfo.expertise }}" readonly label="擅长领域" placeholder="请选择擅长领域" is-link bind:tap="showFieldPopup"/>

      <van-field custom-class="required" input-align="right" border="{{ false }}" bind:change="onChange" data-key="companyName" model:value="{{ userInfo.companyName }}" maxlength="40" label="工作单位" placeholder="请输入工作单位" />

      <van-field custom-class="required" input-align="right" border="{{ false }}" bind:change="onChange" data-key="positionName" model:value="{{ userInfo.positionName }}" maxlength="40" label="职位名称" placeholder="请输入职位名称" />

      <van-field input-align="right" border="{{ false }}" bind:change="onChange" data-key="identityNumber" model:value="{{ userInfo.identityNumber }}" maxlength="18" label="身份证号" placeholder="请输入身份证号" />

      <van-field input-align="right" border="{{ false }}" bind:change="onChange" data-key="bankCardNumber" model:value="{{ userInfo.bankCardNumber }}" maxlength="30" label="银行卡号" placeholder="请输入银行卡号" />

      <van-field input-align="right" border="{{ false }}" bind:change="onChange" data-key="bankCardOpeningBank" model:value="{{ userInfo.bankCardOpeningBank }}" maxlength="30" label="银行卡开户行" placeholder="请输入银行卡开户行" />

      <van-field
      autosize="{{ autosize }}"
      maxlength='500'
      bind:change="onChange" data-key="introduction"
      model:value="{{ userInfo.introduction }}"
      label="个人简介" 
      placeholder="请输入你的个人简介" 
      type="textarea"
      custom-class="required custom-textarea"/>

    </view>
  </view>
</mentorInBoxApply>
