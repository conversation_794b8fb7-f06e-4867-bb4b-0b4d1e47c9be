// 引入 request 文件
import request from '../utils/request';

// 未读消息数
export function unreadCount() {
  return request({
    url: '/user-front-service/im/msg/unread/count',
    method: 'get'
  })
}

// 私信列表
export function getMsgList(data) {
  return request({
    url: `/user-front-service/im/msg/my/list`,
    method: 'get',
    data
  })
}

// 获取聊天详情
export function msgDetail(data) {
  return request({
    url: `/user-front-service/im/msg/detail`,
    method: 'get',
    data
  })
}

export function msgSend(data) {
  return request({
    url: `/user-front-service/im/msg/send`,
    method: 'post',
    data
  })
}

