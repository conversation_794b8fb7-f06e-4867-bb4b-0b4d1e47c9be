.login-selection{
	.login-bg{
		width: 100%;
		.bg{
			width: 100%;
			height: 100%;
		}
  }
	.login-content{
    margin-top: -40rpx;
		display: flex;
		flex-direction: column;
    align-items: center;
    padding: 0 64rpx 100rpx;
		.login-accredit{
      width: 100%;
			margin-bottom: 24rpx;
			width: 622rpx;
			height: 96rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #0057FF;;
			border-radius: 62rpx;
			font-weight: 600;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 32rpx;
			text-align: center;
      font-style: normal;
      margin-left: 0;
      margin-right: 0;
		}
		.login-phone{
			margin-bottom: 32rpx;
			width: 622rpx;
			height: 96rpx;
			border-radius: 62rpx;
			border: 2rpx solid rgba(0,87,255,0.5);
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 600;
			font-size: 32rpx;
			color: #0057FF;
			line-height: 32rpx;
			text-align: center;
			font-style: normal;
    }
    .agreement{
      // margin-top: 32rpx;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 13px;
      color: #999999;
      line-height: 15px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      width: 100%;
      .van-checkbox__label{
        padding-left: 12rpx;
      }
      // .van-checkbox{
      //   display: flex;
      //   align-items: center;
      //   .label-class{
      //     margin-left: 12rpx;
      //   }
      //   .van-checkbox__icon{
      //     width: 28rpx;
      //     height: 28rpx;
      //     font-size: 20rpx;
      //     display: flex;
      //     align-items: center;
      //     justify-content: center;
      //   }
      // }
      .text-value{
        font-weight: 400;
        font-size: 13px;
        color: #999999;
        line-height: 15px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      text{
        color: #0057FF;
      }
    }
    .tips{
      display: flex;
      align-items: center;
      margin-top: 12rpx;
      align-self: flex-start;
      image{
        width: 28rpx;
        height: 28rpx;
      }
      .tips-value{
        margin-left: 12rpx;
        font-weight: 500;
        font-size: 13px;
        color: #979797;
        line-height: 15px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
	}
}