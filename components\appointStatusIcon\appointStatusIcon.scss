.appoint-type{
  margin-left: 8px;
  border-radius: 2px;
  padding: 2px 7px;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}
.to-communicate{
  border: 1px solid rgb(15, 147, 216);
  color: rgb(15, 147, 216);
  background: rgba(15, 147, 216, 0.1);
}
.ongoing{
  background: rgba(31, 162, 26, 0.1);
  border: 0.5px solid rgb(31, 162, 26);
  color: rgb(31, 162, 26);
}
.wait-report{
  background: rgba(121, 121, 121, 0.1);
  border: 0.5px solid rgb(121, 121, 121);
  color: rgb(121, 121, 121);
}
.wait-comment{
  background: rgba(15, 147, 216, 0.1);
  border: 0.5px solid rgb(15, 147, 216);
  color: rgb(15, 147, 216);
}
.finished{
  color: #0057FF;
  background: rgba(107, 130, 246, 0.1);
  border: 0.5px solid #0057FF;
}
.cancel{
  background: rgba(121, 121, 121, 0.1);
  border: 0.5px solid rgb(121, 121, 121);
  color: rgb(121, 121, 121);
}