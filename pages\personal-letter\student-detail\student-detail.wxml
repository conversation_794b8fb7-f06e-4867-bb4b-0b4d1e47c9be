<view class="student-detail">
  <view class="student-basic-detail">
    <view class="left">
      <view class="student-name">{{ studentInfo.userName || '-' }}</view>
      <view class="school">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/school.png" mode="aspectFill" />
        <view class="p">
          <text>{{ studentInfo.school || '-' }}</text>
          <text></text>
          <text>{{ studentInfo.college || '-' }}</text>
        </view>
      </view>
      <view class="major">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/major.png" mode="aspectFill" />
        <view class="p">{{ studentInfo.major || '-' }}</view>
      </view>
      <view class="phone">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/phone.png" mode="aspectFill" />
        <view class="p">{{ studentInfo.phone || '-' }}</view>
      </view>
    </view>
    <image class="student-img" src="{{studentInfo.avatarPath}}" mode="aspectFill" />
  </view>
  <view class="student-detail">
    <!-- 基本信息 -->
    <view class="basic">
      <view class="title">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/information.png" mode="aspectFill" />
        <text>基本信息</text>
      </view>
      <view class="basic-content">
        <view class="line-item">邮箱：{{ studentInfo.email || '-' }}</view>
        <view class="line-item">生日：{{ studentInfo.newBirthday  || '-'}}</view>
        <view class="line-item">性别：{{ studentInfo.newSex  || '-'}}</view>
        <view class="line-item">政治面貌：{{ studentInfo.newPoliticalOutlook || '-'}}</view>
        <view class="line-item">身份证号码：{{ studentInfo.identityNumber || '-' }}</view>
        <view class="line-item">所在城市：{{ studentInfo.cityName || '-' }}</view>
        <view class="line-item">入学年份：{{ studentInfo.enrolDate || '-' }}</view>
        <view class="line-item">届次：{{ studentInfo.session || '-' }}</view>
      </view>
    </view>
    <!-- 求职期望 -->
    <view class="employment">
      <view class="title">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/job.png" mode="aspectFill" />
        <text>求职期望</text>
      </view>
      <view wx:if="{{studentDetail.jobExpectation && studentDetail.jobExpectation.length > 0}}">
        <view class="student-module basic-content" wx:for="{{ studentDetail.jobExpectation }}" wx:key="{{ index }}">
          <view class="line-item">职位类型：{{ item.newNature }}</view>
          <view class="line-item">期望职位：{{ item.title }}</view>
          <view class="line-item">意向城市：{{ item.city }}</view>
          <!-- <view class="line-item">期望月薪：{{ item.lowSalary / 1000 + 'k-' + item.upperSalary / 1000 + 'k' }}</view> -->
          <view class="line-item">期望月薪：{{ item.lowSalary / 1000 + 'k-' + item.upperSalary / 1000 + 'k' }}</view>
        </view>
      </view>
      <view wx:else class="line-item">暂无求职期望</view>
    </view>
    <!-- 教育经历 -->
    <view class="education">
      <view class="title">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/educational.png" mode="aspectFill" />
        <text>教育经历</text>
      </view>
      <view wx:if="{{ studentDetail.candidateEducation && studentDetail.candidateEducation.length > 0 }}">
        <view class="student-module education-content" wx:for="{{ studentDetail.candidateEducation }}" wx:key="{{ index }}">
          <view class="name">{{ item.universityName }}</view>
          <view class="time" wx:if="{{item.newStartDate && item.newEndDate}}">
            {{ item.newStartDate }}-{{ item.newEndDate }}
          </view>
          <view class="introduce introduce1">
            <text class="position-item" wx:if="{{item.address}}">{{ item.address }}</text>
            <text class="position-item" wx:if="{{item.majorName}}">{{ item.majorName }}</text>
            <!-- <text class="position-item" wx:if="{{item.educationLevel}}">{{ item.educationLevel | filterEducationLevel }}</text> -->
            <text class="position-item" wx:if="{{item.newEducationLevel}}">学历</text>
            <text class="position-item" wx:if="{{item.college}}">{{ item.college }}</text>
          </view>
        </view>
      </view>
      <view wx:else class="line-item">暂无教育经历</view>
    </view>
    <!-- 工作经历 -->
    <view class="job">
      <view class="title">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/work.png" mode="aspectFill" />
        <text>工作经历</text>
      </view>
      <view wx:if="{{studentDetail.candidateProfessional && studentDetail.candidateProfessional.length > 0}}">
        <view class="student-module" wx:for="{{studentDetail.candidateProfessional}}" wx:key="{{ index }}">
          <view class="name">{{ item.companyName || '-' }}</view>
          <view class="introduce introduce1">
            <text class="position-item" wx:if="{{item.city}}">{{ item.city }}</text>
            <text class="position-item" wx:if="{{item.department}}">{{ item.department }}</text>
            <text class="position-item" wx:if="{{item.positionName}}">{{ item.positionName }}</text>
          </view>
          <!-- <view class="time" wx:if="{{item.startDate && item.endDate}}">{{ item.startDate | filterDate }}-{{ item.endDate | filterDate }}</view> -->
          <view class="time" wx:if="{{item.newStartDate && item.newEndDate}}">{{ item.newStartDate }}-{{ item.newEndDate }}</view>
          <!-- <view class="introduce" v-html="item.professionalIntroduction" v-if="item.professionalIntroduction"></view> -->
          <view class="introduce">
            <rich-text nodes="{{item.newProfessionalIntroduction}}"/>
          </view>
        </view>
      </view>
      <view wx:else class="line-item">暂无工作经历</view>
    </view>
    <!-- 社团和组织经历 -->
    <view class="mass">
      <view class="title">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/club.png" mode="aspectFill" />
        <text>社团和组织经历</text>
      </view>
      <view wx:if="{{studentDetail.candidateOrganization && studentDetail.candidateOrganization.length > 0}}">
        <view class="student-module" wx:for="{{ studentDetail.candidateOrganization }}" wx:key="{{index}}">
          <view class="name">{{ item.organizationName || '-' }}</view>
          <view class="introduce introduce1">
            <text class="position-item" wx:if="{{item.city}}">{{ item.city }}</text>
            <text class="position-item" wx:if="{{item.department}}">{{ item.department }}</text>
            <text class="position-item" wx:if="{{item.role}}">{{ item.role }}</text>
          </view>
          <!-- <view class="time" v-if="item.startDate && item.endDate">{{ item.startDate | filterDate }}-{{ item.endDate | filterDate }}</view> -->
          <view class="time" wx:if="{{item.newStartDate && item.newStartDate}}">{{ item.newStartDate }}-{{ item.newEndDate }}</view>
          <!-- <view class="introduce" v-html="item.professionalIntroduction" v-if="item.professionalIntroduction"></view> -->
          <view class="introduce">
            <rich-text nodes="{{item.newProfessionalIntroduction}}"/>
          </view>
        </view>
      </view>
      <view wx:else class="line-item">暂无社团和组织经历</view>
    </view>
    <!-- 其他 -->
    <view class="other">
      <view class="title">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/other.png" mode="aspectFill" />
        <text>其他</text>
      </view>
      <view class="introduce introduce1" wx:if="{{studentDetail.other}}">
        <!-- <text class="expectation-other" v-html="getHtmlValue(studentDetail.other)"></text> -->
        <text class="expectation-other">
          <!-- {{studentDetail.other}} -->
          <rich-text nodes="{{studentDetail.newOther}}"/>
        </text>
      </view>
      <view wx:else class="line-item">暂无</view>
    </view>
  </view>
</view>