import request from '../utils/request';

// 获取导师的预约列表
export function getAppointList(data) {
  return request({
    url: '/appoint-front-service/mentor/appoint/log/list',
    method: 'get',
    data
  })
}

// 获取导师最近三段的可预约时间
export function getMentorRecentlyAppointDate(data) {
  return request({
    url: '/appoint-front-service/mentor/appoint/config/latest/able',
    method: 'get',
    data
  })
}

// 获取导师的评价列表
export function getMentorCommentList(data) {
  return request({
    url: '/appoint-front-service/appoint/comment/list',
    method: 'get',
    data
  })
}

// 导师评论上架
export function commentUp(data) {
  return request({
    url: `/appoint-front-service/appoint/comment/${data.appointCommentId}/up`,
    method: 'put',
    data
  })
}

// 导师评论下架
export function commentDown(data) {
  return request({
    url: `/appoint-front-service/appoint/comment/${data.appointCommentId}/down`,
    method: 'put',
    data
  })
}

// 获取预约详情
export function getAppointDetail(data) {
  return request({
    url: `/appoint-front-service/appoint/log/${data.appointLogId}`,
    method: 'get',
    data
  })
}

// 取消预约
export function cancelAppoint(data) {
  return request({
    url: `/appoint-front-service/mentor/appoint/log/${data.appointLogId}/mentor/cancel`,
    method: 'put',
    data
  })
}

// 填写咨询报告
export function sendAppointReport(data) {
  return request({
    url: `/appoint-front-service/appoint/${data.appointLogId}/report`,
    method: 'post',
    data
  })
}

// 填写咨询报告
export function getReportDetail(data) {
  return request({
    url: `/appoint-front-service/appoint/${data.appointLogId}/report`,
    method: 'get',
    data
  })
}

// 获取录音时长
export function getRecordTime(data) {
  return request({
    url: `/common-service/sound/record/${data.appointLogId}/duration/sum`,
    method: 'get',
    data
  })
}

// 在线会议剔除用户
export function removeRtc(data) {
  return request({
    url: `/appoint-front-service/appoint/rtc/remove/${data.appointLogId}`,
    method: 'get',
    data
  })
}
