<view style="width: 100%; height: 500rpx">
  <image-cropper
    id="image-cropper"
    bindload="cropperload"
    bindimageload="loadimage"
    bindtapcut="clickcut"
    limit_move="{{limit_move}}"
    disable_rotate="{{disable_rotate}}"
    width="{{width}}"
    height="{{height}}"
    imgSrc="{{src}}"
    angle="{{angle}}"
    disable_width="{{disable_width}}"
    max_width="{{max_width}}"
    max_height="{{max_height}}"
    disable_height="{{disable_height}}"
    disable_ratio="{{disable_ratio}}"
  >
  </image-cropper>
</view>
<text class="hint">点击中间裁剪框可查看裁剪后的图片</text>
<view class="bottom">
  <view bindtap="cancel">取消</view>
  <view bindtap="reset">还原</view>
  <image
    src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/upload/rotate.png"
    alt=""
    catchtouchstart="rotate"
    catchtouchend="end"
    data-type="rotate"
  />
  <van-button type="info"  color="#0057FF" bindtap="submit">确定</van-button>
</view>
<!-- <view class="bottom">
  <text style="color: white; margin-right: 5rpx">锁定裁剪框宽</text>
  <switch
    checked="{{disable_width?true:false}}"
    bindchange="switchChangeDisableWidth"
  />
  <text style="color: white; margin-right: 5rpx">锁定裁剪框高</text>
  <switch
    checked="{{disable_height?true:false}}"
    bindchange="switchChangeDisableHeight"
  />
  <button bindtap="submit">确定</button>
  <text style="color: white; margin-right: 5rpx">锁定比例</text>
  <switch
    checked="{{disable_ratio?true:false}}"
    bindchange="switchChangeDisableRatio"
  />
  <text></text>
  <text style="color: white; margin-right: 5rpx">锁定旋转</text>
  <switch
    checked="{{disable_rotate?true:false}}"
    bindchange="switchChangeDisableRotate"
  />
  <text style="color: white; margin-right: 5rpx">限制移动</text>
  <switch
    checked="{{limit_move?true:false}}"
    bindchange="switchChangeLimitMove"
  />
  <button
    class="button"
    catchtouchstart="top"
    catchtouchend="end"
    data-type="top"
  >
    上
  </button>
  <button
    class="button"
    catchtouchstart="bottom"
    catchtouchend="end"
    data-type="bottom"
  >
    下
  </button>
  <button
    class="button"
    catchtouchstart="left"
    catchtouchend="end"
    data-type="left"
  >
    左
  </button>
  <button
    class="button"
    catchtouchstart="right"
    catchtouchend="end"
    data-type="right"
  >
    右
  </button>
  <button
    class="button"
    catchtouchstart="enlarge"
    catchtouchend="end"
    data-type="enlarge"
  >
    放大
  </button>
  <button
    class="button"
    catchtouchstart="narrow"
    catchtouchend="end"
    data-type="narrow"
  >
    缩小
  </button>
  <button
    class="button"
    catchtouchstart="rotate"
    catchtouchend="end"
    data-type="rotate"
  >
    旋转
  </button>
  <button class="button" catchtap="upload">上传</button>
</view> -->
