// pages/mine/topic-edit/topic-edit.js
import { addMentorTopic, getMentorTopic, deleteMentorTopic, editMentorTopic } from '~/api/user'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    autosize: {
      maxHeight: 1000,
      minHeight: 120,
    },
    id: '',
    title: '', // 话题标题
    content: '', // 话题主要内容
    cueList: [
      {
        tips: ''
      }
    ], // 话题补充信息列表
    forceUploadAttachment: 1, // 是否强制上传附件
    attachmentDescription: '' // 附件描述
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options, 'options');
    this.setData({
      id: options.id || ''
    })
    if (this.data.id) {
      this.getMentorTopic()
    }
  },
  async getMentorTopic() {
    const { data } = await getMentorTopic({
      mentorTopicId: this.data.id
    })
    this.setData({
      title: data.title,
      content: data.content,
      forceUploadAttachment: data.forceUploadAttachment,
      attachmentDescription: data.attachmentDescription
    })
    if (data.cueList && data.cueList.length > 0) {
      const cueList = []
      data.cueList.forEach(item => {
        cueList.push({
          tips: item
        })
      })
      this.setData({
        cueList
      })
    }
  },
  cueChange(event) {
    this.setData({
      [`cueList[${event.currentTarget.dataset.index}].tips`]: event.detail
    })
  },
  onChange(event) {
    this.setData({
      [event.currentTarget.dataset.key]: event.detail
    })
  },
  deleteCueList(event) {
    const index = event.currentTarget.dataset.index
    const that = this
    wx.showModal({
      content: '确认删除补充信息？',
      async success (res) {
        if (res.confirm) {
          if (that.data.cueList.length > 1) {
            const cueList = that.data.cueList.filter((item, key) => key !== index)
            that.setData({
              cueList
            })
          } else {
            that.setData({
              'cueList[0].tips': ''
            })
          }
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  },
  addCueList() {
    const cueList = this.data.cueList
    cueList.push({
      tips: ''
    })
    this.setData({
      cueList
    })
  },


  attachmentRequireChange() {
    if (this.data.forceUploadAttachment === 0) {
      this.setData({
        forceUploadAttachment: 1
      })
    } else {
      this.setData({
        forceUploadAttachment: 0
      })
    }
  },
  expDelete() {
    const that = this
    wx.showModal({
      content: '确认删除该话题？',
      async success (res) {
        if (res.confirm) {
          const { success } = await deleteMentorTopic({
            mentorTopicId: that.data.id
          })
          if (success) {
            wx.showToast({
              title: '删除话题成功',
              icon: 'none'
            })
            setTimeout(() => {
              wx.navigateBack()
            }, 1000)
          }
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  },
  expBack() {
    wx.navigateBack()
  },
  async expEdit() {
    if(this.data.title.trim() === '') {
      // this.$toast('请输入话题标题')
      wx.showToast({
        title: '请输入话题标题',
        icon: 'none'
      })
      return
    }
    if(this.data.content.trim() === '') {
      // this.$toast('请输入话题描述')
      wx.showToast({
        title: '请输入话题描述',
        icon: 'none'
      })
      return
    }
    if (this.data.forceUploadAttachment === 1) {
      if(this.data.attachmentDescription.trim() === '') {
        // this.$toast('请输入附件信息描述')
        wx.showToast({
          title: '请输入附件信息描述',
          icon: 'none'
        })
        return
      }
    }
    const cueList = []
    if (this.data.cueList && this.data.cueList.length > 0) {
      this.data.cueList.forEach(item => {
        if (item.tips.trim() !== '') {
          cueList.push(item.tips)
        }
      })
    }
    if (this.data.id) {
      const { success } = await editMentorTopic({
        mentorTopicId: this.data.id,
        title: this.data.title,
        content: this.data.content,
        cueList,
        forceUploadAttachment: this.data.forceUploadAttachment,
        attachmentDescription: this.data.attachmentDescription
      })
      if (success) {
        // this.$toast('修改话题成功')
        wx.showToast({
          title: '修改话题成功',
          icon: 'none'
        })
        setTimeout(() => {
          // this.$router.back()
          wx.navigateBack()
        }, 1000)
      }
    } else {
      const { success } = await addMentorTopic({
        title: this.data.title,
        content: this.data.content,
        cueList,
        forceUploadAttachment: this.data.forceUploadAttachment,
        attachmentDescription: this.data.attachmentDescription
      })
      if (success) {
        // this.$toast('新增话题成功')
        wx.showToast({
          title: '新增话题成功',
          icon: 'none'
        })
        setTimeout(() => {
          // this.$router.back()
          wx.navigateBack()
        }, 1000)
      }
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})