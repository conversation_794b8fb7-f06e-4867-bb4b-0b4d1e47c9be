// pages/login-phone/login-phone.js
const regexp = require('~/utils/regexp')
import { getPhoneCode, login, register } from '~/api/login'
import { loginAfter } from '~/utils/loginAbout'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userName: '',
    value: '',
    phoneCode: '',
    countDown: null,
    checked: false
  },
  onChange(event) {
    this.setData({
      checked: !this.data.checked,
    })
  },
  toUserAgreement(e) {
    wx.navigateTo({
      url: `/pages/login/user-agreement/user-agreement`
    })
  },
  toUserPrivacy() {
    wx.navigateTo({
      url: `/pages/login/privacy-policy/privacy-policy`
    })
  },
  agreement(e) {
    wx.navigateTo({
      url: `${e.currentTarget.dataset.url}`
    })
  },
  getPhoneCode() {
    const regexpResult = regexp.regexpPhone(this.data.value)
    if (!regexpResult) {
      wx.showToast({
        title: '请输入正确格式的手机号',
        duration: 1500,
        icon: 'none'
      })
      return
    }
    getPhoneCode({
      phone: this.data.value
    }).then(res => {
      wx.showToast({
        title: '验证码发送成功',
        duration: 1500,
        icon: 'none'
      })
      let countDown = 60
      this.setData({
        countDown
      })
      let interval = setInterval(() => {
        if (countDown <= 1) {
          clearInterval(interval)
          this.setData({
            countDown: null
          })
          return
        }
        countDown--
        this.setData({
          countDown
        })
      }, 1000)
    }).catch(err => {
      console.log(err, 'err')
    })
  },
  register() {
    const regexpResult = regexp.regexpPhone(this.data.value)
    if (this.data.userName.trim() === '') {
      this.$toast('请填写导师姓名')
      return
    }
    if (!regexpResult) {
      wx.showToast({
        title: '请输入正确格式的手机号',
        duration: 1500,
        icon: 'none'
      })
      return
    }
    if (this.data.phoneCode.length < 6) {
      wx.showToast({
        title: '请输入6位短信验证码',
        duration: 1500,
        icon: 'none'
      })
      return
    }
    if (!this.data.checked) {
      wx.showToast({
        title: '请勾选《用户协议》、《隐私政策》',
        duration: 1500,
        icon: 'none'
      })
      return
    }
    register({
      userName: this.data.userName,
      phone: this.data.value,
      smsCode: this.data.phoneCode
    }).then(res => {
      loginAfter(res.data.accessToken)
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果没有参数的话，就走默认的那个注册流程，如果参数为review(查看状态), apply(提交审核)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})