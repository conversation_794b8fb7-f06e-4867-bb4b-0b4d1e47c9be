// components/mentorInBox/mentorInBox.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    back: {
      value: true
    },
    sliderValue: {
      value: 0
    },
    previousBtnValue: {
      value: '上一步'
    },
    nextBtnValue: {
      value: '下一步'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    pageBack() {
      // wx.navigateBack()
      this.triggerEvent('pageBack', { });
    },
    pageNext() {
      this.triggerEvent('pageNext', { });
    },
  }
})