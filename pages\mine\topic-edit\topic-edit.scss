.page{
  height: 100%;
}
.required{
  &::after{
    margin-left: 2rpx;
    padding-top: 6rpx;
    box-sizing: border-box;
    content: "*";
    font-size: 32rpx;
    color: rgb(233, 54, 54);
    line-height: 32rpx;
    font-weight: 400;
  }
}
.required{
  .van-cell__title{
    &::after{
      margin-left: -2rpx;
      padding-top: 6rpx;
      box-sizing: border-box;
      content: "*";
      font-size: 32rpx;
      color: rgb(233, 54, 54);
      line-height: 32rpx;
    }
  }
}
.exp-edit{
  background: #FFF;
  height: 100%;
  display: flex;
  flex-direction: column;
  .exp-content{
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    padding: 28rpx 36rpx 24rpx;
    .experience-edit{
      flex: 1;
      width: 100%;
      margin-bottom: 20rpx;
      .experience-edit-box{
        border-radius: 12rpx;
        padding: 0 30rpx;
        box-shadow: 0px 2px 16px 0px rgba(99, 97, 155, 0.1);
        .van-cell{
          padding: 28rpx 0;
          border-bottom: 2rpx solid #dfdfdf;
          .van-cell__title{
            width: 200rpx !important;
            margin: 0 !important;
            max-width: 200rpx !important;
            min-width: 200rpx !important;
            font-weight: 400;
          }
        }
        .custom-textarea{
          border: none;
          flex-direction: column;
          padding-top: 24rpx;
          padding-bottom: 24rpx;
          .van-field__control{
            margin-top: 8rpx;
            background: rgb(248, 249, 254);
            border-radius: 12rpx;
            padding: 26rpx;
          }
          &::after{
            display: none;
          }
        }
        van-field{
          &:nth-last-child(1) {
            .van-cell{
              border: none;
            }
          }
        }
      }
      .experience-card{
        margin-top: 28rpx;
        border-radius: 12rpx;
        padding: 28rpx 30rpx;
        box-shadow: 0px 2px 16px 0px rgba(99, 97, 155, 0.1);
        .cue-header{
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: rgb(51, 51, 51);
          font-size: 30rpx;
          font-weight: 600;
          line-height: 42rpx;
          display: flex;
          align-items: center;
          position: relative;
          // padding-bottom: 24rpx;
          .title{

          }
          .add-cue{
            color: #0057FF;
            font-size: 24rpx;
            font-weight: 400;
            line-height: 40rpx;
            display: flex;
            align-items: center;
            image{
              margin-top: 4rpx;
              width: 30rpx;
              height: 30rpx;
              margin-right: 4rpx;
            }
          }
          .appoint-required{
            color: #0057FF;
            font-size: 24rpx;
            font-weight: 400;
            line-height: 40rpx;
            display: flex;
            align-items: center;
            image{
              // margin-top: 4rpx;
              width: 24rpx;
              height: 24rpx;
              margin-right: 4rpx;
            }
          }
        }
        .warning-tips{
          margin-top: 10rpx;
          display: flex;
          align-items: center;
          color: rgb(233, 178, 99);
          font-size: 24rpx;
          font-weight: 500;
          line-height: 36rpx;
          image{
            width: 28rpx;
            height: 28rpx;
            margin-right: 4rpx;
          }
        }
        .input-box{
          display: flex;
          align-items: center;
          justify-content: space-between;
          .input-content{
            flex: 1;
            .van-cell{
              padding: 20rpx 0;
              border-bottom: 2rpx solid #e7ecef;
            }
          }
          image{
            width: 36rpx;
            height: 36rpx;
          }
        }
      }
    }
  }
  .exp-footer{
    box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0,0,0,0.05);
    background: #FFF;
    height: 160rpx;
    padding: 24rpx 36rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    van-button{
      overflow: hidden;
      height: 100%;
      flex: 1;
      margin-right: 40rpx;
      button{
        font-size: 36rpx;
        // line-height: 48rpx;
        border-radius: 120rpx;
        width: 100%;
        height: 100%;
      }
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .default-btn{
      border: 1px solid #0057FF;
      background: #fff;
      color: #0057FF;
    }
    .info-btn{
      border: 1px solid #0057FF !important;
      background: #0057FF !important;
      color: rgb(255, 255, 255) !important;
    }
  }
}