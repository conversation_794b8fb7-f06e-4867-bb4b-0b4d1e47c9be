// pages/module-mentorShop/study-progress/study-progress.js
import { getStudentList } from '~/api/mentorShop'
import moment from 'moment'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    studentList: [],
    courseId: '',
    loading: false,
    page: {
      pageSize: 20, // 每页数量
      pageNumber: 1, // 当前页码
    },
    finished: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 页面加载时初始化进度
    this.setData({
      courseId: options.courseId
    }, () => {
      this.getStudentList()
    })

  },
  async getStudentList() {
    if (this.data.loading) return;

    this.setData({
      loading: true,
    });

    const {
      data
    } = await getStudentList({
      ...this.data.page,
      courseId: this.data.courseId
    });

    if (data.length < this.data.page.pageSize) {
      this.setData({
        finished: true,
      });
    }

    // 避免重复数据
    const newList = this.data.page.pageNumber === 1 ?
      data :
      [...this.data.studentList, ...data];

    this.setData({
      studentList: newList,
      loading: false,
    }, () => {
      // 在设置完学生列表数据后初始化进度
      this.initProgress();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  loadMore () {
    if (this.data.loading || this.data.finished) return;
    this.setData(
      {
        'page.pageNumber': this.data.page.pageNumber + 1,
      },
      () => {
        this.getStudentList();
      }
    );
  },


  initProgress() {
    const progressList = this.data.studentList.map(item => {
      // 计算进度百分比
      const progress = (item.videoWatchDuration / item.videoDuration) * 100;
      
      // 使用moment格式化观看时长和视频总时长
      const watchDurationFormatted = moment.utc(item.videoWatchDuration * 1000).format('HH:mm:ss');
      const videoDurationFormatted = moment.utc(item.videoDuration * 1000).format('HH:mm:ss');

      const textReadProgress = `${(item.textReadProgress * 100).toFixed(0)}`;

      return {
        ...item,
        progress: Math.min(progress, 100),
        watchDurationFormatted,  // 添加格式化后的观看时长
        videoDurationFormatted,   // 添加格式化后的视频总时长
        textReadProgress // 添加格式化后的阅读进度
      };
    });
    this.setData({ studentList: progressList });
  },
  // 更新学习时间（当用户继续学习时调用）
  updateLearningTime(index, additionalSeconds) {
    const progressList = [...this.data.studentList];
    const item = progressList[index];

    // 累加学习时间
    item.videoWatchDuration += additionalSeconds;

    // 更新显示的时间格式
    const hours = Math.floor(item.videoWatchDuration / 3600);
    const minutes = Math.floor((item.videoWatchDuration % 3600) / 60);
    const seconds = item.videoWatchDuration % 60;
    item.currentTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    // 更新进度条
    item.progress = Math.min((item.videoWatchDuration / item.videoDuration) * 100, 100);

    this.setData({ studentList: progressList });
  }
})