<!--pages/login-phone/login-phone.wxml-->
<view class="login-phone">
  <view class="login-phone-bg">
    <image class="bg" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/mentor-app/login-bg.png" alt=""/>
  </view>
  <view class="phone-login-title">重置密码</view>
  <view class="login-form">
    <view class="form-item form-phone">
      <view class="label">+86</view>
      <view class="input">
        <van-field
          type="number"
          model:value="{{ value }}"
          placeholder="请输入手机号"
          border="{{ false }}"
          maxlength="11"
        />
      </view>
    </view>
    <view class="form-item form-code">
      <view class="input">
        <van-field
          type="number"
          model:value="{{ phoneCode }}"
          placeholder="请输入验证码"
          border="{{ false }}"
          maxlength="6"
        />
      </view>
      <view class="get-code get-code-disabled" wx:if="{{countDown}}">{{countDown}}</view>
      <view class="get-code" bindtap="getPhoneCode" wx:else>获取验证码</view>
    </view>
    <view class="form-item form-code">
      <view class="input">
        <van-field
          password="{{ true }}"
          model:value="{{ password }}"
          placeholder="新密码，6-16位，必须包含字母和数字"
          border="{{ false }}"
          maxlength="16"
        />
      </view>
    </view>
    <view class="form-item form-code">
      <view class="input">
        <van-field
          password="{{ true }}"
          model:value="{{ passwordRepeat }}"
          placeholder="重复确认密码"
          border="{{ false }}"
          maxlength="16"
        />
      </view>
    </view>
    <view class="login-btn" bind:tap="login">确定</view>
  </view>
</view>