<!--pages/module-mentorShop/add-article/add-article.wxml-->
<view class="video-box">
  <!-- 专栏 -->
  <view class="section">

    <!-- 模式选择 -->
    <view class="mode-section">
      <view class="section-title">模式设置<text>*</text></view>
      <view class="mode" wx:if="{{id}}">{{mode === 'article' ? '文章' : '图文'}}模式</view>
      <view class="mode-tabs" wx:else>
        <view class="tab">
          <view class="tab-item {{mode === 'article' ? 'active' : ''}}" bindtap="switchMode" data-mode="article">
            <view class="tab-content">
              <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/article-icon.png" mode="" />
              <text>文章模式</text>
            </view>
            <view class="check-icon" wx:if="{{mode === 'article'}}">
              <van-checkbox checked-color="#0057FF" value="{{ true }}" />
            </view>
            <!-- -->
          </view>
          <view class="mode-tips">
            <text class="link" bindtap="onViewTemplate" data-mode="article">查看模板</text>
          </view>
        </view>
        <view class="tab">
          <view class="tab-item {{mode === 'imageText' ? 'active' : ''}}" bindtap="switchMode" data-mode="imageText">
            <view class="tab-content">
              <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/imageText-icon.png" mode="" />
              <text>图文模式</text>
            </view>
            <view class="check-icon" wx:if="{{mode === 'imageText'}}">
              <van-checkbox checked-color="#0057FF" value="{{ true }}" />
            </view>
          </view>
          <view class="mode-tips">
            <text class="link" bindtap="onViewTemplate" data-mode="image">查看模板</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 文章名称 -->
    <view class="name-section article">
      <view class="section-title">{{mode === 'article'?'文章':'图文'}}名称<text>*</text></view>
      <view class="section-input">
        <van-field value="{{form.courseName}}" bind:change="handleArticleNameChange" maxlength="30" rows="1" autosize="{{autosize}}" type="textarea" placeholder="请输入{{mode === 'article'?'文章':'图文'}}名称" show-word-limit />
      </view>
    </view>

    <!-- 文章来源 -->
    <view class="name-section article" wx:if="{{mode === 'article'}}">
      <view class="section-title">文章来源</view>
      <view class="section-input">
        <van-field value="{{form.source}}" bind:change="handleArticleSourceChange" maxlength="500" rows="1" autosize="{{autosize}}" type="textarea" placeholder="请输入文章来源" show-word-limit />
      </view>
    </view>

    <!-- 文章作者 -->
    <view class="name-section article" wx:if="{{mode === 'article'}}">
      <view class="section-title">文章作者</view>
      <view class="section-input">
        <van-field value="{{form.author}}" bind:change="handleArticleAuthorChange" maxlength="16" rows="1" autosize="{{autosize}}" type="textarea" placeholder="请输入文章作者" show-word-limit />
      </view>
    </view>

    <van-cell is-link title="内容详情" link-type="navigateTo" bind:tap="onEditDetail" />

    <!-- 上传封面 -->
    <view class="cover-section">
      <view class="section-title">添加封面<text>*</text></view>
      <view class="upload-tip">（按照16:9的比例平铺展示，图片大小小于5M，仅支持JPG、PNG格式）</view>
      <view class="upload-area">
        <view wx:if="{{!form.courseCoverUrl}}" class="upload-btn" bindtap="chooseImage">
          <van-icon name="plus" color='#9EA3AC' size='30px' />
        </view>
        <view class="upload-image" wx:else>
          <van-icon name="cross" color='#fff' size='15px' bindtap="removeImage" />
          <image src="{{form.courseCoverUrl}}" mode="" />
        </view>
      </view>
    </view>
    <van-popup show="{{ showCrop }}" bind:close="cancelShowCrop" custom-style="height: 100%; width: 100%">
      <crop imgFile="{{imgFile}}" upLoadFlag="{{true}}" bind:onCropperDone="handleCropperDone" bind:onCancel="cancelShowCrop"></crop>
    </van-popup>
  </view>

  <!-- 售卖方式 -->
  <view class="sale-section">
    <view class="sale-type">
      <text>支持单独售卖</text>
      <van-switch size="24px" bind:change="handleSwitchChange" checked="{{form.isIndividualSale === 1}}" active-color="#0057FF" />
    </view>
    <view class="sale-sell" bindtap="showActionSheet">
      <text>售卖方式</text>
      <view class="price-tag">
        <text>{{currentSaleType}}</text>
        <van-icon name="arrow" color="#0057FF" />
      </view>
    </view>

    <view class="sale-price" wx:if="{{currentSaleType==='付费'}}">
      <view class="section-title">商品售价<text>*</text></view>
      <van-field value="{{form.coursePrice}}" bind:input="handlePriceChange" bind:blur="handlePriceBlur" type="textarea" placeholder="请输入售价(0.01-100000元)" right-icon="gold-coin-o" />
      <!-- <van-field value="{{form.coursePrice}}" bind:change="handlePriceChange" type="number" placeholder="请输入售价(0.01-100000元)" right-icon="gold-coin-o" /> -->
    </view>

    <view class="sale-type">
      <view class="title">
        <text>支持关联售卖</text>
        <van-icon bind:tap="showTip" data-type="association" color='#AAAAAA' size='18px' name="question-o" />
      </view>
      <van-switch size="24px" bind:change="handleAssociationChange" checked="{{form.isBundleSale === 1}}" active-color="#0057FF" />
    </view>
    <view class="sale-type" wx:if="{{form.isBundleSale === 1}}">
      <text>关联专栏</text>
      <view bindtap="onAddColumn">
        <view class="relevance" wx:if="{{!form.bundleColumnsIds || form.bundleColumnsIds.length  === 0}}">
          请选择
          <van-icon name="add-o" size='20px' color='#0057FF' />
        </view>
        <view class="relevance" wx:elif="{{form.bundleColumnsIds.length>0}}">
          已关联{{form.bundleColumnsIds.length}}个专栏
          <van-icon name="add-o" size='20px' color='#0057FF' />
        </view>
      </view>
    </view>
  </view>

  <!-- 其他 -->
  <view class="sale-section sale-other">
    <!-- <view class="sale-type">
      <view class="title">
        <text>文字防复制</text>
        <van-icon bind:tap="showTip" data-type="copy" color='#AAAAAA' size='18px' name="question-o" />
      </view>
      <van-switch size="24px" bind:change="handleCopyChange" checked="{{form.isTextNoCopy === 1}}" active-color="#0057FF" />
    </view> -->

    <view class="sale-type" wx:if="{{!id}}">
      <view class="title">
        <text>发布至动态</text>
      </view>
      <van-switch size="24px" bind:change="handleDynamicChange" checked="{{form.isPublishToDynamic === 1}}" active-color="#0057FF" />
    </view>
  </view>

  <view class="bottm-button-bar">
    <van-button bind:tap="onSave" color="#0057FF">下一步</van-button>
  </view>
</view>