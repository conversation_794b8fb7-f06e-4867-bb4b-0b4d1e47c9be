/**
 * OSS文件上传工具类
 */
import request from '../utils/request';
export async function webUpload(filePath, path, loadingType) {
  let credentials = null
  try {
    if (!loadingType) {
      wx.showLoading({
        title: '上传中',
        icon: 'loading',
        duration: 0
      })
    }
    if (isCredentialsExpired(credentials)) {
      const response = await request({
        url: '/common-service/generate_signature',
        method: 'GET'
      })
      if (!response.success) {
        throw new Error(`获取STS令牌失败`)
      }
      credentials = response.data
    }
    // 构建上传参数
    const timestamp = Date.now(); // 获取当前时间戳
    const fileExtension = filePath.substring(filePath.lastIndexOf('.'));
    const fileName = `webUpload/${path}`;
    const host = `${credentials.bucket}.${credentials.region}.aliyuncs.com`; 
    const url = `https://${host}`;

    const formData = {
      path:path,
      key: `${fileName}/${Math.round(Math.random() * 100000000)}${timestamp}${fileExtension}`,
      policy: credentials.policy,
      'x-oss-signature-version': credentials.x_oss_signature_version,
      'x-oss-credential': credentials.x_oss_credential,
      'x-oss-date': credentials.x_oss_date,
      'x-oss-signature': credentials.signature,
      'x-oss-security-token': credentials.security_token,
      success_action_status: "200"
    };
    // 执行上传
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: url,
        filePath: filePath,
        name: 'file',   //固定值为file
        formData: formData,
        success(res) {
          // console.log('上传响应:', res);
          if (res.statusCode === 200) {
            const obj = {
              url:`${url}/${formData.key}`
            }
            resolve(obj); // 上传成功
          } else {
            console.error('上传失败，状态码:', res.statusCode);
            console.error('失败响应:', res);
            reject(res); // 上传失败，返回响应
          }
        },
        fail(err) {
          console.error('上传失败:', err); // 输出错误信息
          wx.showToast({ title: '上传失败，请重试!', icon: 'none' });
          reject(err); // 调用回调处理错误
        }
      });
    })

  } catch (err) {
    console.error('文件上传失败:', err)
    if (!loadingType) {
      wx.hideLoading()
    }
    throw err
  }
}
function isCredentialsExpired(credentials) {
  if (!credentials) {
    return true;
  }
  const expireDate = new Date(credentials.expiration);
  const now = new Date();
  return expireDate.getTime() - now.getTime() <= 60000;
}