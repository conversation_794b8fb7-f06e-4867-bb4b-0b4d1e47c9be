// pages/module-home/appoint/appoint-detail/appoint-detail.js
import { getAppointDetail, cancelAppoint } from '~/api/appoint'
const docIcon = 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/upload/word.png'
const pdfIcon = 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/upload/pdf.png'
const excelIcon = 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/upload/excel.png'
import moment from 'moment'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    autosize: {
      maxHeight: 300,
      minHeight: 60,
    },
    appointId: '',
    appointDetail: {
      appointStatus: '',
      appointType: '',
      appointSignIn: {
        validDateTime: '',
        signInPassword: []
      },
      user: {
        phone: '',
        userName: '',
        sex: '',
        birthday: '',
        identityName: '',
        school: '',
        college: '',
        major: '',
      },
      appointLogProblem: {
        problemDescription: {
          basicInfo: "",
          coreProblem: "",
          pastExperience: "",
          needProvideHelp: "",
          other: ""
        },
        picUrlList: [],
        accessoryName: "",
        accessoryUrl: ""
      },
      mentorTopicSnapshot: {
        appointLogCueList: [
          // {
          //   problem: '',
          //   answer: '',
          // },
          // {
          //   problem: '',
          //   answer: '',
          // },
        ]
      }
    },
    // 取消预约
    showAppointCancelPopup: false,
    cancelRadio: 1,
    customReason: '',
    cancelAppointReasonList: [
      {
        id: 1,
        reason: '我的时间有变化'
      },
      {
        id: 2,
        reason: '咨询问题不明确'
      },
      {
        id: 3,
        reason: '其他原因'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.appointId) {
      this.setData({
        appointId: options.appointId
      })
    }
  },
  filterCancelReason(value) {
    const obj = {
      1: '导师取消：导师时间有变化',
      2: '导师取消：学生问题不明确',
      11: '学生取消：临时有事，不能按时咨询',
      12: '学生取消：预约其它导师了'
    }
    return obj[value]
  },
  reasonChange(event) {
    this.setData({
      cancelRadio: event.currentTarget.dataset.id
    })
  },
  showCancelAppointPopup() {
    this.setData({
      showAppointCancelPopup: true
    })
  },
  cancelAppointPopup(){
    this.setData({
      showAppointCancelPopup: false
    })
  },
  // 取消预约事件
  async toCancelAppoint() {
    if (this.data.cancelRadio === 3) {
      if (this.data.customReason.trim() === '') {
        // this.$toast('请填写取消原因')
        wx.showToast({
          title: '请填写取消原因',
          icon: 'none'
        })
        return
      }
    }
    const { success } = await cancelAppoint({
      appointLogId: this.data.appointId,
      appointCancelReason: this.data.cancelRadio,
      appointCancelDesc: this.data.customReason
    })
    if (success) {
      // this.$toast('取消成功')
      wx.showToast({
        title: '取消成功',
        icon: 'none'
      })
      // this.showAppointCancelPopup = false
      // location.reload()
      this.cancelAddressPopupValue()
      this.getAppointDetail()
    }
  },
  cancelAddressPopupValue() {
    this.setData({
      showAppointCancelPopup: false,
      cancelRadio: 1,
      customReason: '',
    })
  },
  async getAppointDetail() {
    const { data } = await getAppointDetail({
      appointLogId: this.data.appointId
    })
    if (data.appointType) {
      const obj = {
        10: '电话咨询',
        20: '线下咨询',
        30: '面对面咨询',
        40: '线上咨询'
      }
      data.appointTypeText = obj[data.appointType]
    }
    if (data.appointLogProblem.accessoryName && data.appointLogProblem.accessoryUrl) {
      data.fileTypeImage = this.iconType(data.appointLogProblem.accessoryName)
    }
    if (data.user.sex || data.user.sex === 0) {
      const sexObject = {
        0: '男',
        1: '女'
      }
      data.user.newSex = sexObject[data.user.sex]
    }
    if (data.user.birthday) {
      data.user.newBirthday = moment(data.user.birthday).format('YYYY')
    }

    if (data.appointCancelReason) {
      data.appointCancelReasonText = this.filterCancelReason(data.appointCancelReason )
    }
    this.setData({
      appointDetail: data
    })
  },
  iconType(fileName) {
    const name = fileName.toLowerCase()
    return name.includes('doc') || name.includes('docx') ? docIcon : name.includes('pdf') ? pdfIcon
      : name.includes('xlsx') || name.includes('xls') ? excelIcon : ''
  },
  imgPreview(event) {
    wx.previewImage({
      current: event.currentTarget.dataset.item, // 当前显示图片的http链接
      urls: this.data.appointDetail.appointLogProblem.picUrlList // 需要预览的图片http链接列表
    })
  },
  reportEdit() {
    wx.navigateTo({
      url: `/pages/module-home/appoint/appoint-report-edit/appoint-report-edit?appointId=${this.data.appointId}`,
    })
  },
  reportEcho() {
    wx.navigateTo({
      url: `/pages/module-home/appoint/appoint-report-echo/appoint-report-echo?appointId=${this.data.appointId}`,
    })
  },
  previewFile() {
    wx.downloadFile({
      url: this.data.appointDetail.appointLogProblem.accessoryUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            showMenu: true,
            success: () => {
            },
            fail: (err) => {
              // reject(new Error('打开文档失败：' + err.errMsg));
            }
          });
        } else {
          // reject(new Error('下载失败，状态码：' + res.statusCode));
        }
      },
      fail: (err) => {
        // reject(new Error('下载失败：' + err.errMsg));
      }
    });
  },
  // 开启咨询
  enterMeeting() {
    wx.navigateTo({
      url: `/pages/module-home/appoint/online-meeting/online-meeting?appointId=${this.data.appointId}`,
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getAppointDetail()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})