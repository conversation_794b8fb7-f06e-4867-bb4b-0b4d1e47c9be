<view class="home-page">
  <scroll-view scroll-y enhanced show-scrollbar="{{false}}" bounces="{{true}}" bindscrolltolower="loadMore" class="home-page-content">
    <view class="home-page-content">
      <view class="page-home-header">
        <view class="user-info">
          <view class="user-left">
            <image class="user-left" src="{{userFullInfo.mentorBase.user.avatarPath}}" mode="scaleToFill" catch:tap="showAvatar" />
          </view>

          <view class="user-right" bind:tap="userEdit">
            <view class="user-name">
              <view class="user-name-content">
                <view class="user-name-content-box">
                  <view class="name">{{userFullInfo.mentorBase.user.userName}}</view>
                  <view class="label" wx:if="{{userFullInfo.mentorBase && userFullInfo.mentorBase.user.identityName}}">{{userFullInfo.mentorBase.user.identityName}}</view>
                </view>
              </view>
              <view class="user-name-edit">
                <image src="/static/icon/edit.png" mode="" />
                <view>编辑</view>
              </view>
            </view>
            <view class="user-rate">
              <image wx:for="{{rate}}" src="{{item}}" />
              <text>{{ userFullInfo.mentorBase.score }}分</text>
            </view>
            <view class="user-company textoverflow" wx:if="{{userFullInfo.mentorBase.companyName}}">
              {{userFullInfo.mentorBase.companyName}}
            </view>
            <view class="user-position textoverflow" wx:if="{{userFullInfo.mentorBase.positionName}}">
              {{userFullInfo.mentorBase.positionName}}
            </view>
          </view>
        </view>
        <view class="user-grade">
          <view class="grade-item" wx:for="{{appointInfo}}" wx:key="{{index}}">
            <view class="grade-header">
              <text class="weight">{{ item.number }}</text>
              <text>{{ item.numberText }}</text>
            </view>
            <view class="grade-footer">{{ item.label }}</view>
          </view>
          <!-- <view class="grade-item">
            <view class="grade-header">
              <text class="weight">1888</text>
              <text>分钟</text>
            </view>
            <view class="grade-footer">辅导时长</view>
          </view>
          <view class="grade-item">
            <view class="grade-header">
              <text class="weight">88</text>
              <text>人次</text>
            </view>
            <view class="grade-footer">待辅导</view>
          </view> -->
        </view>
      </view>
      <view class="page-home-data-box">
        <van-tabs custom-class="page-tabs" color="#0057FF" swipeable="{{true}}" active="{{ active }}" bind:change="tabChange" line-height="4rpx" line-width="68rpx">
          <van-tab title="我的">
            <mine id="myComponent"></mine>
          </van-tab>
          <van-tab title="店铺">
            <shop id="shop"></shop>
          </van-tab>
          <van-tab title="动态" info="{{messageNumber > 0 ? (messageNumber > 99 ? '99+' : messageNumber) : ''}}">
            <view class="dynamics-list">
              <view wx:if="{{dynamicsList.length > 0}}">
                <dynamics id="dynamics" dynamicsList="{{dynamicsList}}" canNavigateToDetail="{{true}}" user-role="teacher" mentorDynamics="{{true}}" />
                <view class="finishedText">
                  <text wx:if="{{loading}}">加载中...</text>
                  <!-- <text wx:elif="{{finished}}">暂无更多</text> -->
                </view>
              </view>

              <van-loading class="loading" type="spinner" vertical color="#0057FF" size="45px" wx:if="{{loading && dynamicsList.length === 0}}"></van-loading>

              <custom-empty-new padding='66rpx' wx:elif="{{!loading && dynamicsList.length === 0}}" describe="暂无动态" />
            </view>
          </van-tab>
        </van-tabs>
      </view>
    </view>
  </scroll-view>
  <view class="home-page-footer" wx:if="{{ active === 0 }}">
    <van-button bind:tap="toPlacardSelect" custom-class="info-btn" type="info">分享我的主页</van-button>
    <van-button bind:tap="toPersonalLetter" custom-class="default-btn" type="default">
      我的私信<text class="message-number" wx:if="{{personalLetterNumber>0}}">{{personalLetterNumber > 99 ? '99+' : personalLetterNumber}}</text>
    </van-button>
  </view>

  <view class="float-buttons" wx:if="{{ active === 1 }}" style="top: {{floatButtonTop}}px; left: {{floatButtonLeft}}px;">
    <view class="float-add">
      <view class="float-button article" wx:if="{{add}}" catchtap="onAddArticle">
        <text>图文</text>
      </view>
      <view class="float-button video" wx:if="{{add}}" catchtap="onAddVideo">
        <text>视频</text>
      </view>
      <view class="float-button main-button" catchtouchmove="onFloatButtonMove" catchtouchstart="onFloatButtonStart" catchtouchend="onFloatButtonEnd" catchtap="onAddColumn">
        <van-icon color='#fff' name="{{!add ? 'plus' : 'minus'}}" />
      </view>

    </view>
  </view>

  <view class="home-page-footer" wx:if="{{ active === 2 }}">
    <van-button bind:tap="toDynamic" custom-class="info-btn" type="info">发布动态</van-button>
    <van-button bind:tap="toDynamicMessage" custom-class="default-btn" type="default">
      动态消息<text class="message-number" wx:if="{{messageNumber>0}}">{{messageNumber > 99 ? '99+' : messageNumber}}</text>
    </van-button>
  </view>
</view>