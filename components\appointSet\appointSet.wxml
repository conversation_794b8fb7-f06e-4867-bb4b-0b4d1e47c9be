<view class="appoint-set">
  <van-popup
  close-on-click-overlay="{{ true }}"
  show="{{ showAppointTimeChangePopup }}"
  position="bottom"
  round
  bind:click-overlay="cancelAppointPopupValue"
  bind:close="cancelAppointPopupValue"
  custom-class="appoint-time-popup-box"
  >
    <view class="appoint-time-popup">
      <view class="close-icon" bind:tap="cancelAppointPopupValue">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/popup-close.png" mode=""/>
      </view>
      <view class="header">
      {{appointDate}} {{appointWeek}} {{((appointTimeList[selectTimeChangeIndex] && appointTimeList[selectTimeChangeIndex].startTime && appointTimeList[selectTimeChangeIndex].startTime.hour) ? appointTimeList[selectTimeChangeIndex].startTime.hour : '')}}:00
      </view>
      <view class="set content" wx:if="{{appointTimeList[selectTimeChangeIndex] && appointTimeList[selectTimeChangeIndex].appointConfigStatus === 1}}">
        <view class="date-set-select">
          <view class="select-item" wx:for="{{selectTimeSetList}}" wx:key="{{index}}" bind:tap="dateSetSelectChange" data-item="{{item}}">
            <view class="text">{{item.label}}</view>
            <view class="icon">
              <image src="{{selectSetId === item.appointConfigType ? 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-active.png' : 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-icon.png'}}"/>
            </view>
          </view>
        </view>
        <view class="date-appoint-type-box" wx:if="{{appointTimeList[selectTimeChangeIndex] && appointTimeList[selectTimeChangeIndex].appointConfigStatus === 1}}">
          <!-- <div class="online-item" wx:if="{{onlineObject.active}}">
            <div class="title">电话咨询</div>
            <div class="subtitle">为保证彼此隐私，电话号码将显示为虚拟号</div>
          </div> -->
          <div class="offline-item" wx:if="{{offlineObject.active}}">
            <div class="title">线下咨询</div>
            <view class="address-list">
              <view class="address-item" wx:for="{{addressList}}" wx:key="{{index}}"bind:tap="addressSetSelectChange" data-item="{{item}}">
                <view class="address">
                  <view class="default" wx:if="{{index === 0}}">默认</view>
                  <view class="text">{{item.address}}</view>
                </view>
                <view class="icon">
                  <image src="{{seleceAddressId === item.id ? 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-active.png' : 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-icon.png'}}"/>
                </view>
              </view>
            </view>
          </div>
        </view>
      </view>
      <view class="unset content" wx:if="{{appointTimeList[selectTimeChangeIndex] && appointTimeList[selectTimeChangeIndex].appointConfigStatus === 3}}">
        <view class="date-set-select">
          <view class="select-item" wx:for="{{selectTimeCancelList}}" wx:key="{{index}}" bind:tap="dateSetSelectChange" data-item="{{item}}" wx:if="{{item.appointConfigType === appointTimeList[selectTimeChangeIndex].appointConfigType}}">
            <view class="text">{{item.label}}</view>
            <view class="icon">
              <image src="{{selectSetId === item.appointConfigType ? 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-active.png' : 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-icon.png'}}"/>
            </view>
          </view>
        </view>
      </view>
      <view class="btn">
        <van-button custom-class="default-btn" type="default" bind:tap="cancelAppointPopupValue">取消</van-button>
        <van-button custom-class="info-btn" type="info" bind:tap="dateConfirm">确定</van-button>
      </view>
    </view>
  </van-popup>

  <van-popup
  close-on-click-overlay="{{ true }}"
  show="{{ showAddAddressPopup }}"
  position="bottom"
  round
  bind:click-overlay="cancelAddressPopupValue"
  bind:close="cancelAddressPopupValue"
  custom-class="set-address-popup"
  >
    <view class="address-popup">
      <view class="close-icon" bind:tap="cancelAddressPopupValue">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/popup-close.png" mode=""/>
      </view>
      <view class="header">
        添加咨询地址
      </view>
      <view class="content">
        <van-field class="input-class" input-align="left" border="{{ false }}" model:value="{{ addAddress }}" maxlength="30" placeholder="请输入咨询地址" />
      </view>
      <view class="btn">
        <van-button custom-class="info-btn" type="info" bind:tap="handleAddAddress">提交</van-button>
      </view>
    </view>
  </van-popup>

  <view class="appoint-set-card no-margin">
    <view class="set-time--title">辅导时间设置</view>
    <view class="set-time-tips">
      <image src="/static/icon/warning-circle.png" mode=""/>
      <!-- <svg-icon name="warning" size="12" color="rgb(233, 178, 99)"></svg-icon> -->
      <text class="set-time-tips-value">请至少设置一个可辅导的时间</text>
    </view>
    <view class="date-select">
      <view class="date-select-header">
        <view class="date-select-header-change-box">
          <view class="date-select-header-left" wx:if="{{offsetDay > 0}}" bind:tap="offersetDayDecrease">
            <!-- <svg-icon name="arrow-left" size="12" color="rgb(51, 51, 51)"></svg-icon> -->
            <image src="/static/icon/arrow-right-gray.png" mode=""/>
          </view>
        </view>
        <text class="date-select-header-content">
          <text>{{appointDate}}</text>
          <text class="date-week">{{appointWeek}}</text>
        </text>
        <view class="date-select-header-change-box">
          <view class="date-select-header-right" wx:if="{{offsetDay < 13}}" bind:tap="offersetDayAugment">
            <!-- <svg-icon name="arrow-right" size="12" color="rgb(51, 51, 51)"></svg-icon> -->
            <image src="/static/icon/arrow-right-gray.png" mode=""/>
          </view>
        </view>

      </view>
      <view class="time-list">
        <view 
        wx:for="{{appointTimeList}}"
        wx:key="{{item.startTime.hour}}"
        bind:tap="appointTimeChange"
        data-item="{{item}}"
        data-index="{{index}}"
        class="time-item [{{item.appointConfigStatus === 3 ? 'time-item-setting' : ''}}, {{item.appointConfigStatus === 2 ? 'time-item-past' : ''}}, {{item.appointConfigStatus === 4 ? 'time-item-appoint': ''}}]"
        >
          {{item.startTime.hour + ':00'}}
          <text class="repetition-week" wx:if="{{item.appointConfigStatus === 3 && item.appointConfigType === 20}}">
            <text class="repetition-week-blue">
              <text>周</text>
            </text>
          </text>
        </view>
        <view class="time-empty"></view>
        <view class="time-empty"></view>
      </view>
      <view class="legend">
        <view class="legend-item">
          <text class="legend-circle past-due"></text>
          <text>已过期</text>
        </view>
        <view class="legend-item">
          <text class="legend-circle setting"></text>
          <text>已设置</text>
        </view>
        <view class="legend-item">
          <text class="legend-circle appointed"></text>
          <text>已预约</text>
        </view>
      </view>
    </view>
  </view>
  <view class="appoint-set-card">
    <view class="set-time--title">辅导方式设置</view>
    <view class="set-time-tips">
      <image src="/static/icon/warning-circle.png" mode=""/>
      <text class="set-time-tips-value">请至少设置一种可辅导的方式</text>
    </view>
    <view class="set-appoint-type">
      <!-- <view class="set-appoint-type-item {{onlineObject.active ? 'set-appoint-type-item-active' : ''}}" bind:tap="appointChange" data-key="onlineObject">
        <view class="item-title">
          <view class="left">
            <image src="{{ onlineObject.active ? onlineObject.iconActive : onlineObject.icon }}" mode=""/>
            <text>电话咨询</text>
          </view>
          <view class="right">
            <image src="{{ onlineObject.active ? onlineObject.selectActiveIcon : onlineObject.selectIcon }}" mode=""/>
          </view>
        </view>
      </view> -->
      
      <view class="set-appoint-type-item {{meetingOnlineObject.active ? 'set-appoint-type-item-active' : ''}}" bind:tap="appointChange" data-key="meetingOnlineObject">
        <view class="item-title">
          <view class="left">
            <image src="{{ meetingOnlineObject.active ? meetingOnlineObject.iconActive : meetingOnlineObject.icon }}" mode=""/>
            <text>线上咨询</text>
          </view>
          <view class="right">
            <image src="{{ meetingOnlineObject.active ? meetingOnlineObject.selectActiveIcon : meetingOnlineObject.selectIcon }}" mode=""/>
          </view>
        </view>
      </view>

      <view class="set-appoint-type-item {{offlineObject.active ? 'set-appoint-type-item-active' : ''}}" bind:tap="appointChange" data-key="offlineObject">
        <view class="item-title">
          <view class="left">
            <image src="{{ offlineObject.active ? offlineObject.iconActive : offlineObject.icon }}" mode=""/>
            <text>线下咨询</text>
          </view>
          <view class="right">
            <image src="{{ offlineObject.active ? offlineObject.selectActiveIcon : offlineObject.selectIcon }}" mode=""/>
          </view>
        </view>
        <view class="appoint-address" wx:if="{{offlineObject.active}}">
          <view class="address-list">
            <view class="address-item" wx:for="{{addressList}}" wx:key="{{index}}">
              <text class="address-item-text">{{item.address}}</text>
              <image src="/static/icon/delete-danger.png" mode="" catch:tap="deleteAddress" data-item="{{item}}"/>
            </view>
          </view>
          <view class="address-add-btn" wx:if="{{addressList.length < 10}}">
            <van-button type="info" catch:tap="showAddAddressPopup">
              <image src="/static/icon/add-simplicity.png" mode=""/>
              <text class="text">添加地址</text>
            </van-button>
          </view>
        </view>
      </view>
    </view>
    <view class="tip">注:同一时间段内只能有一名学生进行咨询</view>
  </view>
</view>
