<view class="personal-letter">

  <view class="student-detail" bind:tap="toStudentDetail">
    <image class="student-img" src="{{ studentInfo.avatarPath }}" mode="aspectFill" />
    <view class="right">
      <view class="student-name">{{ studentInfo.userName }}</view>
      <view class="school">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/school.png" />
        <view class="p">
          <text>{{ studentInfo.school || '-' }}</text>
          <text></text>
          <text>{{ studentInfo.college || '-' }}</text>
        </view>
      </view>
      <view class="major">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/major.png" />
        <view class="p">{{ studentInfo.major || '-' }}</view>
      </view>
    </view>
  </view>
  <!-- <view class="message-content">
    <scroll-view scroll-anchoring="{{true}}" scroll-with-animation="{{ false }}" id="scrollView" class="scroll-view" scroll-into-view="{{toView}}" bindscrolltoupper="scrollToTop" scroll-y>
      <view class="scroll-box" wx:if="{{msgList && msgList.length > 0}}">
        <view class="message-content2">
          <view wx:for="{{msgList}}" wx:key="{{index}}">
            <view id="{{item.postionNodeId}}" wx:if="{{item.postionNodeId}}"></view>
            <view class="left" wx:if="{{!item.isSelf}}">
              <view class="data">{{item.createdAt}}</view>
              <view class="cont">
                <view class="userImg">
                  <image src="{{ item.fromAvatar }}" mode="aspectFill" />
                </view>

                <view class="left-msg" wx:if="{{item.msgType == 10}}">
                  <view class="ask-txt">{{item.content}}</view>
                </view>
                <view class="file-list" wx:if="{{item.showFileType}}">
                  <view class="file-item">
                    <view class="file-item-base">
                      <image :key="item.content" src="{{item.fileTypeIcon}}" mode="aspectFill" />
                      <view class="file-value">
                        <view class="file-name textoverflow">{{item.contentDesc}}</view>
                        <view class="file-date">{{item.fileDate}}</view>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="file-img" wx:if="{{item.showImageType}}">
                  <image src="{{item.content}}" mode="heightFix" />
                </view>
              </view>
            </view>
            <view class="right" wx:else>
              <view class="data">{{item.createdAt}}</view>
              <view class="cont">
                <view class="right-msg">
                  <view class="ask-txt">{{item.content}}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view id="scroll-bottom"></view>
    </scroll-view>
  </view> -->

  <scroll-view scroll-top="{{scrollTop}}" class="chat-container " scroll-y="true" bind:scroll="onScroll" ref="chatContainer">
    <view class="message-list" >
      <view wx:for="{{msgList}}" wx:key="{{index}}">
        <!-- <view id="{{item.postionNodeId}}" wx:if="{{item.postionNodeId}}"></view> -->
        <!-- 学生 -->
        <view class="left" wx:if="{{!item.isSelf}}">
          <view class="data">{{item.createdAt}}</view>
          <view class="cont">
            <view class="userImg">
              <image src="{{ item.fromAvatar }}" mode="aspectFill" />
            </view>

            <view class="left-msg" wx:if="{{item.msgType == 10}}">
            <!-- <view class="left-msg" wx:if="{{ true }}"> -->
              <view class="ask-txt">{{item.content}}</view>
            </view>
            <view class="file-list" bind:tap="msgClick" data-value="{{item}}" wx:if="{{item.showFileType}}">
            <!-- <view class="file-list" wx:if="{{ false }}"> -->
              <view class="file-item">
                <view class="file-item-base">
                  <image :key="item.content" src="{{item.fileTypeIcon}}" mode="aspectFill" />
                  <view class="file-value">
                    <view class="file-name">{{item.contentDesc}}</view>
                    <view class="file-date">{{item.fileDate}}</view>
                  </view>
                </view>
              </view>
            </view>
            <view class="file-img" bind:tap="msgClick" data-value="{{item}}" wx:if="{{item.showImageType}}">
            <!-- <view class="file-img" wx:if="{{ false }}"> -->
              <image src="{{item.content}}" mode="heightFix" bind:tap="" />
            </view>
          </view>
        </view>
        <!-- 老师 -->
        <view class="right" wx:else>
          <view class="data">{{item.createdAt}}</view>
          <view class="cont">
            <view class="right-msg">
              <view class="ask-txt">{{item.content}}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="space" style="height:{{spaceheight+'px'}}"></view>
    </view>
  </scroll-view>

  <view class="message-send">
    <view class="message-field">
      <van-field
        bind:change="messageChange"
        autosize="{{fieldAutoSize}}"
        value="{{ msgValue }}"
        placeholder="请输入内容"
        border="{{ false }}"
        type="textarea"
        maxlength="500"
      />
    </view>
    <image bind:tap="sendMessage" class="send-btn" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/send.png" mode="aspectFill"/>
  </view>
</view>