// pages/mine/user-edit/user-edit.js
import { getUserFullInfo, updateUserAvatar } from '~/api/user'
import { educationLevelFilter } from '~/utils/filter'
import { logout } from '~/utils/loginAbout'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    imgFile: null,
    showCrop:false,

    userInfo: {
      mentorBase: {
        user: {}
      },
      mentorCategoryList: [],
      mentorEducationList: [],
      mentorProfessionalList: [],
      mentorSkillFieldList: [],
      mentorTradeList: []
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    
  },
  toResetPassword() {
    wx.navigateTo({
      url: '/pages/mine/reset-password/phone-check/phone-check',
    })
  },
  handleLoginOut() {
    wx.showModal({
      title: '退出登录',
      content: '确定退出登录？',
      async success (res) {
        if (res.confirm) {
          logout()
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  },
  editEducation(event) {
    if ((event.currentTarget.dataset.id ?? '') === '') {
      wx.navigateTo({
        url: `/pages/mine/education-edit/education-edit`,
      })
    } else {
      wx.navigateTo({
        url: `/pages/mine/education-edit/education-edit?id=${event.currentTarget.dataset.id}`,
      })
    }
  },
  editWork(event) {
    if ((event.currentTarget.dataset.id ?? '') === '') {
      wx.navigateTo({
        url: `/pages/mine/work-edit/work-edit`,
      })
    } else {
      wx.navigateTo({
        url: `/pages/mine/work-edit/work-edit?id=${event.currentTarget.dataset.id}`,
      })
    }
  },
  getHtmlValue(value) {
    const checkOption = value.replace(/\n/g, '<br/>')
    return checkOption
  },
  // 获取所有的用户基本信息
  async getUserFullInfo() {
    const { data } = await getUserFullInfo()
    data.mentorBase.nodeValue = this.getHtmlValue(data.mentorBase.introduction)
    if (data.mentorProfessionalList && data.mentorProfessionalList.length > 0) {
      data.mentorProfessionalList.forEach(item => {
        if (item.endDate === '9999-01-01 00:00:00' || item.endDate === '9999-12-01 00:00:00') {
          item.endDate = '至今'
        } else {
          item.endDate = item.endDate.split('-')[0] + '-' + item.endDate.split('-')[1]
        }
        item.startDate = item.startDate.split('-')[0] + '-' + item.startDate.split('-')[1]
      })
    }
    if (data.mentorEducationList && data.mentorEducationList.length > 0) {
      data.mentorEducationList.forEach(item => {
        item.startDate = item.startDate.split('-')[0] + '-' + item.startDate.split('-')[1]
        item.endDate = item.endDate.split('-')[0] + '-' + item.endDate.split('-')[1]
        item.educationLevelText = educationLevelFilter(item.educationLevel)
      })
    }
    this.setData({
      userInfo: data
    })
    console.log(this.data.userInfo.userId, 'userInfo');
  },
  toPlacardSelect() {
    wx.navigateTo({
      url: `/pages/mine/placard/placard?mentorId=${this.data.userInfo.userId}`,
    })
  },
  editBase() {
    wx.navigateTo({
      url: '/pages/mine/edit-base/edit-base',
    })
  },
  // 处理裁剪完成事件
  handleCropperDone(e) {
    const { url } = e.detail;
    this.uploadAvatar(url)
  },

  // 取消裁剪
  cancelShowCrop() {
    this.setData({
      showCrop: false,
      imgFile: null
    });
  },
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        const file = res.tempFiles[0];
        const fileSize = file.size / 1024 / 1024;

        if (fileSize > 20) {
          wx.showToast({
            title: "图片请控制在20MB以内",
            icon: "none",
          });
          return;
        }
        const imgFile = {
          url: file.tempFilePath,
          width: 320,
          height: 320
        };
        this.setData({
          showCrop: true,
          imgFile: imgFile
        })
      },
      fail: (err) => {
        console.log('图片选择失败', err);
      }
    });
  },
  // removeImage() {
  //   this.setData({
  //     'form.columnCoverUrl': ''
  //   });
  // },
  // 处理裁剪完成事件
  handleCropperDone(e) {
    const {
      url
    } = e.detail;
    this.uploadAvatar(url)
  },
  // 上传头像到服务器
  async uploadAvatar(url) {
    this.setData({
      'userInfo.mentorBase.user.avatarPath': url,
      showCrop: false, // 关闭弹窗
      imgFile: null // 清空临时图片
    });
    updateUserAvatar({
      avatarPath: url
    })
  },
  // 取消裁剪
  cancelShowCrop() {
    this.setData({
      showCrop: false,
      imgFile: null
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getUserFullInfo()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})