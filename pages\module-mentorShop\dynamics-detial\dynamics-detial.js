// pages/module-mentorShop/dynamics-detial/dynamics-detial.js
import {
  getDynamicDetail,
  getDynamicCommentList,
  addDynamicComment,
  replyDynamicComment,
  getDynamicReplyList
} from '~/api/mentorShop'
import { getUserFullInfo } from '~/api/user'
import moment from 'moment'
import eventBus from '~/utils/websocket/eventBus';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    snapshotId: '', // 一次分页周期的唯一标识，获取分页时，不管每条分页数据的更新
    userInfo: {}, // 用户信息
    dynamicsList: [],
    fieldAutoSize: {
      maxHeight: 150
    },
    msgValue: '',
    commentList: [], // 评论列表
    replyTo: '', // 记录回复对象
    showReplyInput: false, // 控制回复输入框显示
    currentCommentId: null, // 当前回复的评论ID
    showInput: false,
    dynamicId: null,
    mentorId: '',
    replyToUserId: null, // 新增回复用户ID
    expandedComments: {}, // 记录每个评论的展开状态
    initialLoaded: {}, // 记录每个评论是否已经初次加载过
    newReplies: {}, // 记录每个评论的新回复，格式: {commentId: [replies]}
    loading: false,
    page: {
      pageSize: 20, // 每页数量
      pageNumber: 1, // 当前页码
    },
    finished: false,
    totalReplies: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      snapshotId: Number(new Date().getTime().toString() + Math.floor(Math.random() * 1000000).toString())
    })
    this.getUserInfo()
    this.setData({
      dynamicId: options.id,
      mentorId: options.mentorId
    })
    this.getDynamicDetail(options.id)
    this.getDynamicCommentList(options.id)
  },

  // 获取用户信息
  getUserInfo() {
    getUserFullInfo().then(res => {
      this.setData({
        userInfo: res.data.mentorBase
      })
    })
  },
  // 获取动态详情
  getDynamicDetail(dynamicId) {
    getDynamicDetail({
      dynamicId: dynamicId
    }).then(res => {
      this.setData({
        dynamicsList: [res.data],
        totalReplies:res.data.commentCount
      })
    })
  },

  // 获取动态评论列表
  async getDynamicCommentList() {
    if (this.data.loading || this.data.finished) return;

    this.setData({
      loading: true,
    });

    try {
      const { data, paging } = await getDynamicCommentList({
        ...this.data.page,
        dynamicId: this.data.dynamicId,
        snapshotId: this.data.snapshotId
      });

      // 判断是否加载完所有数据
      if (data.length < this.data.page.pageSize) {
        this.setData({
          finished: true
        });
      }

      // 避免重复数据
      const newList = this.data.page.pageNumber === 1 ? 
        data : 
        [...this.data.commentList, ...data];
      
      const processedCommentList = this.processCommentList(newList);

      this.setData({
        commentList: processedCommentList,
        loading: false,
        'page.pageNumber': this.data.page.pageNumber + 1
      });
    } catch (error) {
      console.error('获取评论列表失败:', error);
      this.setData({
        loading: false
      });
    }
  },

  // 添加动态评论或回复
  addDynamicComment() {
    if (!this.data.msgValue.trim()) {
      wx.showToast({
        title: '请输入内容',
        icon: 'none'
      });
      return;
    }

    // 根据是否有 currentCommentId 判断是评论还是回复
    const isReply = this.data.currentCommentId != null;
    const currentCommentId = this.data.currentCommentId;

    //参数
    const params = {
      mentorDynamicId: Number(this.data.dynamicId),
      commentContent: this.data.msgValue
    };

    if (isReply) {
      if (!currentCommentId) {
        return;
      }
      // 回复评论需要的额外参数
      params.parentCommentId = currentCommentId;
    }
    // 选择对应的接口和参数
    const request = isReply ? replyDynamicComment : addDynamicComment;

    // 调用接口
    request(params)
      .then((res) => {
        if (isReply) {
          const commentList = [...this.data.commentList];
          // 找到根评论和被回复的评论
          const rootCommentIndex = commentList.findIndex(item => 
            item.id === currentCommentId || // 如果是主评论
            item.replies.some(reply => reply.id === currentCommentId) // 如果是回复中的评论
          );

          if (rootCommentIndex > -1) {
            // 找到被回复的用户信息
            let commentedUser = null;
            if (commentList[rootCommentIndex].id === currentCommentId) {
              // 如果回复的是主评论
              commentedUser = {};
            } else {
              // 如果回复的是回复中的评论
              const replyIndex = commentList[rootCommentIndex].replies.findIndex(reply => reply.id === currentCommentId);
              if (replyIndex > -1) {
                commentedUser = {
                  userName: commentList[rootCommentIndex].replies[replyIndex].commenter.userName
                };
              }
            }

            const newReply = {
              excludeIds: true,
              id: res.data,
              commentContent: params.commentContent,
              commenter: {
                avatarPath: this.data.userInfo.user.avatarPath,
                userName: this.data.userInfo.user.userName
              },
              createTime: moment().format('YYYY-MM-DD HH:mm:ss'),
              commentedUser // 添加被回复者信息
            };

            const isExpanded = this.data.expandedComments[commentList[rootCommentIndex].id];

            if (!isExpanded) {
              // 未展开时，将新回复添加到最后一条回复的后面
              const replies = [...commentList[rootCommentIndex].replies];
              if (replies.length > 0) {
                const firstReply = replies[0];
                const otherReplies = replies.slice(1);
                // 将新回复添加到所有回复的最后
                commentList[rootCommentIndex].replies = [firstReply, ...otherReplies, newReply];
              } else {
                // 如果还没有回复，直接添加
                commentList[rootCommentIndex].replies = [newReply];
              }
            } else {
              // 已展开时，将新回复添加到当前页的最后
              const currentReplies = [...commentList[rootCommentIndex].replies];
              const pageSize = 8;
              const currentPage = Math.ceil(currentReplies.length / pageSize);
              const insertPosition = currentPage * pageSize;

              currentReplies.splice(insertPosition, 0, newReply);
              commentList[rootCommentIndex].replies = currentReplies;
            }

            // 更新回复总数
            commentList[rootCommentIndex].totalReplies = (commentList[rootCommentIndex].totalReplies || 0) + 1;

            this.setData({ commentList });
          }
        } else {
          // 如果是新评论，保持原有逻辑
          const newComment = {
            id: res.data,
            commentContent: params.commentContent,
            commenter: {
              avatarPath: this.data.userInfo.user.avatarPath,
              userName: this.data.userInfo.user.userName
            },
            createTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            replies: [],
            subCommentCount: 0,
            totalReplies: 0
          };

          this.setData({
            commentList: [newComment, ...this.data.commentList]
          });
        }

        // 成功后再清空状态
        this.setData({
          msgValue: '',
          showReplyInput: false,
          replyTo: '',
          currentCommentId: null,
          replyToUserId: null,
          totalReplies: this.data.totalReplies + 1
        });
        
        eventBus.emit('updateDynamicItem', {
          id: Number(this.data.dynamicId),
          commentCount: this.data.totalReplies
        });
        

        // wx.showToast({
        //   title: isReply ? '回复成功' : '评论成功',
        //   icon: 'none'
        // });
      })
      .catch(err => {
        console.error(isReply ? '回复失败' : '评论失败', err);
        wx.showToast({
          title: isReply ? '回复失败' : '评论失败',
          icon: 'none'
        });
      });
  },

  sendMessage() {
    this.addDynamicComment()
  },

  // 处理回复点击
  handleReply(e) {
    const { name, commentid } = e.currentTarget.dataset;

    if (!commentid) {
      return;
    }

    // 检查 commentid 的类型和值
    const parsedCommentId = Number(commentid) || commentid;

    this.setData({
      replyTo: `回复 ${name}：`,
      showReplyInput: true,
      currentCommentId: parsedCommentId
    });
  },

  // 处理输入框获取焦点
  handleFocus() {
    // 保持回复提示
  },

  // 处理输入框失去焦点
  handleBlur(e) {
    if (!e.detail.value) {
      this.setData({
        replyTo: '',
        showReplyInput: false
      });
    }
  },

  // 展开回复
  expandReplies(e) {
    const commentid = Number(e.currentTarget.dataset.commentid);
    const commentList = [...this.data.commentList];
    const commentIndex = commentList.findIndex(item => item.id === commentid);
    if (commentIndex === -1) return;

    const comment = commentList[commentIndex];
    const isExpanded = this.data.expandedComments[commentid];

    // 如果已经展开且已加载全部回复，则收起
    if (isExpanded && comment.replies.length >= comment.totalReplies) {
      this.setData({
        [`expandedComments.${commentid}`]: false,
        [`commentList[${commentIndex}].replies`]: comment.replies.length > 0 ? [comment.replies[0]] : []
      });
      return;
    }

    // 防止重复加载
    if (this.loadingReplies) return;
    this.loadingReplies = true;

    // 固定每页显示8条
    const pageSize = 8;
    const pageNumber = Math.ceil(comment.replies.length / pageSize);

    this.setData({
      [`expandedComments.${commentid}`]: true,
      [`initialLoaded.${commentid}`]: true
    });

    // 获取已有回复的ID列表，用于排除重复加载
    const existingReplyIds = comment.replies.filter((reply, index) => reply.excludeIds || index === 0)
      // .filter(id => id !== undefined && id !== null);
    console.log(existingReplyIds, 'existingReplyIdsexistingReplyIds');
    const excludeIds = existingReplyIds.map(item => item.id).join(',')
    console.log(excludeIds, 'excludeIds');
    // 构建排除ID参数
    // const excludeIds = existingReplyIds.length > 0 ? existingReplyIds.join(',') : ''
    getDynamicReplyList({
      rootCommentId: commentid,
      pageSize,
      pageNumber,
      excludeIds
    }).then(res => {
      this.loadingReplies = false;
      const newReplies = res.data || [];
      const total = res.paging?.total ?? comment.totalReplies ?? 0;

      if (commentIndex > -1) {
        // 过滤掉已存在的回复，避免重复
        const filteredNewReplies = newReplies.filter(newReply => 
          !existingReplyIds.includes(newReply.id)
        );
        
        // console.log('新获取回复数:', newReplies.length, '过滤后新回复数:', filteredNewReplies.length);
        
        // 合并回复列表，确保不重复
        commentList[commentIndex].replies = [...comment.replies, ...filteredNewReplies];
        commentList[commentIndex].totalReplies = total;

        this.setData({
          commentList,
          [`expandedComments.${commentid}`]: true
        });
      }
    }).catch(() => {
      this.loadingReplies = false;
    });
  },

  // 在数据加载时处理
  processCommentList(commentList) {
    return commentList.map(comment => {
      let replies = [];
      if (comment.subComments) {
        replies = Array.isArray(comment.subComments)
          ? comment.subComments
          : [comment.subComments];
      }

      return {
        ...comment,
        replies,
      };
    });
  },
  messageChange(event) {
    this.setData({
      msgValue: event.detail
    })
  },
  // 处理页面点击事件
  handlePageTap(e) {
    // 如果点击的是回复按钮或输入框，不处理
    if (e.target.dataset.type === 'reply' || e.target.dataset.type === 'input') {
      return;
    }

    // 如果输入框显示且没有正在输入，则隐藏输入框
    if (this.data.showReplyInput) {
      this.setData({
        showReplyInput: false,
        replyTo: '',
        msgValue: ''
      });
    }
  },

  // 阻止输入框点击事件冒泡
  handleInputTap(e) {
    // e.stopPropagation();
  },

  // 处理显示评论输入框
  handleShowCommentInput(e) {
    const { dynamicId } = e.detail;
    this.setData({
      showReplyInput: true,
      replyTo: '', // 清空回复对象，因为是新评论
      currentCommentId: null, // 清空当前评论ID
      replyToUserId: null // 清空回复用户ID
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onScrollToLower() {
    if (!this.data.finished) {
      this.getDynamicCommentList();
    }
  },
})