// pages/module-aiMentor/components/ScrollBox/ScrollBox.js
const app = getApp()

Component({
  options: {
    multipleSlots: true  // 启用多slot支持
  },
  properties: {
    // 是否禁止底部的touchmove事件
    bottomTouchDisabled: {
      type: <PERSON>olean,
      value: true
    },
    focusHideBottom: {
      type: <PERSON>olean,
      value: true
    },
    backgroundColor: {
      type: String,
      value: '#fff'
    },
    contentSlot: {
      type: Boolean,
      value: false
    }
  },
  data: {
    // hideBottom: false,
    // bottomBoxHeight: '',
    // equipment: ''
  },
  // observers: {
  //   'hideBottom': function(newValue) {
  //     if (this.data.focusHideBottom) {
  //       // 获取页面实例
  //       const pages = getCurrentPages()
  //       const currentPage = pages[pages.length - 1]
        
  //       if (newValue) {
  //         // 隐藏底部时移除安全区域样式
  //         currentPage.setData({
  //           safeAreaClass: ''
  //         })
  //       } else {
  //         // 显示底部时添加安全区域样式
  //         currentPage.setData({
  //           safeAreaClass: 'safe-area'
  //         })
  //       }
  //     }
  //   }
  // },
  // lifetimes: {
  //   attached() {
  //     this.setData({
  //       equipment: this.getEquipment()
  //     })
  //     // 延迟执行 initPadding，确保组件完全渲染
  //     wx.nextTick(() => {
  //       this.initPadding()
  //     })
  //   },
  //   detached() {
  //     // 清理工作
  //   }
  // },
  // methods: {
  //   getEquipment() {
  //     // 设备校准
  //     return wx.getSystemInfoSync().platform === 'ios' ? 'IOS' : 'Android';
  //   },
  //   initPadding() {
  //     const query = this.createSelectorQuery()
  //     query.select('.scroll-bottom').boundingClientRect(res => {
  //       if (res) {
  //         this.setData({
  //           bottomBoxHeight: res.height
  //         })
  //       }
  //     }).exec()
  //   },
  //   touchDisabled(event) {
  //     // 阻止事件冒泡和默认行为
  //     if (event) {
  //       event.stopPropagation()
  //       event.preventDefault()
  //     }
  //   },
  //   handleTouchStart() {
  //     // 使用小程序的方式来处理输入框失焦
  //     wx.hideKeyboard()
  //   },
  //   bindTouchStart() {
  //     // 简化触摸事件绑定
  //     this.handleTouchStart()
  //   },
  //   unbindTouchStart() {
  //     // 清理工作（如果需要）
  //   }
  // }
})
