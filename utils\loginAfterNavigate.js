import { getUserFullInfo, getTouristAuthenticationInfo } from '~/api/user'
import { logout } from '~/utils/loginAbout'
export const loginAfterNavigate = (type) => {
  if (type === 'register') {
    logout('/pages/register/register')
  } else {
    if (wx.getStorageSync('token') && wx.getStorageSync('identity')) {
      if (wx.getStorageSync('identity') === 'mentor') {
        getUserFullInfo().then((res) => {
          // 10：已填写手机号，20：已填写完基本信息，30：已填写话题信息，40：已填写信息时间，这里30跟40就直接到首页
          // hideBack的参数是啥值无所谓，只要有值就行
          if (res.data.mentorBase.processStatus === 10) {
            wx.reLaunch({
              url: '/pages/mentor-in/base-info/base-info?hideBack=1',
            })
          }
          if (res.data.mentorBase.processStatus === 20 && res.data.mentorEducationList && res.data.mentorEducationList.length === 0) {
            wx.reLaunch({
              url: '/pages/mentor-in/education/education?hideBack=1',
            })
          } else if (res.data.mentorBase.processStatus === 20 && res.data.mentorProfessionalList && res.data.mentorProfessionalList.length === 0) {
            wx.reLaunch({
              url: '/pages/mentor-in/work/work?hideBack=1',
            })
          } else if (res.data.mentorBase.processStatus === 20 && res.data.mentorEducationList.length > 0 && res.data.mentorProfessionalList.length > 0) {
            wx.reLaunch({
              url: '/pages/mentor-in/topic/topic?hideBack=1',
            })
          }
          if (res.data.mentorBase.processStatus >= 30) {
            wx.reLaunch({
              url: '/pages/module-home/home/<USER>',
            })
          }
        })
      }
      if (wx.getStorageSync('identity') === 'tourist') {
        // 走判断逻辑，如果是处于未提交审核状态，那么就去提交审核，如果处于提交审核状态，就走审核结果页
        getTouristAuthenticationInfo().then(res => {
          if (!res.data) {
            wx.reLaunch({
              url: '/pages/mentor-apply/mentor-apply-process/base-info/base-info',
            })
          } else {
            wx.reLaunch({
              url: '/pages/mentor-apply/menrot-apply-result/menrot-apply-result',
            })
          }
        })
      }
    } else {
      logout('/pages/mentor-apply/mentor-apply-welcome/mentor-apply-welcome')
      // wx.reLaunch({
      //   url: '/pages/login/login-select/login-select',
      // })
    }
  }
}