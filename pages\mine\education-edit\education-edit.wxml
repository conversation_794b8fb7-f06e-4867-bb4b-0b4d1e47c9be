<view class="exp-edit">
  <van-popup
  show="{{ educationLevelPopup }}"
  position="bottom"
  bind:close="popupClose"
  >
  <van-picker bind:cancel="popupClose" bind:confirm="educationLevelConfirm" show-toolbar columns="{{ educationListLevel }}" />
  </van-popup>

  <van-popup
  show="{{ startDatePopup }}"
  position="bottom"
  bind:close="popupClose"
  >
    <van-picker bind:change="startDateChange" id="startPicker" bind:cancel="popupClose" bind:confirm="startConfirm" show-toolbar columns="{{ startColums }}" />
  </van-popup>

  <van-popup
  show="{{ endDatePopup }}"
  position="bottom"
  bind:close="popupClose"
  >
  <van-picker bind:change="endDateChange" id="endPicker" bind:cancel="popupClose" bind:confirm="endConfirm" show-toolbar columns="{{ endColums }}" />
  </van-popup>
  <view class="exp-content">
    <view class="experience-edit">
      <view class="experience-edit-box">
        <van-field input-align="right" border="{{ false }}" bind:change="onChange" data-key="universityName" model:value="{{ educationObject.universityName }}" maxlength="30" label="学校" placeholder="请填写学校名称" />

      <van-field input-align="right" border="{{ false }}" value="{{ educationObject.levelName }}" readonly label="学历" placeholder="请选择学历" is-link bind:tap="showEducationLevelPopup"/>

      <van-field input-align="right" border="{{ false }}" bind:change="onChange" data-key="majorName" model:value="{{ educationObject.majorName }}" maxlength="30" label="专业" placeholder="请填写专业名称" />

      <van-field input-align="right" border="{{ false }}" value="{{ educationObject.startDate }}" readonly label="入学时间" placeholder="请选择入学时间" is-link bind:tap="showStartPopup"/>

      <van-field input-align="right" border="{{ false }}" value="{{ educationObject.endDate }}" readonly label="毕业时间" placeholder="请选择毕业时间" is-link bind:tap="showEndPopup"/>

      </view>
    </view>
    <copyright></copyright>
  </view>
  <view class="exp-footer">
    <van-button wx:if="{{id}}" bind:tap="expDelete" custom-class="default-btn" type="default">删除</van-button>
    <van-button wx:else bind:tap="expBack" custom-class="default-btn" type="default">取消</van-button>
    <van-button bind:tap="expEdit" custom-class="info-btn" type="info">保存</van-button>
  </view>
</view>