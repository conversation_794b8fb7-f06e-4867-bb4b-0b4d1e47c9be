// pages/appoint/mentor-appoint/components/dynamics/dynamics.js
import moment from 'moment'
import {
  addDynamicVoicePlayCount,
  addDynamicAudioVoicePlayCount,
  updateCourseCommentStatus,
  likeCourseComment,
  thumbUpDynamic,
  cancelThumbUpDynamic,
  deleteDynamic
} from '~/api/mentorShop'
import {
  navigateAndAddPage,
  navigateBack
} from '~/utils/pageNavigation';
import eventBus from '~/utils/websocket/eventBus';
Component({
  properties: {
    limit: {
      type: Number,
      value: 0 // 0 表示不限制
    },
    userRole: {
      type: String,
      value: '', // 老师/学生评论
      observer: function (newVal, oldVal) {
        this.setData({
          isTeacher: newVal === 'teacher',
          isStudent: newVal === 'student'
        });
      }
    },
    isCourseComment: {
      type: Boolean,
      value: false // 是否为课程详情评论区
    },
    isExpand: {
      type: Boolean,
      value: false // 文本默认不展开
    },
    showFollowStatus: {
      type: Boolean,
      value: false // 导师默认不展示关注状态
    },
    canNavigateToDetail: {
      type: Boolean,
      value: false // 默认可以跳转到详情页
    },
    isDelete: {
      type: Boolean,
      value: false // 展示删除按钮
    },
    padding: {
      type: String,
      value: '24rpx 36rpx 0' // 默认内边距为0
    },
    courseId: {
      type: String,
      value: '', // 课程id
    },
    mentorDynamics: {
      type: Boolean,
      value: false // 是否为导师动态
    },
    dynamicsList: {
      type: Array,
      value: [],
      observer: function (newVal) {
        if (newVal && newVal.length > 0) {
          // 处理数据限制
          const limitedList = this.properties.limit > 0 ? newVal.slice(0, this.properties.limit) : newVal;
          // 直接处理新数据
          this.setDynamicsData(limitedList);

          // 如果需要展开，初始化展开状态
          if (this.properties.isExpand) {
            this.initExpandStatus();
          }
        }
      }
    },
    totalFlag:{
      type: Boolean,
      value: false // 是否使用传递的评论数量
    },
    totalReplies:{
      type: Number,
      value: 0 // 传递的评论数量
    },
  },

  /**
   * 页面的初始数据
   */
  data: {
    limitedDynamicsList: [], // 添加新的数据属性
    isTeacher: false,
    isStudent: false,

    audioPlayObject: {
      audioPlayIndex: null, // 当前播放的音频索引
      isPlay: false, // 播放状态
    },
    expandMap: {}, // 用于存储每条内容的展开状态
    needExpand: {}, // 存储每条内容是否需要展开按钮
    currentAudio: null, // 当前音频实例
    currentPlayingVideo: null, // 当前播放的视频索引
    showPreviewVideo: false,
    isFullscreen: false
  },
  lifetimes: {
    attached() {
      // 组件初始化时进行身份判断
      const {
        userRole
      } = this.properties;
      this.setData({
        isTeacher: userRole === 'teacher',
        isStudent: userRole === 'student'
      });

      // 监听路由变化
      this.routeChangeListener = () => {
        this.stopAudioAndReset();
      };
      
      // 使用 wx.onAppRoute 监听路由变化
      wx.onAppRoute(this.routeChangeListener);
    },
    detached() {
      // 停止并销毁音频
      if (this.data.currentAudio) {
        this.data.currentAudio.stop();
        this.data.currentAudio.destroy();
        this.stopAudioAndReset();
      }
      
      // 移除路由监听
      // 微信小程序没有直接的 offAppRoute 方法
      // 但组件卸载时，监听器会自动被移除，所以这里不需要额外操作
    }
  },

  methods: {
    // 初始化展开状态
    initExpandStatus() {
      const needExpand = {};
      const expandMap = {};
      this.data.limitedDynamicsList.forEach((item, index) => {
        needExpand[index] = item.content && item.content.length > 100 || item.dynamicContent && item.dynamicContent.length > 100;
        expandMap[index] = false;
      });

      this.setData({
        needExpand,
        expandMap
      });
    },

    // 更新数据时调用
    setDynamicsData(list) {
      const processedList = this.processData(list);
      this.setData({
        limitedDynamicsList: processedList
      });
    },

    // 处理动态数据
    processData(dynamicsList) {
      return dynamicsList.map(item => {
        // 处理时间格式 - 只保留年月日
        if(item.createdAt) {
          item.createdAt = moment(item.createdAt).format('YYYY-MM-DD');
        }
        // 处理音频信息
        if (item.dynamicAudio) {
          if (!item.audioInfo) {
            item.audioInfo = {
              audioProgress: 0,
              audioSrc: item.dynamicAudio
            };
          } else if (typeof item.audioInfo === 'object' && !item.audioInfo.hasOwnProperty('audioProgress')) {
            item.audioInfo.audioProgress = 0;
          }
          if (item.audioDuration) {
            item.formattedDuration = moment.utc(item.audioDuration * 1000).format('mm:ss');
          }
        }

        // 处理图片数组
        if (typeof item.dynamicImages === 'string') {
          try {
            item.parsedImages = JSON.parse(item.dynamicImages);
          } catch (e) {
            item.parsedImages = [];
          }
        } else {
          item.parsedImages = item.dynamicImages || [];
        }

        return item;
      });
    },

    // 预览图片
    previewImage(e) {
      const {
        urls,
        current
      } = e.currentTarget.dataset;
      wx.previewImage({
        urls: urls,
        current: current // 当前显示图片的链接
      });
    },

    // 查看全部评价
    reviews() {
      navigateAndAddPage('/pages/module-mentorShop/all-reviews/all-reviews', {
        courseId: this.properties.courseId
      })
      // wx.navigateTo({
      //   url: `/pages/module-mentorShop/all-reviews/all-reviews?courseId=${this.properties.courseId}`
      // });
    },

    // 点击播放/暂停按钮
    recordClick(e) {
      const index = e.currentTarget.dataset.index;
      const audioSrc = this.data.limitedDynamicsList[index].audioInfo.audioSrc;
      const dynamicId = this.data.limitedDynamicsList[index].id;

      // 如果点击的是当前正在播放的音频
      if (this.data.audioPlayObject.audioPlayIndex === index) {
        if (!this.data.audioPlayObject.isPlay) {
          // 如果是播放完成后的重新播放，需要重置进度条和音频时间
          if (this.data.limitedDynamicsList[index].audioInfo.audioProgress >= 100) {
            this.data.currentAudio.seek(0);
            this.setData({
              [`limitedDynamicsList[${index}].audioInfo.audioProgress`]: 0
            });
          }
          // 继续播放当前音频
          this.data.currentAudio.play();
          this.setData({
            'audioPlayObject.isPlay': true
          });
          // 触发事件通知父组件暂停视频
          this.triggerEvent('audioPlay', {
            duration: this.data.limitedDynamicsList[index].audioInfo.duration
          });
        } else {
          // 暂停当前音频
          this.data.currentAudio.pause();
          this.setData({
            'audioPlayObject.isPlay': false
          });
          // 触发音频暂停事件
          this.triggerEvent('audioPause');
        }
        return;
      }

      // 触发事件通知父组件暂停视频
      this.triggerEvent('audioPlay', {
        duration: this.data.limitedDynamicsList[index].audioInfo.duration
      });

      // 如果之前有其他音频在播放，先停止它
      if (this.data.currentAudio) {
        this.data.currentAudio.stop();
        this.data.currentAudio.destroy();
        this.setData({
          'audioPlayObject.isPlay': false,
          'audioPlayObject.audioPlayIndex': null
        });
      }

      // 重置所有音频的进度条
      const resetList = this.data.limitedDynamicsList.map((item, idx) => {
        if (idx !== index && item.audioInfo) {
          item.audioInfo.audioProgress = 0;
        }
        return item;
      });

      // 创建新的音频实例
      const audioContext = wx.createInnerAudioContext();
      audioContext.src = audioSrc;

      // 添加错误处理
      audioContext.onError((err) => {
        console.error('音频播放错误:', err);
        // wx.showToast({
        //   title: '请重试',
        //   icon: 'none'
        // });
        this.setData({
          'audioPlayObject.isPlay': false,
          'audioPlayObject.audioPlayIndex': null
        });
      });

      // 监听播放事件
      audioContext.onPlay(() => {
        this.setData({
          'audioPlayObject.isPlay': true,
          'audioPlayObject.audioPlayIndex': index,
          limitedDynamicsList: resetList
        });
      });

      // 监听暂停事件
      audioContext.onPause(() => {
        this.setData({
          'audioPlayObject.isPlay': false
        });
        // 触发音频暂停事件
        this.triggerEvent('audioPause');
      });

      // 监听结束事件
      audioContext.onEnded(() => {
        // 音频播放完成时调用计数接口
        // mentorDynamics:导师自己发布的动态
        let request = this.properties.mentorDynamics ? addDynamicAudioVoicePlayCount : addDynamicVoicePlayCount
        const params = {};
        if (this.properties.mentorDynamics) {
          params.dynamicId = dynamicId;
        } else {
          params.courseReviewsId = dynamicId;
        }

        request(params).then(() => {
          const currentCount = this.data.limitedDynamicsList[index].audioPlayCount || 0;
          this.setData({
            [`limitedDynamicsList[${index}].audioPlayCount`]: currentCount + 1
          });
          if(this.properties.mentorDynamics) {
            eventBus.emit('updateDynamicItem', {
              id:this.data.limitedDynamicsList[index].id,
              audioPlayCount: currentCount + 1
            });
          }
        }).catch(() => {});

        this.setData({
          [`limitedDynamicsList[${index}].audioInfo.audioProgress`]: 0,
          'audioPlayObject.audioPlayIndex': null,
          'audioPlayObject.isPlay': false
        });
        // 触发音频结束事件
        this.triggerEvent('audioEnd');
      });

      // 监听播放进度
      audioContext.onTimeUpdate(() => {
        const progress = (audioContext.currentTime / audioContext.duration) * 100;
        this.setData({
          [`limitedDynamicsList[${index}].audioInfo.audioProgress`]: progress
        });
      });

      // 开始播放
      audioContext.play();
      this.setData({
        currentAudio: audioContext
      });
    },

    // 进度条改变
    audioChange(e) {
      const value = e.detail;
      const index = e.currentTarget.dataset.index;

      if (!this.data.currentAudio || this.data.audioPlayObject.audioPlayIndex !== index) return;

      const duration = this.data.currentAudio.duration;
      const currentTime = (value / 100) * duration;

      // 先暂停音频
      this.data.currentAudio.pause();

      // 设置新的播放位置
      this.data.currentAudio.seek(currentTime);

      // 重新开始播放
      this.data.currentAudio.play();

      // 强制更新一次进度
      this.setData({
        [`limitedDynamicsList[${index}].audioInfo.audioProgress`]: value,
        'audioPlayObject.isPlay': true
      });

      // 确保 timeupdate 事件能正确触发
      this.data.currentAudio.onTimeUpdate(() => {
        if (!this.data.currentAudio.duration) return;
        const progress = (this.data.currentAudio.currentTime / this.data.currentAudio.duration) * 100;
        this.setData({
          [`limitedDynamicsList[${index}].audioInfo.audioProgress`]: progress
        });
      });
      // // 监听路由变化
      // wx.onAppRoute(() => {
      //   this.stopAudioAndReset();
      // });
    },
    // detached() {
    //   this.stopAudioAndReset();
    //   // 移除路由监听
    //   wx.offAppRoute();
    // },

    // 处理展开/收起点击
    handleExpand(e) {
      const index = e.currentTarget.dataset.index;
      const key = `expandMap.${index}`;
      this.setData({
        [key]: !this.data.expandMap[index]
      });
    },

    // 添加暂停音频的公共方法
    stopAudioAndReset() {
      if (this.data.currentAudio) {
        this.data.currentAudio.pause();
        this.setData({
          'audioPlayObject.isPlay': false,
          'audioPlayObject.audioPlayIndex': null
        });

        // 重置所有音频的进度条
        const resetList = this.data.limitedDynamicsList.map(item => {
          if (item.audioInfo) {
            item.audioInfo.audioProgress = 0;
          }
          return item;
        });

        this.setData({
          limitedDynamicsList: resetList
        });
      }
    },
    stopPropagation() {
      // 空函数，只用于阻止事件冒泡
      // 不需要做任何事情
      return;
    },
    // 修改处理动态点击的方法
    handleDynamicsItem(e) {
      if (this.properties.canNavigateToDetail) {
        const id = e.currentTarget.dataset.id;
        this.stopAudioAndReset();
        navigateAndAddPage('/pages/module-mentorShop/dynamics-detial/dynamics-detial', {
          id
        })
        // wx.navigateTo({
        //   url: `/pages/module-mentorShop/dynamics-detial/dynamics-detial?id=${id}`
        // });
      }
    },

    // 处理评论点击
    handleComment(e) {
      const dynamicId = e.currentTarget.dataset.id;

      // 获取当前页面栈
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      this.stopAudioAndReset();

      // 判断当前页面类型并进行相应处理
      if (currentPage.route.includes('dynamics-detial')) {
        // 如果已经在详情页，直接触发评论事件
        this.triggerEvent('showCommentInput', {
          dynamicId
        });
      } else {
        // 如果不在详情页，跳转到详情页
        navigateAndAddPage("/pages/module-mentorShop/dynamics-detial/dynamics-detial", {
          id: dynamicId,
          mentorId: this.properties.mentorId
        })
        // wx.navigateTo({
        //   url: `/pages/module-mentorShop/dynamics-detial/dynamics-detial?id=${dynamicId}&mentorId=${this.properties.mentorId}`
        // });
      }
    },

    // 处理点赞操作
    handleThumbUp(e) {
      const index = e.currentTarget.dataset.index;
      const currentDynamic = this.data.limitedDynamicsList[index];
      const isThumbUp = !currentDynamic.isThumbUp;
      const newThumbUpCount = currentDynamic.thumbUpCount + (isThumbUp ? 1 : -1);
      // 更新数据
      this.setData({
        [`limitedDynamicsList[${index}].isThumbUp`]: isThumbUp,
        [`limitedDynamicsList[${index}].thumbUpCount`]: newThumbUpCount
      });
      if(this.properties.mentorDynamics) {
      eventBus.emit('updateDynamicItem', {
        id:currentDynamic.id,
        thumbUpCount: newThumbUpCount,
        isThumbUp: isThumbUp
      });}
      

      // 根据mentorDynamics选择正确的接口和参数
      if (this.properties.mentorDynamics) {
        // 导师动态的点赞处理
        const params = {
          dynamicId: currentDynamic.id
        };
        let request = isThumbUp ? thumbUpDynamic : cancelThumbUpDynamic;
        request(params).catch(() => {
          // 失败时回滚数据
          this.setData({
            [`limitedDynamicsList[${index}].isThumbUp`]: !isThumbUp,
            [`limitedDynamicsList[${index}].thumbUpCount`]: currentDynamic.thumbUpCount
          });
          if(this.properties.mentorDynamics) {
          eventBus.emit('updateDynamicItem', {
            id: this.properties.dynamicsList.id,
            thumbUpCount: newThumbUpCount,
            isThumbUp: isThumbUp
          });}
        });
      } else {
        // 课程评论的点赞处理
        likeCourseComment({
          business_type: 24,
          business_id: currentDynamic.id,
          operation: isThumbUp ? 1 : 0
        }).catch(() => {
          // 失败时回滚数据
          this.setData({
            [`limitedDynamicsList[${index}].isThumbUp`]: !isThumbUp,
            [`limitedDynamicsList[${index}].thumbUpCount`]: currentDynamic.thumbUpCount
          });
        });
      }
    },

    // 添加一个方法用于外部暂停音频
    pauseAudio() {
      if (this.data.currentAudio && this.data.audioPlayObject.isPlay) {
        this.data.currentAudio.pause();
        this.setData({
          'audioPlayObject.isPlay': false
        });
        // 触发音频暂停事件
        this.triggerEvent('audioPause');
      }
    },

    // 显示/隐藏评价
    handleCommentOperation(e) {
      const item = e.currentTarget.dataset.item;
      const index = e.currentTarget.dataset.index;
      const courseReviewsId = item.id;
      const showStatus = item.showStatus === 0 ? 1 : 0;

      this.setData({
        [`limitedDynamicsList[${index}].showStatus`]: showStatus
      });
      // 更新评价显示状态
      updateCourseCommentStatus({
        courseReviewsId: courseReviewsId,
        showStatus: showStatus
      }).then(() => {

      }).catch(() => {
        this.setData({
          [`limitedDynamicsList[${index}].showStatus`]: item.showStatus
        });
      });
    },


    // 处理课程点击
    handleVideoClick(e) {
      const course = e.currentTarget.dataset.course;
      const type = course.courseType === 30 ? 'article' : 'video';
      navigateAndAddPage('/pages/module-mentorShop/course-detail/course-detail', {
        id: course.id,
        type,
        isEdit: 1
      })
      // wx.navigateTo({
      //   url: `/pages/module-mentorShop/course-detail/course-detail?id=${course.id}&type=${type}&isEdit=1`
      // });
    },

    // 删除动态
    handleDelete(e) {
      const dynamicId = e.currentTarget.dataset.id;
      wx.showModal({
        title: '删除',
        content: '确定删除该动态吗？',
        success: (res) => {
          if (res.confirm) {
            deleteDynamic({
              dynamicId: dynamicId
            }).then(() => {
              wx.setStorageSync('dynamicId', dynamicId);
              wx.showToast({
                title: '删除成功',
                icon: 'none'
              });

              navigateBack();
            }).catch(() => {
              console.log('删除动态失败');
            });
          }
        }
      });
    },
    handleVideoPlay(e) {
      const index = e.currentTarget.dataset.index;
      
      // 停止所有正在播放的音频
      if (this.data.audioPlayObject && this.data.audioPlayObject.isPlay) {
        this.stopCurrentAudio();
      }
      
      // 停止其他正在播放的视频
      const videoContexts = this.selectAllComponents('video');
      videoContexts.forEach(videoCtx => {
        if (videoCtx.dataset.index !== index) {
          videoCtx.pause();
        }
      });
      
      // 更新当前播放的视频状态
      this.setData({
        currentPlayingVideo: index
      });
    },
  
    stopCurrentAudio() {
      // 停止音频播放的逻辑
      if (this.data.audioPlayObject.audioContext) {
        this.data.audioPlayObject.audioContext.stop();
      }
      this.setData({
        'audioPlayObject.isPlay': false,
        'audioPlayObject.audioPlayIndex': null
      });
    },
    // 审核失败的原因
    promptReason(){
      wx.showModal({
        title: '内容违规',
        content: '图文涉及违法违规/血腥暴力/色情低俗/封建迷信/敏感话题内容',
        showCancel: false,
        confirmText: '确定',
        success: (res) => {
        }
      });
    },
    // 点击播放按钮 
    previewVideo(e) {
    },
  
    // 关闭预览 
    closePreview() {
    }
  }
})