{"pages": ["pages/page-before/page-before", "pages/mentor-apply/menrot-apply-result/menrot-apply-result", "pages/mentor-apply/mentor-apply-process/materials/materials", "pages/mentor-apply/mentor-apply-process/base-info/base-info", "pages/register/register", "pages/login/login-phone/login-phone", "pages/personal-letter/personal-letter-test/personal-letter-test", "pages/module-home/appoint/appoint-list/appoint-list", "pages/mine/topic-list/topic-list", "pages/login/login-select/login-select", "pages/login/reset-password/reset-password", "pages/login/login-account/login-account", "pages/module-home/home/<USER>", "pages/mentor-in/base-info/base-info", "pages/login/user-agreement/user-agreement", "pages/login/privacy-policy/privacy-policy", "pages/mentor-in/education/education", "pages/mentor-in/work/work", "pages/mentor-in/topic/topic", "pages/mentor-in/appoint-set/appoint-set", "pages/mine/education-edit/education-edit", "pages/mine/work-edit/work-edit", "pages/mine/topic-edit/topic-edit", "pages/mentor-in/welcome/welcome", "pages/mine/user-edit/user-edit", "pages/mine/edit-base/edit-base", "pages/mine/placard/placard", "pages/personal-letter/personal-letter-list/personal-letter-list", "pages/personal-letter/personal-letter-page/personal-letter-page", "pages/personal-letter/student-detail/student-detail", "pages/module-home/appoint/appoint-detail/appoint-detail", "pages/module-home/appoint/appoint-dateset/appoint-dateset", "pages/mine/comment-list/comment-list", "pages/mine/reset-password/phone-check/phone-check", "pages/mine/reset-password/reset/rest", "pages/module-home/appoint/appoint-report-edit/appoint-report-edit", "pages/module-home/appoint/appoint-report-echo/appoint-report-echo", "pages/module-home/appoint/online-meeting/online-meeting", "pages/template/end-meeting/end-meeting", "pages/mentor-apply/mentor-apply-welcome/mentor-apply-welcome", "pages/mentor-apply/mentor-apply-introduction/mentor-apply-introduction", "pages/mentor-apply/mentor-apply-process/education/education", "pages/mentor-apply/mentor-apply-process/work/work", "pages/mentor-apply/mentor-apply-register/mentor-apply-register"], "subpackages": [{"root": "pages/module-mentorShop", "pages": ["all-column/all-column", "column-detail/column-detail", "add-course/add-course", "components/course/course", "add-column/add-column", "edit-detail/edit-detail", "add-article/add-article", "add-video/add-video", "course-detail/course-detail", "article-template/article-template", "study-progress/study-progress", "all-reviews/all-reviews", "dynamics-detial/dynamics-detial", "message-detial/message-detial", "dynamics-release/dynamics-release"]}], "usingComponents": {"appointStatusIcon": "/components/appointStatusIcon/appointStatusIcon", "appointSet": "/components/appointSet/appointSet", "scroll-box": "/components/ScrollBox/ScrollBox", "mentorInBox": "/components/mentorInBox/mentorInBox", "mentorInBoxApply": "/components/mentorInBoxApply/mentorInBoxApply", "custom-empty-new": "/components/CustomEmptyNew/CustomEmptyNew", "Uploader": "/components/uploader/uploader", "mentor-identity": "/components/mentorIdentity/mentorIdentity", "van-icon": "/miniprogram_npm/@vant/weapp/icon/index", "van-tab": "/miniprogram_npm/@vant/weapp/tab/index", "van-tabs": "/miniprogram_npm/@vant/weapp/tabs/index", "van-loading": "/miniprogram_npm/@vant/weapp/loading/index", "van-action-sheet": "/miniprogram_npm/@vant/weapp/action-sheet/index", "van-datetime-picker": "/miniprogram_npm/@vant/weapp/datetime-picker/index", "van-switch": "/miniprogram_npm/@vant/weapp/switch/index", "van-cell": "/miniprogram_npm/@vant/weapp/cell/index", "van-cell-group": "/miniprogram_npm/@vant/weapp/cell-group/index", "van-image": "/miniprogram_npm/@vant/weapp/image/index", "van-area": "/miniprogram_npm/@vant/weapp/area/index", "van-picker": "/miniprogram_npm/@vant/weapp/picker/index", "van-slider": "/miniprogram_npm/@vant/weapp/slider/index", "van-circle": "/miniprogram_npm/@vant/weapp/circle/index", "van-collapse": "/miniprogram_npm/@vant/weapp/collapse/index", "van-collapse-item": "/miniprogram_npm/@vant/weapp/collapse-item/index", "copyright": "/components/copyright/copyright", "empty": "/components/empty/empty", "van-button": "/miniprogram_npm/@vant/weapp/button/index", "van-search": "/miniprogram_npm/@vant/weapp/search/index", "van-rate": "/miniprogram_npm/@vant/weapp/rate/index", "van-popup": "/miniprogram_npm/@vant/weapp/popup/index", "van-field": "/miniprogram_npm/@vant/weapp/field/index", "van-uploader": "/miniprogram_npm/@vant/weapp/uploader/index", "van-index-bar": "/miniprogram_npm/@vant/weapp/index-bar/index", "van-index-anchor": "/miniprogram_npm/@vant/weapp/index-anchor/index", "van-checkbox": "/miniprogram_npm/@vant/weapp/checkbox/index", "van-checkbox-group": "/miniprogram_npm/@vant/weapp/checkbox-group/index", "van-radio": "/miniprogram_npm/@vant/weapp/radio/index", "van-radio-group": "/miniprogram_npm/@vant/weapp/radio-group/index"}, "window": {"navigationBarTitleText": "毕师父导师端", "navigationBarTextStyle": "black", "navigationBarBackgroundColor": "#ffffff"}, "resolveAlias": {"~/*": "/*", "@utils/*": "utils/*"}, "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents", "plugins": {"WechatSI": {"version": "0.3.6", "provider": "wx069ba97219f66d99"}}}