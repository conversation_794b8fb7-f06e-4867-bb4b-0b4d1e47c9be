// pages/mine/comment-list/comment-list.js
import { getMentorCommentList, commentUp, commentDown } from '~/api/appoint'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 分页参数
    commentList: [],
    pageSize: 20,
    pageNumber: 0,
    loading: false,
    finished: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.commentLoad()
  },
  async upChange(event) {
    // const itme = event.currentTarget.dataset.key
    const item = this.data.commentList[event.currentTarget.dataset.key]
    if (item.status === 1) {
      // item.status = 0
      this.setData({
        [`commentList[${event.currentTarget.dataset.key}].status`]: 0
      })
      commentUp({
        appointCommentId: item.id
      }).catch(() => {
        // this.$toast('取消隐藏失败')
        wx.showToast({
          title: '取消隐藏失败',
          icon: 'none'
        })
        this.setData({
          [`commentList[${event.currentTarget.dataset.key}].status`]: 1
        })
      })
    } else {
      this.setData({
        [`commentList[${event.currentTarget.dataset.key}].status`]: 1
      })
      // item.status = 1
      commentDown({
        appointCommentId: item.id
      }).catch(() => {
        // this.$toast('隐藏失败')
        wx.showToast({
          title: '隐藏失败',
          icon: 'none'
        })
        // item.status = 0
        this.setData({
          [`commentList[${event.currentTarget.dataset.key}].status`]: 0
        })
      })
    }
  },
  loadMore() {
    this.commentLoad()
  },
  async commentLoad() {
    if (this.data.loading) {
      return
    }
    if (this.data.finished) {
      return
    }
    this.setData({
      pageNumber: this.data.pageNumber + 1,
      loading: true
    })
    const { data } = await getMentorCommentList({
      pageSize: this.data.pageSize,
      pageNumber: this.data.pageNumber
    })
    if (data && data.length > 0) {
      for(let i = 0 ; i < data.length; i++) {
        // data[i].commentDate = this.dateFormat(data[i].createdAt)
        data[i].commentDate = data[i].createdAt.split(' ')[0]
      }
      const newData = [...this.data.commentList, ...data]
      this.setData({
        commentList: newData
      })
    } else {
      this.setData({
        finished: true
      })
    }
    this.setData({
      loading: false
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})