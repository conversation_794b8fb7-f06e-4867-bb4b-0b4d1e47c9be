import eventBus from './eventBus';

class WebSocketService {
  constructor(url, tenantId) {
    this.url = url;
    this.tenantId = tenantId;
    this.socketTask = null;

    this.isConnected = false;
    this.isConnecting = false;
    this.allowReconnect = true;
    this.lockReconnect = false;
    this.forceClose = false;

    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;

    this.timeout = 10000;
    this.timeoutObj = null;
    this.serverTimeoutObj = null;
    this.reconnectTimer = null;

    this.messageQueue = [];
    this.backgroundMode = false;
    this.msg = null;
  }

  get currentPage() {
    const pages = getCurrentPages();
    return pages.length ? pages[pages.length - 1] : null;
  }

  get isInWebView() {
    return this.currentPage?.route.includes('attached-resume-preview');
  }

  connect() {
    const token = wx.getStorageSync('token');
    if (!token || this.isConnected || this.isConnecting || !this.allowReconnect) return;

    this.isConnecting = true;
    this.forceClose = false;
    this.clearTimers();

    try {
      this.socketTask = wx.connectSocket({
        url: this.url,
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'Tenant_id': this.tenantId,
          'Terminal': 20
        },
        protocols: [token],
        success: () => eventBus.emit('ws-connecting'),
        fail: (err) => {
          this.isConnecting = false;
          eventBus.emit('ws-error', err);
          this.handleReconnect();
        }
      });

      this.initSocketEvents();
      this.handleAppLifecycle();
    } catch (error) {
      this.isConnecting = false;
      eventBus.emit('ws-error', error);
      this.handleReconnect();
    }
  }

  initSocketEvents() {
    this.socketTask.onOpen(() => {
      this.isConnecting = false;
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.lockReconnect = false;
      eventBus.emit('ws-open');
      this.startHeartbeat();
      this.flushMessageQueue();
    });

    this.socketTask.onClose((event) => {
      this.isConnecting = false;
      this.isConnected = false;
      eventBus.emit('ws-close', event);
      this.handleClose(event);
    });

    this.socketTask.onError((error) => {
      this.isConnecting = false;
      this.isConnected = false;
      eventBus.emit('ws-error', error);
      if (!this.forceClose) this.handleReconnect();
    });

    this.socketTask.onMessage((message) => {
      this.handleMessage(message);
    });
  }

  handleMessage(message) {
    try {
      const data = typeof message.data === 'string' ? JSON.parse(message.data) : message.data;
      if (data.sig === 0) return this.handleHeartbeatResponse();
      this.msg = data;
      eventBus.emit('ws-message', data);
    } catch (error) {
      eventBus.emit('ws-error', { message: '消息处理失败', error });
    }
  }

  startHeartbeat() {
    this.clearTimers();
    const interval = this.backgroundMode ? this.timeout * 2 : this.timeout;

    this.timeoutObj = setTimeout(() => {
      this.sendHeartbeat();
      this.serverTimeoutObj = setTimeout(() => {
        this.socketTask?.close();
      }, interval);
    }, interval);
  }

  sendHeartbeat() {
    if (!this.isConnected) return;
    this.socketTask.send({
      data: JSON.stringify({ sig: 0 }),
      fail: () => this.handleReconnect()
    });
  }

  handleHeartbeatResponse() {
    clearTimeout(this.serverTimeoutObj);
    clearTimeout(this.timeoutObj);
    this.startHeartbeat();
  }

  handleClose(event) {
    this.clearTimers();
    if (this.forceClose || !this.allowReconnect) return;
    if (!wx.getStorageSync('token')) return;
    if (event.code === 1005 && this.isInWebView) return;
    this.handleReconnect();
  }

  handleReconnect() {
    if (this.lockReconnect || this.isConnecting || this.isConnected || !this.allowReconnect) return;
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.allowReconnect = false;
      eventBus.emit('ws-reconnect-failed');
      return;
    }

    this.lockReconnect = true;
    this.reconnectAttempts++;
    eventBus.emit('ws-reconnecting', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.maxReconnectAttempts
    });

    this.reconnectTimer = setTimeout(() => {
      this.lockReconnect = false;
      this.socketTask = null;
      this.connect();
    }, 2000);
  }

  sendMessage(message) {
    if (!this.isConnected) {
      this.messageQueue.push(message);
      return false;
    }

    this.socketTask.send({
      data: message,
      fail: (err) => eventBus.emit('ws-send-failed', err)
    });
    return true;
  }

  flushMessageQueue() {
    while (this.messageQueue.length && this.isConnected) {
      this.sendMessage(this.messageQueue.shift());
    }
  }

  clearTimers() {
    clearTimeout(this.timeoutObj);
    clearTimeout(this.serverTimeoutObj);
    clearTimeout(this.reconnectTimer);
  }

  close() {
    this.allowReconnect = false;
    this.forceClose = true;
    this.clearTimers();
    this.reconnectAttempts = 0;

    if (this.socketTask && this.isConnected) {
      this.socketTask.close({
        success: () => {
          this.isConnected = false;
          this.socketTask = null;
        }
      });
    } else {
      this.socketTask = null;
    }
  }

  handleAppLifecycle() {
    wx.onAppShow(() => {
      this.backgroundMode = false;
      if (!this.isConnected) {
        this.lockReconnect = false;
        this.handleReconnect();
      }
    });

    wx.onAppHide(() => {
      this.backgroundMode = true;
    });
  }
}

export default WebSocketService;
