// 全局请求封装
import { env } from '../utils/env'
import { logout } from './loginAbout'
export default (params) => {
  if (params.loading) {
    wx.showLoading({
      title: 'loading...',
      mask: true
    })
  }
  let url = params.url;
  let method = params.method || "GET";
  let data = params.data || {};
  if (params.data && typeof params.data === 'object') {
    data = { ...params.data };
    const arr = Object.keys(data).sort();
    arr.map(key => {
      if (data[key] || data[key] === 0) {
        // 保留非空值和0
        data[key] = data[key];
      } else {
        if (!params.noDelete) {
          delete data[key];
        }
      }
    });
  }
  let header = params.header || {};
  header['Tenant_id'] = env.Tenant_id
  if (method == "POST") {
    header['content-type'] = 'application/json'
  }

  // 获取本地token
  const token = wx.getStorageSync("token");
  if (token) {
    header['Authorization'] = 'Bearer ' + token;
  }
  header['Terminal'] = 20;
  return new Promise((resolve, reject) => {
    wx.request({
      url: env.apiBaseUrl + url,
      method: method,
      header: header,
      data: data,
      responseType: params.responseType || 'text',
      // 'text'：默认值，响应数据会被解析为字符串
      success(response) {
        wx.hideLoading()
        if (response.statusCode == 200) {
          // 如果是arraybuffer类型，直接返回response.data
          if (params.responseType === 'arraybuffer') {
            resolve(response.data);
            return;
          }
          resolve(response.data)
        } else {
          if (response.statusCode) {
            if (response.statusCode === 401) {
              wx.showModal({
                title: "",
                content: "登录已过期，请重新登录",
                showCancel: false,
                success(res) {
                  logout()
                },
              });
            } else if (response.statusCode === 404) {
              wx.showToast({
                title: '请求地址不存在...',
                duration: 2000,
                icon: 'none'
              })
            } else if(response.data.message === '身份验证无效，请重新登录'){
              wx.showModal({
                title: "",
                content: "登录已过期，请重新登录",
                showCancel: false,
                success(res) {
                  logout()
                },
              });
            } else {
              wx.showToast({
                title: response.data.message,
                duration: 2000,
                icon: 'none'
              })
            }
            return reject({
              statusCode: response.statusCode,
              message: response.data?.message || '请求错误',
              response
            });
          }
        }
      },
      fail(err) {
        wx.hideLoading()
        if (err.errMsg.indexOf('request:fail') !== -1) {
          wx.showToast({
            title: '网络异常',
            icon: "none",
            duration: 2000
          });
        } else {
          wx.showToast({
            title: '未知异常',
            duration: 2000,
            icon: 'none'
          });
        }
        reject(err);
      },
      complete() {
        // wx.hideLoading();
        // wx.hideToast();
      }
    });
  })
};