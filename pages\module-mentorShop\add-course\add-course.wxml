<!--pages/module-mentorShop/add-column/add-column.wxml-->
<scroll-view scroll-y class="course" enhanced show-scrollbar="{{false}}" bounces="{{true}}" bindscrolltolower="loadMore">
  <!-- <view class="course" > -->
  <view class="tab-container">
    <view>
      <view class="tab {{activeTab === 'video' ? 'active' : ''}}" bindtap="switchTab" data-type="video">
        视频
      </view>
      <view class="tab {{activeTab === 'article' ? 'active' : ''}}" bindtap="switchTab" data-type="article">
        图文
      </view>
    </view>
  </view>

  <!-- 视频课程列表 -->
  <view class="video-section">
    <van-checkbox-group wx:if="{{courses && courses.length > 0}}" value="{{ result }}" bind:change="onChange">
      <view class="box">
        <view wx:for="{{courses}}" wx:key="id">
          <view class="course-itema" data-id="{{item.id}}">
            <van-checkbox name="{{item.id}}"></van-checkbox>
            <!-- wx:if="{{!item.columnsRelate}}" -->
            <view class="course-item">
              <course  add="{{true}}" column-data="{{item}}" />
            </view>
          </view>
        </view>
      </view>
      <view class="finishedText">
        <text wx:if="{{!finished}}">加载中...</text>
        <text wx:else>暂无更多</text>
      </view>
    </van-checkbox-group>

    <view wx:else class="finishedText">
      <text wx:if="{{!finished}}">加载中...</text>
      <view class="empty" wx:else="{{finished}}">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/book-empty.png" />
        <text>暂无课程，请在店铺页面创建课程</text>
      </view>
    </view>
  </view>

  <van-action-sheet custom-class='van-action-sheet' show="{{ show }}" z-index='2' title="已选择课程" bind:close="onClose">
    <view class="course-list" wx:if="{{selectedItems && selectedItems.length > 0}}">
      <view wx:for="{{selectedItems}}" wx:key="id" data-id="{{item.id}}">
        <course  bind:handleCourseRemove="onCourseRemove" select="{{true}}" course-remove="{{true}}" column-data="{{item}}" />
      </view>
    </view>
    <view wx:else class="empty">
      <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/book-empty.png" />
      <text>暂无已选择课程</text>
    </view>

  </van-action-sheet>

  <view class="bottom-bar" wx:if="{{courses && courses.length > 0}}">
    <view class="association">
      <view bindtap="onToggleSheet">
        <text class="selected-count">已选择：{{result.length}}</text>
        <van-icon name="{{show?'arrow-up':'arrow-down'}}" color='#0057ff' />
      </view>
      <view class="confirm-btn" bindtap="handleConfirm">确认</view>
    </view>
  </view>
  <!-- </view> -->
</scroll-view>