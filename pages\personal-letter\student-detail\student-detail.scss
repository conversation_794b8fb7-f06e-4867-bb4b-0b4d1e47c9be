.student-detail{
  .student-basic-detail {
    background: url('https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/IM/student-detail-bg.png');
    background-size: cover;
    padding: 40rpx 40rpx 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .student-img {
      // background-color: red;
      width: 176rpx;
      height: 208rpx;
      border-radius: 16rpx;
    }
    .left {
      .p {
        margin: 0;
      }
      .student-name {
        color: #333333;
        font-size: 40rpx;
        font-weight: 600;
        line-height: 48rpx;
        margin-bottom: 20rpx;
      }
      .major {
        margin: 16rpx 0;
      }
      .school,
      .major,
      .phone {
        display: flex;
        align-items: center;
        image {
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
        }
        .p {
          color: #4b5362;
          font-size: 24rpx;
          line-height: 34rpx;
          text:nth-child(2) {
            display: inline-block;
            width: 1rpx;
            height: 24rpx;
            background-color: #bbbbbb;
            margin: 0 20rpx;
          }
        }
      }
    }
  }
  .student-detail {
    margin: 20rpx 40rpx;
  
    .basic,
    .employment,
    .education,
    .job,
    .mass,
    .other {
      padding: 50rpx 0;
      position: relative;
      .title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        image {
          width: 48rpx;
          height: 48rpx;
        }
        text {
          margin-left: 16rpx;
          color: #333333;
          font-size: 34rpx;
          font-weight: 600;
          line-height: 40rpx;
        }
      }
      .line-item {
        color: #4b5362;
        font-size: 28rpx;
        line-height: 34rpx;
        margin-bottom: 16rpx;
      }
      .line-item:last-of-type {
        margin-bottom: 0;
      }
    }
    .name {
      color: #333333;
      font-size: 30rpx;
      line-height: 36rpx;
      font-weight: 600;
    }
    .time {
      color: #919bae;
      font-size: 28rpx;
      line-height: 32rpx;
    }
    .introduce {
      color: #4b5362;
      font-size: 28rpx;
      line-height: 44rpx;
    }
    .introduce1 {
      line-height: 32rpx;
      .position-item {
        &::after {
          content: '/';
        }
        &:nth-last-child(1) {
          &::after {
            display: none;
          }
        }
      }
      .expectation-other {
        line-height: 40rpx;
      }
    }
    .education-content {
      .time {
        margin: 6rpx 0;
      }
    }
    .student-module {
      margin-bottom: 48rpx;
    }
    .student-module:last-of-type {
      margin-bottom: 0;
    }
    .job,
    .mass {
      .introduce {
        margin: 16rpx 0;
      }
      .introduce:last-of-type {
        margin-bottom: 0;
      }
    }
  }
  .other {
    .introduce {
      margin-bottom: 16rpx;
    }
    .introduce:last-of-type {
      margin-bottom: 0;
    }
  }
}