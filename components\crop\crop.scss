.top {
  position: absolute;
  width: 100%;
  top: 10rpx;
  display: flex;
  flex-flow: wrap;
  z-index: 10;
  color: white;
  justify-content: space-around;
}

.hint {
  position: absolute;
  top: 10rpx;
  width: 100%;
  font-size: 33rpx;
  text-align: center;
  color: white;
  z-index: 10;
}

page {
  background: white;
}

// view {
//   font-size: 30rpx;
// }

.bottom {
  position: absolute;
  width: 100%;
  bottom: 0rpx;
  z-index: 10;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  box-sizing: border-box;
  image {
    width: 30px;
    height: 30px;
  }
  view:nth-child(1),
  view:nth-child(2) {
    font-size: 20px;
    color: #ffffff;
  }
  button {
    border-radius: 8px;
    height: 35px !important;
    font-size: 24rpx !important;
  }
}

// button {
//   font-size: 27rpx;
//   z-index: 2;
//   padding: 0 20rpx;
//   height: 60rpx;
//   min-width: 70rpx;
//   margin: 0 4rpx;
// }

// .input {
//   display: flex;
//   height: 50rpx;
//   width: 50%;
// }

// .input > .label {
//   min-width: 150rpx;
//   font-size: 30rpx;
//   height: 50rpx;
//   line-height: 50rpx;
// }

// .input > input {
//   margin-left: 10rpx;
//   text-align: center;
//   max-width: 160rpx;
//   border: 1px solid rgb(255, 255, 255);
//   height: 50rpx;
//   line-height: 50rpx;
//   min-height: 50rpx;
//   box-sizing: border-box;
// }
