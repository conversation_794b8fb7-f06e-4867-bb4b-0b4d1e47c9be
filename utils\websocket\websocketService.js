import WebSocketService from "./websocket";
import { env } from "../env";
import { initGlobalMessageWatcher } from "./globalMessageWatcher";
import createMessageWatcher from "./watchMessage";

// 导出WebSocket连接函数
export const connectWebSocket = async (tenantFunction) => {
  const app = getApp();
  const existingWebSocket = app.globalData.websocket;

  // 如果 WebSocket 已连接，直接返回现有实例
  if (existingWebSocket && existingWebSocket.isConnected) {
    console.log('WebSocket 已连接，复用现有连接');
    return existingWebSocket;
  }

  // 提前检查 token 和 tenantFunction 是否满足连接条件
  const token = wx.getStorageSync('token');
  const imFlag = tenantFunction.includes('userPrivateMessageList');

  if (!token || !imFlag) {
    console.log('未满足连接条件，跳过 WebSocket 连接');
    return null;
  }

  // 创建 WebSocket 实例
  const websocket = new WebSocketService(
    `${env.wsApi}/user-front-service/ws/mentor/h5`,
    env.Tenant_id
  );
  try {
    // 连接 WebSocket
    await websocket.connect();

    // 初始化全局消息监听器
    initGlobalMessageWatcher(createMessageWatcher);

    // 将 WebSocket 实例保存到全局
    app.globalData.websocket = websocket;

    console.log('WebSocket 连接成功');
  } catch (error) {
    console.error('WebSocket 连接失败:', error);
  }

  return websocket;
};
