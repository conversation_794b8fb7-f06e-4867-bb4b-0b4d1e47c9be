import request from '../utils/request';
import { env } from '~/utils/env'
// 文件上传
export function uploadFile({ filePath, name = 'file', formData = {} }) {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: `${env.apiBaseUrl}/common-service/upload`,
      filePath,
      name,
      formData: {
        ...formData,
        path: 'mentor'
      },
      header: {
        'Content-Type': 'multipart/form-data' // 让微信自动处理 `Content-Type`
      },
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            resolve(JSON.parse(res.data)); // 解析后端返回的 JSON 数据
          } catch (error) {
            reject({ errMsg: '解析 JSON 失败', rawData: res.data });
          }
        } else {
          reject(res);
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

// 简历解析的文件上传
export function resumeAnalyze(filePath, name = 'file', formData = {}) {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: `${env.apiBaseUrl}/recruit-front-service/resume/analyze/save/mentor/info`,
      filePath,
      name,
      formData: {
        ...formData,
        path: 'mentor'
      },
      header: {
        'Content-Type': 'multipart/form-data', // 让微信自动处理 `Content-Type`
        'Authorization': 'Bearer ' + wx.getStorageSync("token"),
        'Terminal': 20,
        'Tenant_id': env.Tenant_id
      },
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            resolve(JSON.parse(res.data)); // 解析后端返回的 JSON 数据
          } catch (error) {
            reject({ errMsg: '解析 JSON 失败', rawData: res.data });
          }
        } else {
          reject(res);
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

// im文件上传
export function uploadImFile(data, path) {
  data.append('path', path)
  return request({
    url: '/common-service/upload',
    method: 'post',
    data,
    hideToken: true,
    uploadForm: true
  })
}
