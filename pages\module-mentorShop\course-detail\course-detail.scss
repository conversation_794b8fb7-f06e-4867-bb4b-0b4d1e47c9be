/* pages/module-mentorShop/course-detail/course-detail.wxss */
page {
  height: 100%;
}
.mentor-introduce {
  margin-top: 12rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #000000;
  line-height: 36rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 控制最大行数 */
  overflow: hidden;
}
.mentor-introduce-position {
  padding: 0 36rpx;
  width: 100vw;
  display: block;
  margin-top: 0;
  position: fixed;
  left: -9999px;
  top: 0;
}
.show-all-text {
  display: block;
}
.course-detail {
  background: #f5f5f5;
  // min-height: 100vh;
  .course-banner {
    position: relative;
    width: 100%;
    height: 422rpx;

    video,image {
      width: 100%;
      height: 100%;
    }

    .video-mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;

      .mask-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #fff;

        .lock-icon {
          width: 62rpx;
          height: 76rpx;
          margin-bottom: 22rpx;
        }

        .mask-text {
          font-size: 22rpx;
        }
      }
    }
  }

  .course-header {
    background: #fff;
    padding: 22rpx 36rpx 26rpx;
    margin-bottom: 24rpx;
    .title {
      font-size: 40rpx;
      line-height: 47rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 8rpx;
      word-break: break-all;
    }
    .meta-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .meta {
        .tag {
          background: hsla(220, 100%, 50%, 0.16);
          color: #0057ff;
          padding: 8rpx 12rpx;
          border-radius: 8rpx;
          margin-right: 20rpx;
          font-size: 20rpx;
          display: inline-block;
          margin-bottom: 20rpx;
          .tag-img {
            width: 20rpx;
            height: 20rpx;
            vertical-align: middle;
          }
        }

        .date {
          color: #aaaaaa;
          font-size: 22rpx;
          view{
            color: #0057FF;
            padding-right: 16rpx;
          }
        }
      }
      .price-box {
        .price {
          // font-size: 48rpx;
          font-size: 64rpx;
          color: #ff0019;
          font-weight: bold;
          text:first-child {
            font-size: 40rpx;
          }
        }
        .participants {
          color: #9ea3ac;
          font-size: 24rpx;
          text-align: right;
        }
      }
    }
  }

  .instructor-section {
    background: #fff;
    padding: 30rpx 36rpx 28rpx;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 30rpx;
    }
    .mentor-base {
      .mentor-base-info {
        display: flex;
        align-items: center;
        .avatar {
          width: 118rpx;
          height: 118rpx;
          margin-right: 22rpx;
          border-radius: 50%;
          flex-shrink: 0;
        }
        .info {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .info-left {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            .info-name {
              display: flex;
              .name {
                flex: 1;
                font-weight: bold;
                font-size: 28rpx;
                color: #000000;
                line-height: 41rpx;
                text-align: center;
                font-style: normal;
                text-transform: none;
                padding-right: 18rpx;
              }
              .label {
                padding: 2rpx 8rpx;
                font-weight: 400;
                font-size: 20rpx;
                color: #ffffff;
                line-height: 32rpx;
                text-align: center;
                font-style: normal;
                text-transform: none;
                background: linear-gradient(to bottom, #FFB72D, #FF9E0C);
                border-radius: 5rpx;
                // border: 1rpx solid #6B82F6;
              }
            }
            .company {
              margin-top: 10rpx;
              font-weight: 400;
              font-size: 24rpx;
              color: #737783;
              text-align: center;
              font-style: normal;
              text-transform: none;

              view {
                text-align: left;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
              }
            }
          }
          .info-right {
            image {
              width: 32rpx;
              height: 32rpx;
            }
          }
        }
      }
      .mentor-appoint-star {
        display: flex;
        align-items: center;
        margin: 10rpx 0;

        .star-icon {
          width: 24rpx;
          height: 24rpx;
          margin-right: 4rpx;

          &:last-of-type {
            margin-right: 8rpx;
          }
        }

        text {
          color: #c18b4c;
          font-size: 20rpx;
          line-height: 24rpx;
        }
      }
      .unfold {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 10rpx;
        .arrow-bottom,
        .arrow-top {
          width: 20rpx;
          height: 20rpx;
        }
        .arrow-top {
          transform: rotate(180deg);
        }
      }
    }
  }

  .students-section {
    background: #fff;
    padding: 30rpx 36rpx 28rpx;
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;

      .title {
        font-size: 28rpx;
        color: #333;
      }

      .more {
        font-size: 28rpx;
        color: #0057ff;
        font-weight: bold;
      }
    }

    .student-list {
      display: flex;
      flex-wrap: wrap;

      .student-avatar {
        width: 58rpx;
        height: 58rpx;
        border-radius: 40rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
      }
    }
  }
}
.course-bottom {
  padding: 24rpx 40rpx 30rpx;
  box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
  position: relative;
  button {
    width: 100%;
    background: #0057ff;
    border: none;
    color: rgb(255, 255, 255);
    font-size: 32rpx;
    line-height: 32rpx;
    font-weight: 400;
    height: auto;
    padding: 30rpx 0;
    border-radius: 46rpx;
  }
}
.student-list-empty {
  color: #737783;
  font-size: 28rpx;
}
