.mentor-apply-introduction{
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #F7F8FB;
  .content{
    padding: 24rpx 36rpx;
    flex: 1;
    overflow: auto;
    .process{
      width: 100%;
      // margin-top: -70rpx;
      background: #FFFFFF;
      box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(99,97,155,0.1);
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      padding: 30rpx 30rpx 38rpx;
      .title{
        font-weight: bold;
        font-size: 32rpx;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .process-box{
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-top: 16rpx;
        padding: 0 30rpx;
        .process-item{
          display: flex;
          flex-direction: column;
          align-items: center;
          .process-item-title{
            font-weight: 600;
            font-size: 48rpx;
            color: #333333;
            text-align: center;
            font-style: normal;
            text-transform: none;
            position: relative;
            .icon{
              position: absolute;
              background: rgba(0,87,255,0.6);
              border-radius: 4rpx 4rpx 4rpx 4rpx;
              width: 48rpx;
              height: 8rpx;
              bottom: 10rpx;
              left: 50%;
              transform: translateX(-50%);
            }
          }
          .process-item-text{
            margin-top: 8rpx;
            font-weight: 500;
            font-size: 26rpx;
            color: #333333;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
        }
        .process-transition{
          padding-top: 8rpx;
          color: rgba(0,87,255,0.8);
          image{
            height: 22rpx;
            width: 22rpx;
          }
        }
      }
    }
    .required-materials{
      box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(99,97,155,0.1);
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      padding: 30rpx;
      background: #FFFFFF;
      margin-top: 24rpx;
      .title{
        font-weight: bold;
        font-size: 32rpx;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .required-materials-position{
        margin-top: 16rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left{
          flex: 1;
          .left-title{
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .left-value{
            margin-top: 8rpx;
            font-weight: 400;
            font-size: 22rpx;
            color: #777777;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
        .right{
          border: 1rpx dashed #979797;
          margin-left: 16rpx;
          width: 180rpx;
          height: 108rpx;
          position: relative;
          image{
            width: 100%;
            height: 100%;
          }
          .example{
            width: 62rpx;
            height: 62rpx;
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }
    }
    .question{
      box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(99,97,155,0.1);
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      padding: 30rpx;
      background: #FFFFFF;
      margin-top: 24rpx;
      .title{
        font-weight: bold;
        font-size: 32rpx;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .subtitle{
        margin-top: 16rpx;
        display: flex;
        align-items: center;
        padding-bottom: 4rpx;
        .subtitle-item{
          margin-right: 16rpx;
          background: rgba(119,119,119,0.15);
          border-radius: 8rpx 8rpx 8rpx 8rpx;
          border: 1rpx solid #777777;
          font-weight: 500;
          font-size: 22rpx;
          color: #777777;
          text-align: left;
          font-style: normal;
          text-transform: none;
          padding: 4rpx 16rpx;
        }
        .active{
          background: rgba(0,87,255,0.15);
          border: 1rpx solid #0057FF;
          color: #0057FF;
        }
      }
      .value-item{
        margin-top: 16rpx;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        .icon{
          width: 10rpx;
          height: 10rpx;
          margin-right: 8rpx;
          border-radius: 50%;
          background: #0057FF;
          margin-top: 10rpx;
        }
        .value{
          flex: 1;
          font-weight: 500;
          font-size: 22rpx;
          color: #777777;
          text-align: justified;
          font-style: normal;
          text-transform: none;
          .value-title{
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
            text-align: justified;
            font-style: normal;
            text-transform: none;
            margin-bottom: 5rpx;
          }
        }
      }
    }
  }
  .footer{
    background: #FFF;
    padding: 20rpx 48rpx 40rpx;
		.login-accredit{
      width: 100%;
			// margin-bottom: 24rpx;
			width: 100%;
			height: 96rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #0057FF;;
			border-radius: 62rpx;
			font-weight: 600;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 32rpx;
			text-align: center;
      font-style: normal;
      margin-left: 0;
      margin-right: 0;
    }
    .login-box{
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .exit{
        display: flex;
        align-items: center;
        image{
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
        }
        text{
          font-weight: bold;
          font-size: 24rpx;
          color: #333333;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
      .btn-box{
        flex: 1;
        margin-left: 40rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .login-btn{
          height: 96rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #0057FF;;
          border-radius: 62rpx;
          font-weight: 600;
          font-size: 24rpx;
          color: #FFFFFF;
          line-height: 32rpx;
          text-align: center;
          font-style: normal;
          // margin-left: 16rpx;
          margin-right: 0;
          padding: 0;
          // &:nth-child(1) {
          //   margin-left: 0;
          //   padding: 0;
          // }
        }
        .left-btn{
          margin-left: 0;
          width: 210rpx;
        }
        .right-btn{
          margin-left: 16rpx;
          flex: 1;
        }
      }
    }
  }
}
