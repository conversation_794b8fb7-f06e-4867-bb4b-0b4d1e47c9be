// pages/personal-letter/student-detail/student-detail.js
import { getStudentInfo, getStudentDetail } from '~/api/user'
import moment from 'moment'
import { educationLevelFilter } from '~/utils/filter'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    studentId: '',
    studentInfo: {},
    studentDetail: {
      jobExpectation: [{}],
      candidateEducation: [{}],
      candidateProfessional: [{}],
      candidateOrganization: [{}]
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      studentId: options.studentId
    })
    this.getStudentInfo()
    this.getStudentDetail()
  },
  async getStudentInfo() {
    const { data } = await getStudentInfo({ userId: this.data.studentId })
    if (data.sex || data.sex === 0) {
      const sexObject = {
        0: '男',
        1: '女'
      }
      data.newSex = sexObject[data.sex]
    }
    if (data.politicalOutlook) {
      const politicalOutlook = {
        10: '群众',
        20: '共青团员',
        30: '党员'
      }
      data.newPoliticalOutlook = politicalOutlook[data.politicalOutlook]
    }
    if (data.birthday) {
      data.newBirthday = moment(data.birthday).format('YYYY-MM')
    }
    this.setData({
      studentInfo: data || {}
    })
  },
  async getStudentDetail() {
    const { data } = await getStudentDetail({ userId: this.data.studentId })
    // this.studentDetail = data || {}
    if (data.other) {
      data.newOther = this.getHtmlValue(data.other)
    }
    if (data.jobExpectation && data.jobExpectation.length > 0) {
      const nature = {
        10: '全职',
        20: '兼职',
        30: '实习生'
      }
      data.jobExpectation.forEach(item => {
        item.newNature = nature[item.nature]
      })
    }
    if (data.candidateEducation && data.candidateEducation.length > 0) {
      data.candidateEducation.forEach(item => {
        item.newStartDate = moment(item.startDate).format('YYYY-MM')
        item.newEndDate = moment(item.endDate).format('YYYY-MM')
        item.newEducationLevel = educationLevelFilter(item.educationLevel)
      })
    }
    if (data.candidateProfessional && data.candidateProfessional.length > 0) {
      data.candidateProfessional.forEach(item => {
        item.newStartDate = moment(item.startDate).format('YYYY-MM')
        item.newEndDate = moment(item.endDate).format('YYYY-MM')
        item.newProfessionalIntroduction = this.getHtmlValue(item.professionalIntroduction)
      })
    }
    if (data.candidateOrganization && data.candidateOrganization.length > 0) {
      data.candidateOrganization.forEach(item => {
        item.newStartDate = moment(item.startDate).format('YYYY-MM')
        item.newEndDate = moment(item.endDate).format('YYYY-MM')
        item.newProfessionalIntroduction = this.getHtmlValue(item.professionalIntroduction)
      })
    }
    this.setData({
      studentDetail: data || {}
    })
  },
  getHtmlValue(value) {
    return value ? value.replace(/\n/g, '<br/>') : ''
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})