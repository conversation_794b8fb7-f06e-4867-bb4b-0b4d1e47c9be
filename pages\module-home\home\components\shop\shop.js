// pages/appoint/mentor-appoint/components/shop/shop.js
import moment from 'moment'
import {
  getColumnList,
  getCourseList,
  updateCourseStatus
} from '~/api/mentorShop';
import { navigateAndAddPage } from '~/utils/pageNavigation';
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    mentorId: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentTab: 'column',
    activeTab: 'video', // 默认选中视频标签
    columnCourses: [],
    courses: [],
    showFilter: false,
    /**
     * @status: 
     *  ''：全部
     *  '1'：上架
     *  '0': '下架'
     *  '2': '审核中',
     *  '3': '审核失败'
     */
    courseStatus: '', // 默认显示全部
    filterText: '全部',
   
    activeTabNum: 10,
    total: 0,
    currentCourseId: null,
    loading: false,
    needRefresh: false, // 添加标志变量，控制是否需要刷新
    page: {
      pageSize: 20, // 每页数量
      pageNumber: 1, // 当前页码
    },
    finished: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  attached() {
    // 首次加载时设置需要刷新的标志
    // this.setData({
    //   needRefresh: true
    // });
    this.getCourseList();
    console.log('onLoad+++++++');
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 页面显示时根据标志决定是否重置页码并重新加载数据
    onShow() {
      console.log('店铺show');
      this.getColumnList();
      if (wx.getStorageSync('courseRefresh')) {
        wx.removeStorageSync('courseRefresh')
        this.setData({
          page: {
            pageSize: 20,
            pageNumber: 1
          },
          finished: false,
          loading: false,
          courses: []
        }, () => {
          this.getCourseList();
        });
      }
      // 调用接口判断哪些数据发生了变化，有变化的时候根据我们当前已经缓存着的数据一 一修改，两个列表都有数据就修改，没数据不需要动
      // 如果需要刷新，才重置页码并重新加载数据
      // if (this.data.needRefresh) {
      //   this.setData({
      //     page: {
      //       pageSize: 20,
      //       pageNumber: 1
      //     },
      //     finished: false,
      //     loading: false,
      //     courses: [],
      //     needRefresh: false // 重置标志
      //   }, () => {
      //     this.getCourseList();
      //   });
      // }
    },

    // 设置需要刷新的标志
    setNeedRefresh() {
      // this.setData({
      //   needRefresh: true
      // });
    },

    // 获取专栏列表
    getColumnList() {
      getColumnList({
        pageNumber: 1,
        pageSize: 2
      }).then(res => {
        this.setData({
          columnCourses: res.data,
          total: res.paging.total
        });
      })
    },

    // 获取课程列表
    async getCourseList() {
      if (this.data.loading) return;

      this.setData({
        loading: true,
      });
      let courseStatus = ''
      let auditStatus = ''
      if (this.data.courseStatus === '') {
        courseStatus = ''
        auditStatus = ''
      } else if (this.data.courseStatus === '1') {
        courseStatus = '1'
        auditStatus = '20'
      } else if (this.data.courseStatus === '0') {
        courseStatus = '0'
        auditStatus = '20'
      } else if (this.data.courseStatus === '2') {
        courseStatus = ''
        auditStatus = '10'
      } else if (this.data.courseStatus === '3') {
        courseStatus = ''
        auditStatus = '30'
      }
      const {
        data
      } = await getCourseList({
        ...this.data.page,
        courseType: this.data.activeTabNum,
        courseStatus,
        auditStatus
      });

      if (data.length < this.data.page.pageSize) {
        this.setData({
          finished: true
        });
      }
      // 处理课程时长格式
      const formattedData = data?.map(item => ({
        ...item,
        courseDuration: item.courseDuration ? moment.utc(item.courseDuration * 1000).format('HH:mm:ss') : '00:00:00'
      }));
      const newList = this.data.page.pageNumber === 1 ?
        formattedData :
        [...this.data.courses, ...formattedData];
      this.setData({
        courses: newList,
        loading: false
      });

    },

    loadMore() {
      if (this.data.loading || this.data.finished) return;
      this.setData(
        {
          'page.pageNumber': this.data.page.pageNumber + 1,
        },
        () => {
          this.getCourseList();
        }
      );
    },
    // 切换标签页
    switchTab(e) {
      const type = e.currentTarget.dataset.type;
      this.setData({
        activeTab: type,
        activeTabNum: type === 'video' ? 10 : 20,
        page: {
          pageSize: 20,
          pageNumber: 1
        },
        finished: false,
        loading: false,
        courses: [],
        courseStatus: '',
        filterText: '全部'
      });
      this.getCourseList();
    },

    // 点击更多
    onMoreTap() {
      navigateAndAddPage('/pages/module-mentorShop/all-column/all-column');
      // wx.navigateTo({
      //   url: '/pages/module-mentorShop/all-column/all-column'
      // });
    },

    // 创建专栏
    onCreateColumn() {
      navigateAndAddPage('/pages/module-mentorShop/add-column/add-column')
      // wx.navigateTo({
      //   url: '/pages/module-mentorShop/add-column/add-column'
      // });
    },

    // 点击课程
    onCourseClick(e) {
      const courseId = e.currentTarget.dataset.id;
      const type = e.currentTarget.dataset.type || this.data.activeTab;
      const tempForm = wx.getStorageSync('tempVideoForm');
      if (tempForm) {
        wx.removeStorage({
          key: 'tempVideoForm'
        });
      }
      if (type === 'course') {
        navigateAndAddPage('/pages/module-mentorShop/column-detail/column-detail',{
          id:courseId,
          mentorId:this.properties.mentorId
        })
        // wx.navigateTo({
        //   url: `/pages/module-mentorShop/column-detail/column-detail?id=${courseId}&mentorId=${this.properties.mentorId}`
        // });
      } else {
        navigateAndAddPage('/pages/module-mentorShop/course-detail/course-detail',{
          id:courseId,
          mentorId:this.properties.mentorId,
          type:type,
          isEdit:1
        })
        // wx.navigateTo({
        //   url: `/pages/module-mentorShop/course-detail/course-detail?id=${courseId}&mentorId=${this.properties.mentorId}&type=${type}&isEdit=1`
        // });
      }
    },

    // 点击筛选按钮
    onFilterTap() {
      this.setData({
        showFilter: !this.data.showFilter
      });
    },

    // 选择筛选选项
    onFilterSelect(e) {
      const type = e.currentTarget.dataset.type;
      let filterText = '已上架';

      switch (type) {
        case '':
          filterText = '全部';
          break;
        case '1':
          filterText = '已上架';
          break;
        case '0':
          filterText = '已下架';
          break;
        case '2':
          filterText = '审核中';
          break;
        case '3':
          filterText = '审核失败';
          break;
      }

      this.setData({
        page: {
          pageSize: 20,
          pageNumber: 1
        },
        finished: false,
        loading: false,
        courses: [],
        courseStatus: type,
        filterText: filterText,
        showFilter: false // 关闭下拉框
      });

      this.getCourseList();
    },

    showReason(e) {
      const { courseId, status, shelfReason, index, courseType } = e.currentTarget.dataset;
      this.setData({ currentCourseId: courseId });

      const updateStatus = (newStatus, successMsg) => {
        updateCourseStatus({ coursesId: courseId, loadStatus: newStatus })
          .then(() => {
            this.setData({ [`courses[${index}].loadStatus`]: newStatus });
            if (successMsg) {
              // this.setData({
              //   finished: false,
              //   loading: false,
              //   'page.pageNumber': 1,
              //   courses: []
              // })
              // this.getCourseList()
              console.log(this.data.courseStatus, 'this.data.courseStatus')
              if (this.data.courseStatus.toString() === '1' || this.data.courseStatus.toString() === '0' || this.data.courseStatus.toString() === '2' || this.data.courseStatus.toString() === '3') {
                const filterData = this.data.courses.filter(item => {
                  return item.id !== courseId
                })
                console.log(filterData, 'filterData')
                this.setData({
                  courses: filterData
                })
              }
              wx.showToast({ title: successMsg, icon: 'none' });
            }
          })
          .catch(() => {
            wx.showToast({ title: '操作失败，请重试', icon: 'none' });
          });
      };

      // 下架原因弹窗
      if (status === 0 && shelfReason) {
        return wx.showModal({
          title: '下架原因',
          content: shelfReason,
          showCancel: true,
          cancelText: '取消',
          confirmText: '前往修改',
          success: (res) => {
            if (res.confirm) {
              if( this.data.activeTab === 'video'){
                navigateAndAddPage('/pages/module-mentorShop/add-video/add-video',{
                  id:courseId
                })
                // wx.navigateTo({
                //   url: `/pages/module-mentorShop/add-video/add-video?id=${courseId}`
                // });
              }else{
                navigateAndAddPage('/pages/module-mentorShop/add-article/add-article',{
                  id:courseId,
                  courseType:courseType
                })
                // wx.navigateTo({
                //   url: `/pages/module-mentorShop/add-article/add-article?id=${courseId}&courseType=${courseType}`
                // });
              }
            }
          }
        });
      }

      // 状态切换逻辑
      if (status === 1) {
        // 确认下架弹窗
        wx.showModal({
          title: '下架',
          content: '下架后，已购用户仍可以学习，未购用户将无法查看和购买，确认下架？',
          showCancel: true,
          cancelText: '取消',
          confirmText: '确认',
          success: (res) => {
            if (res.confirm) {
              updateStatus(0, '已下架');
            }
          }
        });
      } else if (status === 0) {
        // 上架
        updateStatus(1, '已上架');
      }
    },
    reviewFailedReason(e) {
      const { courseId, shelfReason, courseType } = e.currentTarget.dataset;
      wx.showModal({
        title: '失败原因',
        content: shelfReason,
        showCancel: true,
        cancelText: '取消',
        confirmText: '前往修改',
        success: (res) => {
          if (res.confirm) {
            if( this.data.activeTab === 'video'){
              navigateAndAddPage('/pages/module-mentorShop/add-video/add-video',{
                id:courseId
              })
            }else{
              navigateAndAddPage('/pages/module-mentorShop/add-article/add-article',{
                id:courseId,
                courseType:courseType
              })
            }
          }
        }
      })
    }
  }
})