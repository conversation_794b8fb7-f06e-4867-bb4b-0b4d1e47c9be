<!--pages/login-phone/login-phone.wxml-->
<view class="login-phone">
  <view class="login-phone-bg">
    <image class="bg" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/mentor-app/login-bg.png" alt=""/>
  </view>
  <view class="phone-login-title" wx:if="{{type === 'apply'}}">导师注册</view>
  <view class="phone-login-title" wx:if="{{type === 'review'}}">查看审核状态</view>
  <view class="login-form">
    <view class="form-item form-phone" wx:if="{{type === 'apply'}}">
      <view class="input">
        <van-field
          model:value="{{ userName }}"
          placeholder="请输入导师姓名"
          border="{{ false }}"
          maxlength="20"
        />
      </view>
    </view>
    <view class="form-item form-phone">
      <view class="label">+86</view>
      <view class="input">
        <van-field
          type="number"
          model:value="{{ value }}"
          placeholder="请输入手机号"
          border="{{ false }}"
          maxlength="11"
        />
      </view>
    </view>
    <view class="form-item form-code">
      <view class="input">
        <van-field
          type="number"
          model:value="{{ phoneCode }}"
          placeholder="请输入验证码"
          border="{{ false }}"
          maxlength="6"
        />
      </view>
      <view class="get-code get-code-disabled" wx:if="{{countDown}}">{{countDown}}</view>
      <view class="get-code" bindtap="getPhoneCode" wx:else>获取验证码</view>
    </view>
    <view class="agreement" bind:tap="onChange">
      <van-checkbox
      value="{{ checked }}"
      icon-size="28rpx"
      checked-color="#0C7AFF"
      >
        <view class="text-value">
          我已阅读并同意
          <text catchtap="toUserAgreement" data-page="agreement">《用户协议》</text>
          <text catchtap="toUserPrivacy" data-page="data-page">《隐私政策》</text>
        </view>
      </van-checkbox>
		</view>
    <view class="login-btn" wx:if="{{type === 'apply'}}" bind:tap="register">下一步</view>
    <view class="login-btn" wx:if="{{type === 'review'}}" bind:tap="register">下一步</view>
  </view>
</view>