<!--pages/module-mentorShop/column-detail/column-detail.wxml-->
<scroll-box backgroundColor='#F8F8F8'>
  <scroll-view slot='content' scroll-y enhanced show-scrollbar="{{false}}" class="scroll-box-content" bounces="{{true}}" bindscrolltolower="loadMore">
    <view class="filter" bindtap="onFilterTap">
      {{filterText}}
      <van-icon name="{{showFilter ? 'arrow-up' :'arrow-down'}}" />
      <view class="filter-dropdown {{showFilter ? 'show' : ''}} {{ courses.length <= 1 ? 'filter-dropdown-top' : '' }}">
        <view class="filter-item {{courseStatus === '' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="">全部</view>
        <view class="filter-item {{courseStatus === '1' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="1">已上架</view>
        <view class="filter-item {{courseStatus === '0' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="0">已下架</view>

        <view class="filter-item {{courseStatus === '2' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="2">审核中</view>
        <view class="filter-item {{courseStatus === '3' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="3">审核失败</view>
      </view>
    </view>

    <view class="video-section" wx:if="{{courses.length > 0}}">
      <view class="box">
        <view class="course-item1" wx:for="{{courses}}" wx:key="id" bindtap="onCourseClick" data-id="{{item.id}}" data-mentor-id="{{item.mentorUserId}}"  data-type="{{item.courseType}}">
          <view class="course-item">
            <view class="image-wrapper">
              <image class="course-image" src="{{item.courseCoverUrl}}" mode="aspectFill" />
              <view class="course-mask" wx:if="{{item.auditStatus===20 && item.loadStatus===0}}">已下架</view>
              <view class="course-mask" wx:if="{{item.auditStatus===10}}">审核中</view>
              <view class="course-mask" wx:if="{{item.auditStatus===30}}">审核失败</view>
            </view>
            <view class="course-info">
              <view class="top">
                <view class="course-meta">
                  <view class="course-title {{add ? 'course-title-select':''}}">{{item.courseName}}</view>
                  <view class="duration" wx:if="{{item.courseType === 10}}" >{{item.courseDuration}}</view>
                  <!-- <view class="price-box">
                    <text class="currency" wx:if="{{ item.saleMethod === 10 }}">¥</text>
                    <text class="price">{{item.saleMethod === 10 ? item.coursePrice:'免费'}}</text>
                  </view> -->
                </view>
                <view class="right">
                  <view class="rating">
                    <text class="score">评分：</text>
                    <text class="value">{{item.avgScore>0?item.avgScore:'暂无评'}}分</text>
                  </view>
                  <view class="student-count">{{item.addCount}}人购买</view>
                  <!-- <view class="right-bottom" catch:tap="showReason" data-course-id="{{item.id}}" wx:if="{{!add}}">
                    查看原因
                  </view>
                  <view class="right-bottom online" catch:tap="showReason" data-course-id="{{item.id}}" wx:if="{{!add}}">
                    移除
                  </view> -->
                </view>
              </view>
              <view class="bottom">
                <view class="price-box">
                  <text class="currency" wx:if="{{ item.saleMethod === 10 }}">¥</text>
                  <text class="price">{{item.saleMethod === 10 ? item.coursePrice:'免费'}}</text>
                </view>
                <view class="btn-box">

                  <view wx:if="{{ item.auditStatus === 20 && item.loadStatus === 0 && item.shelfReason }}" catch:tap="showCourseReason" class="right-bottom gray" catch:tap="showReason" data-course-id="{{item.id}}" data-course-type="{{item.courseType}}" data-status="{{item.loadStatus}}" data-index="{{index}}" data-shelf-reason="{{item.shelfReason}}">
                    查看原因
                  </view>
                  <view wx:if="{{ item.auditStatus === 20 && item.loadStatus === 0 && !item.shelfReason }}" class="right-bottom offline" catch:tap="showReason" data-course-id="{{item.id}}" data-course-type="{{item.courseType}}" data-status="{{item.loadStatus}}" data-index="{{index}}" data-shelf-reason="{{item.shelfReason}}">
                    上架
                  </view>
                  <view wx:if="{{ item.auditStatus === 20 && item.loadStatus === 1}}" class="right-bottom online" catch:tap="showReason" data-course-id="{{item.id}}" data-course-type="{{item.courseType}}" data-status="{{item.loadStatus}}" data-index="{{index}}" data-shelf-reason="{{item.shelfReason}}">
                    下架
                  </view>

                  <view wx:if="{{ item.auditStatus === 30 }}" class="right-bottom gray" catch:tap="reviewFailedReason" data-course-id="{{item.id}}" data-course-type="{{item.courseType}}" data-course-type="{{item.courseType}}" data-shelf-reason="{{item.auditFailReason}}">
                    查看原因
                  </view>

                  <view class="right-bottom online" catch:tap="removeCourse" data-course-id="{{item.id}}">
                    移除
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- <view class="finishedText">
        <text wx:if="{{!finished}}">加载中...</text>
        <text wx:else>暂无更多</text>
      </view> -->
    </view>
    <view class="no-data" wx:else>
      <custom-empty-new padding='66rpx' describe="暂无课程"></custom-empty-new>
    </view>
  </scroll-view>
  <view slot='bottom' class="bottm-button-bar">
    <van-button bind:tap="onAddColumn" color="#0057FF">新增课程</van-button>
  </view>
</scroll-box>