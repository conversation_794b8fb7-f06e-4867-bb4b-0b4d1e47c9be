<!--pages/module-mentorShop/add-column/add-column.wxml-->
<view class="add-column">
  <!-- 专栏 -->
  <view class="section">
    <view class="detail-section">
      <view class="section-title">专栏名称</view>
      <!-- <text>*</text> -->
      <view class="section-input">
        <van-field value="{{form.columnName}}" bind:change="handleColumnNameChange" maxlength="30" rows="1" autosize="{{autosize}}" type="textarea" placeholder="请输入专栏名称" show-word-limit />
      </view>

    </view>

    <van-cell is-link title="专栏详情" link-type="navigateTo" bind:tap="onEditDetail" />

    <view class="cover-section">
      <view class="section-title">添加封面</view>
      <!-- <text>*</text> -->
      <view class="upload-tip">(按照16:9的比例显示，图片大小小于5M，仅支持JPG、PNG格式)</view>
      <view class="upload-area">
        <view wx:if="{{!form.columnCoverUrl}}" class="upload-btn" bindtap="chooseImage">
          <van-icon name="plus" color='#9EA3AC' size='30px' />
        </view>
        <view class="upload-image" wx:else>
           <van-icon name="cross" color='#fff' size='15px' bindtap="removeImage" />
          <image src="{{form.columnCoverUrl}}" mode="" />
        </view>
      </view>
    </view>
    <van-popup show="{{ showCrop }}" bind:close="cancelShowCrop" custom-style="height: 100%; width: 100%">
      <crop imgFile="{{imgFile}}" upLoadFlag="{{true}}" bind:onCropperDone="handleCropperDone" bind:onCancel="cancelShowCrop"></crop>
    </van-popup>
  </view>

  <!-- 内容更新排序 -->
  <view class="sort-section">
    <view class="section-title">内容更新排序</view>
    <van-radio-group value="{{form.updateSortRule}}" bind:change="handleRadioChange" direction="horizontal">
      <van-radio name="10" checked-color="#0057FF">最新添加的在前</van-radio>
      <van-radio name="20" checked-color="#0057FF">最新添加的在后</van-radio>
    </van-radio-group>
  </view>

  <!-- 售卖方式 -->
  <view class="sale-section">
    <view class="sale-header">
      <text>售卖</text>
     <!-- <van-switch size="24px" bind:change="handleSwitchChange" checked="{{form.checked}}" active-color="#0057FF" />-->
    </view>
    <view class="sale-type" bindtap="showActionSheet">
      <text>售卖方式</text>
      <view class="price-tag">
        <text>{{currentSaleType}}</text>
        <van-icon name="arrow" color="#0057FF" />
      </view>
    </view>

    <view class="sale-price" wx:if="{{currentSaleType==='付费'}}">
      <view class="section-title">商品售价</view>
      <!-- <text>*</text> -->
      <!-- <van-field value="{{form.columnPrice}}" bind:change="handlePriceChange" type="number" placeholder="请输入售价(0.01-100000元)" right-icon="gold-coin-o" /> -->
      <van-field value="{{form.columnPrice}}" bind:input="handlePriceChange" bind:blur="handlePriceBlur" type="textarea" placeholder="请输入售价(0.01-100000元)" right-icon="gold-coin-o" />
    </view>
  </view>
  <view class="bottm-button-bar">
    <van-button bind:tap="onAddColumn" color="#0057FF">保存</van-button>
  </view>
</view>