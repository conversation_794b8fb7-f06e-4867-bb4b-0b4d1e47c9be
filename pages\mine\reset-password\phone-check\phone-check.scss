page{
  height: 100%;
}
.reset-password-check{
  height: 100%;
  display: flex;
  flex-direction: column;
  .reset-content{
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    padding: 60rpx;
    .login-form{
      width: 100%;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      .form-item{
        background: #F7F7F7;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        width: 100%;
      }
      .form-phone{
        .label{
          padding: 0 30rpx;
          font-weight: 500;
          font-size: 28rpx;
          color: #000000;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
          border-right: 2rpx solid #D7D7D7;
        }
        .input{
          flex: 1;
          padding-left: 30rpx;
          height: 100%;
          .van-cell{
            padding: 0;
            height: 100%;
            background: #F7F7F7;
          }
          .van-field__body{
            height: 100%;
          }
        }
      }
      .form-code{
        margin-top: 28rpx;
        display: flex;
        align-items: center;
        .input{
          flex: 1;
          padding-left: 30rpx;
          height: 100%;
          .van-cell{
            padding: 0;
            height: 100%;
            background: #F7F7F7;
          }
          .van-field__body{
            height: 100%;
          }
        }
        .get-code{
          width: 220rpx;
          text-align: center;
          // padding: 0 30rpx 0 40rpx;
          border-left: 2rpx solid #D7D7D7;
          font-weight: 500;
          font-size: 28rpx;
          color: #165DFF;
          line-height: 33rpx;
          font-style: normal;
          text-transform: none;
        }
        .get-code-disabled{
          color: #aaa;
        }
      }
    }
  }
  .reset-footer{
    box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0,0,0,0.05);
    background: #FFF;
    height: 160rpx;
    padding: 24rpx 36rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    van-button{
      overflow: hidden;
      height: 100%;
      flex: 1;
      margin-right: 40rpx;
      button{
        font-size: 36rpx;
        // line-height: 48rpx;
        border-radius: 120rpx;
        width: 100%;
        height: 100%;
      }
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .default-btn{
      border: 1px solid #0057FF;
      background: #fff;
      color: #0057FF;
    }
    .info-btn{
      border: 1px solid #0057FF;
      background: #0057FF;
      color: rgb(255, 255, 255);
    }
  }
}