<!--pages/module-mentorShop/all-column/all-column.wxml-->
<scroll-view scroll-y enhanced show-scrollbar="{{false}}" bounces="{{true}}" bindscrolltolower="loadMore" class="video-section">
  <view class="filter" bindtap="onFilterTap">
    {{filterText}}
    <van-icon name="{{showFilter ? 'arrow-up' :'arrow-down'}}" />
    <view class="filter-dropdown {{showFilter ? 'show' : ''}}">
      <view class="filter-item {{loadStatus === '' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="">全部</view>
      <view class="filter-item {{loadStatus === '1' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="1">已上架</view>
      <view class="filter-item {{loadStatus === '0' ? 'active' : ''}}" catchtap="onFilterSelect" data-type="0">已下架</view>
    </view>
  </view>
  <view class="course-list">
    <van-checkbox-group value="{{ result }}" bind:change="onChange" wx:if="{{list.length > 0}}">
      <view class="item-box" wx:for="{{list}}" wx:key="id">
        <van-checkbox wx:if="{{add}}" name="{{item.id}}"></van-checkbox>
        <view bindtap="onCourseClick" data-id="{{item.id}}" class="course-item">
          <view class="image-wrapper">
            <view class="shadow-top"></view>
            <view class="shadow-top-second"></view>
            <image class="course-image" src="{{item.columnCoverUrl}}" mode="aspectFill" />
            <view class="course-stats">
              <view class="left-stats">
                <image class="icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/file-icon.png" />
                <text>{{item.courseCount}}</text>
              </view>
              <text class="student-count" wx:if="{{item.addCount>0}}">{{item.addCount}}人购买</text>
            </view>
          </view>
          <view class="course-info">
            <view class="course-meta">
              <view class="course-title">{{item.columnName}}</view>
            </view>
            <view class="course-bottom">
              <view class="price-box">
                <text class="currency" wx:if="{{ item.saleMethod === 10 }}">¥</text>
                <text class="price">{{item.saleMethod === 10 ?item.columnPrice:'免费'}}</text>
              </view>
              <view class="edit">
                <view catchtap="showReason" data-index="{{index}}" data-id="{{item.id}}" data-status="{{item.loadStatus}}" class="{{item.loadStatus === 0 ? 'offline' : item.loadStatus === 1 ? 'online' :''}}"> {{item.loadStatus === 0 ? '上架' : '下架'}}</view>
                <view catchtap="onEdit" data-id="{{item.id}}">编辑</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="finishedText">
        <text wx:if="{{!finished}}">加载中...</text>
        <text wx:else>暂无更多</text>
      </view>
    </van-checkbox-group>
    <view class="empty" wx:else>
      <custom-empty-new  padding='66rpx'/>
    </view>
  </view>

  <view class="bottm-button-bar">
    <!-- {{add ? 'vanshow-button':''}} -->
    <van-button color="#0057FF" bind:tap="onAddColumn">新增专栏</van-button>
  </view>

  <van-action-sheet custom-class='van-action-sheet' show="{{ show }}" z-index='2' title="已选择专栏" bind:close="onClose">
    <view wx:if="{{selectedItems && selectedItems.length > 0}}" class="selectedItem-list course-list">
      <view class="item-box" wx:for="{{selectedItems}}" wx:key="id">
        <view data-id="{{item.id}}" class="course-item">
          <view class="image-wrapper">
            <view class="shadow-top"></view>
            <view class="shadow-top-second"></view>
            <image class="course-image" src="{{item.columnCoverUrl}}" mode="aspectFill" />
            <!-- <view class="course-stats">
              <view class="left-stats">
                <image class="icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/file-icon.png" />
                <text>188</text>
              </view>
              <text class="student-count">{{item.studentCount}}人购买</text>
            </view> -->
          </view>
          <view class="course-info">
            <view class="course-meta">
              <view class="course-title">{{item.columnName}}</view>
            </view>
            <view class="course-number">
              共{{item.courseCount}}课时
              <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/file-icon.png" mode="" />
            </view>
            <view class="course-bottom">
              <view class="price-box">
                <text class="currency" wx:if="{{item.saleMethod === 10 }}">¥</text>
                <text class="price">{{item.saleMethod === 10 ?item.columnPrice:'免费'}}</text>
              </view>
              <view class="number" wx:if="{{item.addCount>0}}">{{item.addCount}}人购买</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view wx:else class="empty">
      <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/book-empty.png" />
      <text>暂无已选择专栏</text>
    </view>

  </van-action-sheet>

  <view class="bottom-bar" wx:if="{{add && list.length > 0}}">
    <view class="association">
      <view bindtap="onToggleSheet">
        <text class="selected-count">已选择：{{result.length}}</text>
        <van-icon name="{{show?'arrow-up':'arrow-down'}}" color='#0057ff' />
      </view>
      <view class="confirm-btn" bindtap="handleConfirm">确认</view>
    </view>
  </view>


  <view class="float-buttons" wx:if="{{add}}">
    <view class="float-button" bindtap="onAddColumn">
      <van-icon color='#fff' name="plus" />
    </view>
  </view>
</scroll-view>