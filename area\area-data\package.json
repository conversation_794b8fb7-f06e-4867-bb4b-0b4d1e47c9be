{"_from": "@vant/area-data@^2.0.0", "_id": "@vant/area-data@2.0.0", "_inBundle": false, "_integrity": "sha512-zgP4AA8z09S9QTNgVCCHo9cHjcybrv22RJDYPjuCkecn4SB98T5EoPQh2TwqbQXmUhbaOGgiZGy3OUaUxnY7qg==", "_location": "/@vant/area-data", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@vant/area-data@^2.0.0", "name": "@vant/area-data", "escapedName": "@vant%2farea-data", "scope": "@vant", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/@vant/area-data/-/area-data-2.0.0.tgz", "_shasum": "9be3b812245d9edf5fc1116c58ccaee826c234d4", "_spec": "@vant/area-data@^2.0.0", "_where": "D:\\跃鱼\\small-program-student", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/vant-ui/vant/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Vant 省市区数据", "devDependencies": {"esbuild": "^0.24.0", "rimraf": "^6.0.1", "typescript": "^5.6.2"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.mjs", "require": "./dist/index.cjs.js"}, "./package.json": "./package.json"}, "files": ["dist"], "homepage": "https://github.com/vant-ui/vant#readme", "license": "MIT", "main": "dist/index.cjs.js", "module": "dist/index.esm.mjs", "name": "@vant/area-data", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "git", "url": "git+https://github.com/vant-ui/vant.git", "directory": "packages/vant-area-data"}, "scripts": {"build": "pnpm clean && pnpm build:bundle && pnpm build:types", "build:bundle": "node ./build.js", "build:types": "tsc -p ./tsconfig.json --emitDeclarationOnly", "clean": "rimraf ./dist", "dev": "node ./build.js -w", "release": "vant-cli release"}, "sideEffects": false, "types": "dist/index.d.ts", "version": "2.0.0"}