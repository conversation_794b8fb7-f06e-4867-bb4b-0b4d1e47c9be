<!--pages/module-mentorShop/dynamics-detial/dynamics-detial.wxml-->
<view class="dynamics-detial" bindtap="handlePageTap">
  <scroll-view scroll-y class="dynamics-box" bindscrolltolower="onScrollToLower" enhanced show-scrollbar="{{false}}">
    <dynamics dynamicsList="{{dynamicsList}}" userRole="teacher" isDelete="{{true}}" isExpand="{{true}}" mentorDynamics="{{true}}" totalFlag="{{true}}" totalReplies ="{{totalReplies}}"bind:showCommentInput="handleShowCommentInput" padding='0' />

    <view class="comment-box">
      <!-- 评论列表 -->
      <view class="comment-list">
        <view wx:if="{{commentList.length > 0}}">
          <view class="title">共{{totalReplies}}条评论</view>
          <view>
            <view class="comment-item" wx:for="{{commentList}}" wx:key="id">
              <!-- 评论主体 -->
              <view class="comment-main">
                <view class="header">
                  <view class="left">
                    <image class="avatar" src="{{item.commenter.avatarPath}}" mode="aspectFill" />
                    <view>
                      <view class="name">{{item.commenter.userName}}</view>
                      <view class="time">{{item.createTime}}</view>
                    </view>
                  </view>
                  <view class="time-reply" catchtap="handleReply" data-name="{{item.commenter.userName}}" data-commentid="{{item.id}}">
                    <image class="icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/comment.png"></image>
                    <view>
                      回复
                    </view>
                  </view>
                </view>

                <view class="content">{{item.commentContent}}</view>
              </view>

              <!-- 回复列表 -->
              <view class="replies-container" wx:if="{{item.replies.length > 0}}">
                <view class="reply-list">
                  <view class="reply-item" wx:for="{{item.replies}}" wx:key="id" wx:for-item="reply">
                    <view class="header">
                      <view class="left">
                        <image class="avatar" src="{{reply.commenter.avatarPath}}" mode="aspectFill" />
                        <view>
                          <view class="name">{{reply.commenter.userName}}</view>
                          <view class="time">{{reply.createTime}}</view>
                        </view>
                      </view>
                      <view class="time-reply" catchtap="handleReply" data-name="{{reply.commenter.userName}}" data-commentid="{{reply.id}}">
                        <image class="icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/comment.png"></image>
                        <view>
                          回复
                        </view>
                      </view>
                    </view>
                    <view class="reply-content"><text class="reply-tips" wx:if="{{reply.commentedUser.userName}}">回复</text><text class="name" wx:if="{{reply.commentedUser.userName}}">{{reply.commentedUser.userName}}：</text>{{reply.commentContent}}</view>
                  </view>
                </view>

                <!-- 展开回复按钮 -->
                <block wx:if="{{item.replies.length > 0 && item.subCommentCount > 1}}">
                  <view class="expand-replies" bindtap="expandReplies" data-commentid="{{item.id}}">
                    <block wx:if="{{!expandedComments[item.id]}}">
                      <!-- 未展开时 -->
                      展开更多回复
                    </block>
                    <block wx:else>
                      <!-- 已展开但未加载完 -->
                      <block wx:if="{{item.replies.length < item.totalReplies}}">
                        展开更多回复
                      </block>
                      <!-- 已加载全部 -->
                      <block wx:else>
                        收起回复
                      </block>
                    </block>
                  </view>
                </block>
              </view>
            </view>
          </view>
          <view class="finishedText">
            <text wx:if="{{loading}}">加载中...</text>
            <text wx:elif="{{finished}}">暂无更多</text>
          </view>
        </view>
      </view>


      <view class="comment-list" wx:if="{{!loading && commentList.length === 0}}" describe="暂无动态">
        <empty text="暂无评论"></empty>
      </view>

      <!-- 回复输入框 -->
      <view class="reply-input-container" wx:if="{{showReplyInput}}" catchtap="handleInputTap">
        <view class="message-field">
          <van-field focus="{{ true }}" bind:change="messageChange" autosize="{{fieldAutoSize}}" value="{{ msgValue }}" placeholder="{{replyTo?replyTo:'请输入评论内容'}}" cursor-spacing="300" show-confirm-bar="{{ false }}" border="{{ false }}" type="textarea" maxlength="300" />
        </view>
        <image bind:tap="sendMessage" class="send-btn" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/IM/send.png" mode="aspectFill" />
      </view>
    </view>
  </scroll-view>
</view>