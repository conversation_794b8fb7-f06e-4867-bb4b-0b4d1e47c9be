/* pages/module-mentorShop/column-detail/column-detail.wxss */
page {
  height: 100%;
}
.filter {
  padding: 20rpx 30rpx;
  background: #FFF;
  color: #777777;
  font-size: 24rpx;
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(99,97,155,0.1);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  justify-content: flex-end;
  .van-icon {
    margin-left: 4rpx;
    transition: transform 0.3s ease;
  }

  .filter-dropdown {
    position: absolute;
    top: calc(100% + 20rpx);
    right: 0;
    width: 160rpx;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    z-index: 100;
    display: none;

    &.show {
      display: block;
    }

    .filter-item {
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 10rpx;
      font-size: 24rpx;
      color: #777777;
      margin: 13rpx;
      background-color: #f8f9fe;
      border-radius: 8rpx;
      border: 2rpx solid transparent;
      &.active {
        color: #0057ff;
        border: 2rpx solid #0057ff;
      }

      &:hover {
        background: #f5f5f5;
      }
    }
  }
  .filter-dropdown-top{
    // top: unset;
    // bottom: calc(100% + 20rpx);
  }
  
}



.scroll-box-content {
  padding: 30rpx 36rpx 0;
  box-sizing: border-box;
  height: 100%;
}

.video-section {
  padding: 0 30rpx 20rpx;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  .box {
    .course-item1 {
      .course-item {
        padding: 28rpx 0 28rpx;
        border-bottom: 2rpx solid #c4c4c4;
        display: flex;
        .image-wrapper {
          position: relative;
          width: 244rpx;
          height: 136rpx;

          .course-image {
            width: 100%;
            height: 100%;
            border-radius: 8rpx;
          }

          .course-mask {
            border-radius: 8rpx;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            color: #fff;
            font-weight: bolder;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28rpx;
          }
        }

        .course-info {
          margin-left: 16rpx;
          flex: 1;
          display: flex;
          // align-items: flex-start;
          flex-direction: column;
          justify-content: space-between;
          .top{
            display: flex;
            justify-content: space-between;
            .course-meta {
              // margin-bottom: 10rpx;
              flex: 1;
              flex-shrink: 0;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              .course-title {
                color: #333333;
                font-size: 26rpx;
                font-weight: bold;
                line-height: 30rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                word-break: break-all;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
              }
              .course-title-select {
                // width: 130rpx;
              }
            }
      
            .right {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              flex-shrink: 0;
              margin-left: 10rpx;
              min-width: 120rpx; 
              min-height: 100%;
              justify-content: space-between;
            }
      
            .rating {
              display: flex;
              align-items: center;
              // flex: 1;
              flex-shrink: 0;
              text-align: right;
      
              .score,
              .value {
                color: #0057ff;
                font-size: 20rpx;
              }
      
              .score {
                color: #9ea3ac;
              }
            }
            .duration{
              font-weight: 500;
              font-size: 24rpx;
              color: #9EA3AC;
              text-align: left;
              font-style: normal;
              text-transform: none;
              margin-top: 10rpx;
              line-height: 34rpx;
            }
            .student-count {
              font-size: 24rpx;
              color: #9ea3ac;
              line-height: 28rpx;
              // margin-top: 14rpx;
            }
      
            .student-count {
              margin-top: 26rpx;
              text-align: right;
            }
          }
          .bottom{
            display: flex;
            margin-top: 14rpx;
            align-items: center;
            justify-content: space-between;
            .price-box {
              // margin-top: 14rpx;
              display: flex;
              align-items: baseline;
              font-weight: bold;
              color: #ff0019;
              line-height: 40rpx;
              .currency {
                font-size: 24rpx;
                margin-right: 2rpx;
              }
      
              .price {
                font-size: 36rpx;
              }
            }
            .btn-box{
              display: flex;
              align-items: center;
              .right-bottom {
                // margin-top: 14rpx;
                color: #ffffff;
                font-size: 24rpx;
                font-weight: 500;
                background-color: #979797;
                border-radius: 8rpx;
                text-align: center;
                padding: 2rpx 16rpx;
                box-sizing: border-box;
                // width: 100%;
                // max-width: 120rpx; /* 限制按钮最大宽度 */
                margin-right: 12rpx;
                &:nth-last-child(1) {
                  margin-right: 0;
                }
                &.online {
                  color: #fff;
                  background-color: #ff0019;
                }
        
                &.offline {
                  color: #fff;
                  background-color: #0057ff;
                }
        
                &.gray {
                  background-color: #979797;
                }
              }  
            }
          }
        }
      }

      &:last-child {
        .course-item {
          border-bottom: none;
        }
      }
    }
  }
}

.bottm-button-bar {
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // right: 0;
  padding: 24rpx 40rpx 30rpx;
  box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
  background-color: #fff;

  button {
    width: 100%;
    background: #0057ff;
    border: none;
    color: rgb(255, 255, 255);
    font-size: 32rpx;
    line-height: 32rpx;
    font-weight: 400;
    height: auto;
    padding: 30rpx 0;
    border-radius: 46rpx;
  }
}
