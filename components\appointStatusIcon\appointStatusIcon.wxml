<view>
  <view class="appoint-type to-communicate" wx:if="{{appointStatus === 10}}">
    待沟通
  </view>
  <view class="appoint-type ongoing" wx:if="{{appointStatus === 20}}">
    进行中
  </view>
  <view class="appoint-type wait-report" wx:if="{{appointStatus === 30 && reportStatus === 10}}">
    报告待发送
  </view>
  <view class="appoint-type wait-comment" wx:if="{{appointStatus === 30 && reportStatus === 20}}">
    待评价
  </view>
  <view class="appoint-type cancel" wx:if="{{appointStatus === 40}}">
    已取消
  </view>
  <view class="appoint-type wait-report" wx:if="{{appointStatus === 50 && reportStatus === 10}}">
    报告待发送
  </view>
  <view class="appoint-type finished" wx:if="{{appointStatus === 50 && reportStatus === 20}}">
    已完成
  </view>
  <view class="appoint-type cancel" wx:if="{{appointStatus === 60}}">
    已错过
  </view>
</view>