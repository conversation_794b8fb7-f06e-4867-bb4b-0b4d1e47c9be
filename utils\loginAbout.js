import { loginAfterNavigate } from '~/utils/loginAfterNavigate'
const getTenantInfoData = async () => {
  try {
    const app = getApp();
    await app.getTenantInfo();
  } catch (error) {
    console.error('获取租户信息失败:', error);
  }
};
// 这个方法是用与导师身份相关的逻辑，游客身份的登录自己页面里面写
export const loginAfter = (token) => {
  wx.showToast({
    title: '登录成功',
    duration: 1500,
    icon: 'none'
  })
  setTimeout(() => {
    wx.setStorageSync('token', token)
    // tourist && mentor------游客与导师两种身份
    wx.setStorageSync('identity', 'mentor')
    getTenantInfoData()
    // 校验登录跳转的目标页面
    loginAfterNavigate()
  }, 1000)
}

export const touristloginAfter = (token) => {
  // wx.showToast({
  //   title: '登录成功',
  //   duration: 1500,
  //   icon: 'none'
  // })
  wx.setStorageSync('token', token)
  // tourist && mentor------游客与导师两种身份
  wx.setStorageSync('identity', 'tourist')
  loginAfterNavigate()
}

/**
 * 
 * @param {*} controlsCheck 校验是否需要登录的，如果是通过点击按钮之类的校验，那么传入的值为true，如果是页面的校验，那么就传false，或者不传
 */
  // 这个方法暂时无用
export const loginCheck = (callBack, controlsCheck = true) => {
  if (wx.getStorageSync("token")) {
    callBack && callBack()
  } else {
    // 保存当前页面路径
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const url = `/${currentPage.route}`
    const options = currentPage.options
    
    // 将参数按照键名排序并处理
    let queryString = ''
    if (options && Object.keys(options).length > 0) {
      const sortedKeys = Object.keys(options).sort()
      queryString = sortedKeys
        .filter(key => options[key] || options[key] === 0)
        .map(key => `${key}=${options[key]}`)
        .join('&')
    }
    
    // 完整的返回路径
    const returnPath = queryString ? `${url}?${queryString}` : url
    wx.setStorageSync('returnPath', returnPath)

    if (controlsCheck) {
      wx.navigateTo({
        url: '/pages/mentor-apply/mentor-apply-welcome/mentor-apply-welcome',
      })
    } else {
      wx.navigateTo({
        url: '/pages/mentor-apply/mentor-apply-welcome/mentor-apply-welcome',
      })
    }
  }
}

export const logout  = (url = '/pages/mentor-apply/mentor-apply-welcome/mentor-apply-welcome') => {
  const app = getApp();
  if (app.globalData.websocket) {
    app.globalData.websocket.close();
    app.globalData.websocket = null;
  }
  wx.clearStorageSync()
  wx.reLaunch({
    url: url,
  })
}