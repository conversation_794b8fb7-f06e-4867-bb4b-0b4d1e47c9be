/* pages/login-phone/login-phone.wxss */
.login-phone-bg{
  width: 100%;
  height: 360rpx;
  .bg{
    width: 100%;
    height: 100%;
  }
}
.phone-login-title{
  width: 100%;
  margin-top: -200rpx;
  font-weight: bold;
  font-size: 48rpx;
  color: #303030;
  line-height: 56rpx;
  text-align: center;
  font-style: normal;
}
.login-form{
  width: 622rpx;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40rpx;
  .form-item{
    background: #F7F7F7;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    width: 100%;
  }
  .form-phone{
    margin-top: 28rpx;
    .label{
      padding: 0 30rpx;
      font-weight: 500;
      font-size: 28rpx;
      color: #000000;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
      border-right: 2rpx solid #D7D7D7;
    }
    .input{
      flex: 1;
      padding-left: 30rpx;
      height: 100%;
      .van-cell{
        padding: 0;
        height: 100%;
        background: #F7F7F7;
      }
      .van-field__body{
        height: 100%;
      }
    }
  }
  .form-code{
    margin-top: 28rpx;
    display: flex;
    align-items: center;
    .input{
      flex: 1;
      padding-left: 30rpx;
      height: 100%;
      .van-cell{
        padding: 0;
        height: 100%;
        background: #F7F7F7;
      }
      .van-field__body{
        height: 100%;
      }
    }
    .get-code{
      width: 220rpx;
      text-align: center;
      // padding: 0 30rpx 0 40rpx;
      border-left: 2rpx solid #D7D7D7;
      font-weight: 500;
      font-size: 28rpx;
      color: #165DFF;
      line-height: 33rpx;
      font-style: normal;
      text-transform: none;
    }
    .get-code-disabled{
      color: #aaa;
    }
  }
  .agreement{
    padding-left: 8rpx;
    width: 100%;
    margin-top: 96rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-weight: 400;
    font-size: 26rpx;
    color: #999999;
    line-height: 30rpx;
    text-align: left;
    font-style: normal;
    .van-checkbox__label{
      padding-left: 12rpx;
    }
    // .van-checkbox{
    //   display: flex;
    //   align-items: center;
    //   .label-class{
    //     margin-left: 12rpx;
    //   }
    //   .van-checkbox__icon{
    //     width: 28rpx;
    //     height: 28rpx;
    //     font-size: 24rpx;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //   }
    //   .van-icon{
    //     line-height: 34rpx !important;
    //     height: 34rpx !important;
    //     &::before{
    //       height: 36rpx;
    //     }
    //   }
    // }
    .text-value{
      // margin-left: 12rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #979797;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    text{
      color: #0057FF;
    }
  }
  .login-btn{
    margin-top: 16rpx;
    width: 100%;
    height: 80rpx;
    background: #165DFF;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 32rpx;
    color: #FFFFFF;
    line-height: 32rpx;
    text-align: center;
    font-style: normal;
    font-weight: bold;
    font-size: 30rpx;
    color: #FFFFFF;
    line-height: 35rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .account-submit{
    margin-top: 16rpx;
    font-weight: 500;
    font-size: 26rpx;
    color: #8D8D8D;
    line-height: 36rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    text-align: center;
  }
}