// pages/module-mentorShop/components/column/column.js
import { removeCourseFromColumn } from '~/api/mentorShop'
Component({
  properties: {
    columnData: {
      type: Object,
      value: {},
      observer: function (newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.setData({
            columnInfo: newVal
          })
        }
      }
    },
    columnsId: {
      type: String, // 专栏id
      value: '',
      observer: function (newVal) {
        this.setData({
          columnsId: newVal
        })
      }
    },
    select: {
      type: Boolean,
      value: false,
      observer: function (newVal) {
        this.setData({
          selected: newVal
        })
      }
    },
    add: {
      type: Boolean,
      value: false,
      observer: function (newVal) {
        this.setData({
          add: newVal
        })
      }
    },
    courseRemove: {
      type: Boolean,
      value: false
    },
  },

  data: {
    columnInfo: {}, // 用于存储父组件传递的对象数据
    selected: false,
    add: false,
    courseRemove:false
  },

  methods: {
    // // 组件的方法放在这里
    showReason(e) {
      if(this.properties.courseRemove){
        const id = e.currentTarget.dataset.courseId;
        this.triggerEvent('handleCourseRemove', {
          courseId:id
        });
        return;
      }
      const coursesId = this.properties.columnInfo.id
      wx.showModal({
        title: '移除',
        content: '移除后，已购用户仍可以学习，未购用户将无法查看和购买，确认移除？',
        success: (res) => {
          if (res.confirm) {
            removeCourseFromColumn({
              columnsId: this.properties.columnsId,
              coursesId: coursesId
            }).then(res => {
              wx.showToast({
                title: '移除成功',
                icon: 'none'
              })
              // 触发自定义事件通知父组件更新
              this.triggerEvent('removeCourse', {
                coursesId: coursesId
              })
            })
          }
        }
      })
    }
  }
})