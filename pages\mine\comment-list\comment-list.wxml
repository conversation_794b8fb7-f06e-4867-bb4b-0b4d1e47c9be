<view class="comment-list">
  <scroll-view class="scrollarea" scroll-y type="list" bindscrolltolower="loadMore">
    <view class="comment-box" wx:if="{{commentList && commentList.length > 0}}">
      <view class="comment-item" wx:for="{{commentList}}" wx:key="{{index}}">
        <view class="card-box">
          <view class="header">
            <view class="comment-user">
              <image src="{{item.studentAvatarPath}}" mode="aspectFill" />
              <text>{{item.studentName}}</text>
            </view>
            <view class="comment-operation" wx:if="{{item.status === 1}}" bind:tap="upChange" data-key="{{index}}">
              <text>已隐藏</text>
              <text class="circle"></text>
            </view>
            <view class="comment-operation comment-operation-down" wx:else bind:tap="upChange" data-key="{{index}}">
              <text class="circle"></text>
              <text>隐藏评价</text>
            </view>
          </view>
          <view class="content">{{item.content}}</view>
          <view class="topic">
            <view class="textoverflow">#{{item.topicTitle}}</view>
          </view>
          <view class="date">
            <view>{{item.commentDate}}</view>
            <view class="like-number">
              <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/like-plain.png"/>
              <text>{{item.thumbUpNum || 0}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="empty" wx:else>
      <custom-empty-new></custom-empty-new>
    </view>
  </scroll-view>
</view>
