page{
  height: 100%;
}
.appoint-detail{
  // height: 100%;
  background-color: rgb(248, 248, 248);
  // padding: 36rpx 36rpx 24rpx;
  // overflow: auto;
  // display: flex;
  // flex-direction: column;

  height: 100%;
  display: flex;
  flex-direction: column;
  .appoint-detail-content{
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    overflow: auto;
    padding: 36rpx 36rpx 24rpx;
    .appoint-status{
      padding: 0 24rpx;
      display: flex;
      align-items: center;
      image{
        width: 90rpx;
        height: 90rpx;
        border-radius: 50%;
        margin-right: 12rpx;
      }
      .status-text{
        .status-text-title{
          color: rgb(51, 51, 51);
          font-size: 34rpx;
          font-weight: 600;
          line-height: 48rpx;
        }
        .status-text-subtitle{
          margin-top: 10rpx;
          color: rgb(119, 119, 119);
          font-size: 24rpx;
          font-weight: 400;
          line-height: 34rpx;
        }
      }
    }
    .card-box{
      margin-top: 28rpx;
      padding: 30rpx 28rpx;
      border-radius: 12rpx;
      background: #FFF;
      .header{
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        color: rgb(51, 51, 51);
        font-size: 30rpx;
        font-weight: 600;
        line-height: 42rpx;
        display: flex;
        align-items: center;
        position: relative;
        padding-bottom: 24rpx;
        .title{
          flex: 1;
        }
        .header-right{
          padding: 4rpx 14rpx;
          background: rgba(107, 130, 246, 0.1);
          border: 2rpx solid #0057FF;
          border-radius: 4rpx;
          color: #0057FF;
          font-size: 24rpx;
          font-weight: 400;
          line-height: 34rpx;
        }
        .no-sign-in{
          background: rgba(121, 121, 121, 0.1);
          border-color: rgb(121, 121, 121);
          color: rgb(121, 121, 121);
        }
      }

      .sign-in-number{
        margin-top: 8rpx;
        display: flex;
        .sign-number-item{
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100rpx;
          height: 100rpx;
          background: rgb(238, 240, 242);
          border-radius: 24rpx;
          color: rgb(51, 51, 51);
          font-size: 40rpx;
          font-weight: 400;
          line-height: 56rpx;
          margin-right: 28rpx;
          &:nth-last-child(1) {
            margin-right: 0;
          }
        }
      }
      .sign-in-date{
        padding: 4rpx 0;
        margin-top: 28rpx;
        color: rgb(51, 51, 51);
        font-size: 30rpx;
        font-weight: 500;
        line-height: 36rpx;
        display: flex;
        align-items: center;
        image{
          width: 36rpx;
          height: 36rpx;
          margin-right: 4rpx;
          object-fit: cover;
        }
      }

      .appoint-base{
        .appoint-base-item{
          margin-top: 12rpx;
          font-size: 26rpx;
          font-weight: 500;
          line-height: 42rpx;
          display: flex;
          .appoint-base-item-label{
            color: rgb(119, 119, 119);
            // margin-right: 28rpx;
            width: 132rpx;
            white-space: nowrap;
            flex-shrink: 0;
          }
          .appoint-base-item-value{
            flex: 1;
            // margin-left: 12rpx;
            color: rgb(51, 51, 51);
          }
          &:nth-child(1) {
            margin-top: 0;
          }
        }
      }

      .appoint-detail-file{
        .question-directive{
          word-break: break-all;
          font-size: 26rpx;
          font-weight: 400;
          line-height: 42rpx;
          color: rgb(51, 51, 51);
          // word-break: break-word;
        }
        .file-type{
          margin-top: 20rpx;
          color: #333333;
          font-size: 28rpx;
          font-weight: 600;
          line-height: 40rpx;
          display: flex;
          align-items: center;
          image{
            width: 40rpx;
            height: 40rpx;
            margin-right: 20rpx;
          }
        }
        .image-type-list{
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          .image-type{
            margin-top: 20rpx;
            width: 300rpx;
            height: 200rpx;
            border-radius: 8rpx;
            overflow: hidden;
            image{
              width: 100%;
              height: 100%;
            }
          }
        }
        .appoint-describe-supplementary{
          margin-top: 32rpx;
          .appoint-describe-supplementary-title{
            color: rgb(51, 51, 51);
            font-weight: 700;
          }
          .appoint-describe-supplementary-item{
            margin-top: 20rpx;
            font-size: 26rpx;
            .appoint-describe-supplementary-label{
              font-weight: 500;
            }
            .appoint-describe-supplementary-value{
              color: rgb(119, 119, 119);
              margin-top: 10rpx;
              word-break: break-all;
            }
          }
        }
      }

      .student-info{
        .student-info-item{
          display: flex;
          justify-content: space-between;
          margin-top: 24rpx;
          font-size: 26rpx;
          font-weight: 400;
          line-height: 42rpx;
          .student-info-item-label{
            color: rgb(119, 119, 119);
            width: 180rpx;
          }
          .student-info-item-value{
            flex: 1;
            // @include textoverflow;
            color: rgb(51, 51, 51);
            text-align: right;
          }
        }
      }
    }
  }
  .appoint-detail-footer{
    box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0,0,0,0.05);
    background: #FFF;
    height: 160rpx;
    padding: 24rpx 36rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    van-button{
      overflow: hidden;
      height: 100%;
      flex: 1;
      margin-right: 40rpx;
      button{
        font-size: 36rpx;
        // line-height: 48rpx;
        border-radius: 120rpx;
        width: 100%;
        height: 100%;
      }
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .default-btn{
      border: 1px solid #0057FF;
      background: #fff;
      color: #0057FF;
    }
    .info-btn{
      border: 1px solid #0057FF !important;
      background: #0057FF !important;
      color: rgb(255, 255, 255) !important;
    }
  }
}

.appoint-cancel-popup{
  .cancel-popup{
    position: relative;
    .close-icon{
      position: absolute;
      top: 36rpx;
      right: 28rpx;
      width: 52rpx;
      height: 52rpx;
      z-index: 99;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .header{
      padding-top: 36rpx;
      color: #272727;
      font-size: 38rpx;
      font-weight: 600;
      line-height: 52rpx;
      text-align: center;
      position: relative;
    }
    .content{
      padding: 30rpx 50rpx;
      // padding-bottom: 0;
      max-height: 800rpx;
      overflow: auto;
      // background: red;
      .reason-item{
        padding: 24rpx 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #333333;
        font-size: 28rpx;
        font-weight: 400;
        line-height: 40rpx;
        border-bottom: 2rpx solid #dfdfdf;
        image{
          width: 32rpx;
          height: 32rpx;
        }
        &:nth-last-child(1) {
          border: none;
          padding-bottom: 0;
        }
      }
      .reason-value{
        .custom-textarea{
          border: none;
          flex-direction: column;
          padding-top: 24rpx;
          padding-bottom: 30rpx;
          // padding: 0;
          margin-top: 20rpx;
          background: rgb(248, 249, 254);
          .van-field__control{
            
            
            border-radius: 12rpx;
            // padding: 26rpx;
          }
          .van-cell{
            padding: 0 !important;
          }
          &::after{
            display: none;
          }
        }
      }
    }
    .btn{
      padding: 50rpx;
      padding-top: 32rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      van-button{
        overflow: hidden;
        height: 100%;
        flex: 1;
        margin-right: 40rpx;
        button{
          font-size: 36rpx;
          // line-height: 48rpx;
          border-radius: 120rpx;
          width: 100%;
          height: 106rpx;
        }
        &:nth-last-child(1) {
          margin-right: 0;
        }
      }
      .default-btn{
        border: 1px solid #0057FF;
        background: #fff;
        color: #0057FF;
      }
      .info-btn{
        border: 1px solid #0057FF !important;
        background: #0057FF !important;
        color: rgb(255, 255, 255) !important;
      }
    }
  }
}
