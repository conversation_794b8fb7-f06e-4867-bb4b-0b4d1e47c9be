import eventBus from './eventBus'

// 创建消息监听器
const createMessageWatcher = (callback, filter) => {
  if (typeof callback !== 'function') {
    throw new Error('callback must be a function')
  }

  const messageHandler = (message) => {
    try {
      // 如果有filter函数，则根据filter判断是否执行callback
      if (filter && typeof filter === 'function') {
        if (filter(message)) {
          callback(message)
        }
      } else {
        callback(message)
      }
    } catch (error) {
      console.error('消息处理器执行错误:', error)
    }
  }

  // 返回清理函数和手动触发函数
  return {
    listen: () => eventBus.on('ws-message', messageHandler),
    clear: () => eventBus.off('ws-message', messageHandler),
    trigger: messageHandler // 用于手动触发消息处理（测试用）
  }
}

export default createMessageWatcher 