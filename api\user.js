// 引入 request 文件
import request from '../utils/request';

// 获取租户信息
export function getTenantInfo(data) {
  return request({
    url: `/user-front-service/tenant/${data.tenantId}`,
    method: 'get',
    data
  })
}

// 获取用户所有信息
export function getUserFullInfo(data) {
  return request({
    url: '/user-front-service/mentor/full',
    method: 'get',
    data
  })
}

// 获取游客的认证状态
export function getTouristAuthenticationInfo(data) {
  return request({
    url: '/user-front-service/mentor/authentication/info',
    method: 'get',
    data
  })
}

// 简历解析
// export function resumeAnalyze(data) {
//   return request({
//     url: '/recruit-front-service/resume/analyze/save/mentor/info',
//     method: 'post',
//     data
//   })
// }

// 获取游客是否已关注过公众号的接口
export function getUserLikeGzhStatus(data) {
  return request({
    url: '/user-front-service/wx/getUserLikeGzhStatus',
    method: 'get',
    data
  })
}

// 提交游客的审核信息
export function postTouristApplyInfo(data) {
  return request({
    url: '/user-front-service/mentor/apply/wxmp',
    method: 'post',
    data
  })
}

// 获取公众号二维码
export function getGZHQRcode(data) {
  return request({
    url: '/user-front-service/wx/generateQRCode/bsf/gzh',
    method: 'get',
    data
  })
}

// 轮询查看用户关注公众号的状态
export function pollingGZHStatus(data) {
  return request({
    url: `/user-front-service/wx/checkLoginStatus/${data.sceneStr}`,
    method: 'get',
    data
  })
}

// 获取用户所有信息
export function getUserBaseInfo(data) {
  return request({
    url: '/user-front-service/mentor/base',
    method: 'get',
    data
  })
}

// 获取用户身份列表
export function getIdList(data) {
  return request({
    url: '/common-service/dictionary/dataType',
    method: 'get',
    data
  })
}
// 更新用户头像
export function updateUserAvatar(data) {
  return request({
    url: '/user-front-service/user/base',
    method: 'put',
    data
  })
}
// 更新用户信息
export function updateUserBaseInfo(data) {
  return request({
    url: '/user-front-service/mentor/base',
    method: 'put',
    data
  })
}
// 获取用户擅长领域列表
export function getFieldList(data) {
  return request({
    url: '/user-front-service/skill-field',
    method: 'get',
    data
  })
}



// 获取导师教育经历列表
export function getMentorEducationList(data) {
  return request({
    url: '/user-front-service/mentor/education',
    method: 'get',
    data
  })
}

// 获取导师单个教育经历
export function getMentorEducation(data) {
  return request({
    url: `/user-front-service/mentor/education/${data.mentorEducationId}`,
    method: 'get',
    data
  })
}

// 添加导师教育经历
export function postMentorEducation(data) {
  return request({
    url: '/user-front-service/mentor/education',
    method: 'post',
    data
  })
}

// 更新导师教育经历
export function editMentorEducation(data) {
  return request({
    url: `/user-front-service/mentor/education/${data.mentorEducationId}`,
    method: 'put',
    data
  })
}

// 删除导师教育经历
export function deleteMentorEducation(data) {
  return request({
    url: `/user-front-service/mentor/education/${data.mentorEducationId}`,
    method: 'delete',
    data
  })
}






// 获取导师工作经历列表
export function getMentorWorkList(data) {
  return request({
    url: '/user-front-service/mentor/professional',
    method: 'get',
    data
  })
}

// 添加导师工作经历
export function postMentorWork(data) {
  return request({
    url: '/user-front-service/mentor/professional',
    method: 'post',
    data
  })
}

// 修改导师工作经历
export function editMentorWork(data) {
  return request({
    url: `/user-front-service/mentor/professional/${data.mentorProfessionalId}`,
    method: 'put',
    data
  })
}

// 获取导师单个工作经历
export function getMentorWork(data) {
  return request({
    url: `/user-front-service/mentor/professional/${data.mentorProfessionalId}`,
    method: 'get',
    data
  })
}

// 删除导师单个工作经历
export function deleteMentorWork(data) {
  return request({
    url: `/user-front-service/mentor/professional/${data.mentorProfessionalId}`,
    method: 'delete',
    data
  })
}







// 获取导师的话题列表
export function getMentorTopicList(data) {
  return request({
    url: '/appoint-front-service/mentor/topic',
    method: 'get',
    data
  })
}

// 新增导师话题
export function addMentorTopic(data) {
  return request({
    url: '/appoint-front-service/mentor/topic',
    method: 'post',
    data
  })
}

// 编辑导师话题
export function editMentorTopic(data) {
  return request({
    url: `/appoint-front-service/mentor/topic/${data.mentorTopicId}`,
    method: 'put',
    data
  })
}

// 查询导师单个话题
export function getMentorTopic(data) {
  return request({
    url: `/appoint-front-service/mentor/topic/${data.mentorTopicId}`,
    method: 'get',
    data
  })
}

// 删除导师单个话题
export function deleteMentorTopic(data) {
  return request({
    url: `/appoint-front-service/mentor/topic/${data.mentorTopicId}`,
    method: 'delete',
    data
  })
}

// 查询导师的可预约时间设置
export function getMentorAppointDate(data) {
  return request({
    url: `/appoint-front-service/mentor/appoint/config/able/date`,
    method: 'get',
    data
  })
}

// 设置导师的可预约时间
export function setMentorAppointDate(data) {
  return request({
    url: `/appoint-front-service/mentor/appoint/config`,
    method: 'post',
    data
  })
}

// 取消导师的可预约时间
export function cancelMentorAppointDate(data) {
  return request({
    url: `/appoint-front-service/mentor/appoint/config/${data.mentorAppointConfigId}`,
    method: 'delete',
    data
  })
}
// 查询导师的线下地址列表
export function getAppointOfflineAddressList(data) {
  return request({
    url: `/appoint-front-service/mentor/offline/address`,
    method: 'get',
    data
  })
}

// 删除导师的线下地址列表
export function deleteAppointOfflineAddress(data) {
  return request({
    url: `/appoint-front-service/mentor/offline/address/${data.mentorOfflineAddressId}`,
    method: 'delete',
    data
  })
}

// 添加导师的线下地址列表
export function postAppointOfflineAddress(data) {
  return request({
    url: `/appoint-front-service/mentor/offline/address`,
    method: 'post',
    data
  })
}
// 导师辅导方式打开
export function mentorAppointTypeOpen(data) {
  return request({
    url: `/user-front-service/mentor/appoint/${data.appointType}/status/open`,
    method: 'put',
    data
  })
}

// 导师辅导方式关闭
export function mentorAppointTypeClose(data) {
  return request({
    url: `/user-front-service/mentor/appoint/${data.appointType}/status/close`,
    method: 'put',
    data
  })
}
// 导师的预约统计数据
export function getMentorStatistics(data) {
  return request({
    url: `/appoint-front-service/mentor/appoint/log/statistics`,
    method: 'get',
    data
  })
}

// 获取学生基本信息
export function getStudentInfo(data) {
  return request({
    url: `/user-front-service/user/detail/${data.userId}`,
    method: 'get',
    data
  })
}
// 获取学生详细信息
export function getStudentDetail(data) {
  return request({
    url: `/recruit-front-service/online/resume/${data.userId}`,
    method: 'get',
    data
  })
}
