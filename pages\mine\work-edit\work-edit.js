// pages/mine/education-edit/education-edit.js
import { postMentorWork, getMentorWork, editMentorWork, deleteMentorWork } from '~/api/user'
import moment from 'moment'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    autosize: {
      maxHeight: 1000,
      minHeight: 120,
    },
    id: '', // 是否具有编辑的id


    defaultStarSelectDate: '', // 默认的开始时间选择时间
    defaultEndSelectDate: '', // 默认的结束日期选择时间
    workObject: {
      companyName: '',
      positionName: '',
      professionalIntroduction: '',
      startDate: '',
      endDate: ''
    },

    startColums: [
      {
        values: [],
        defaultIndex: 0,
      },
      {
        values: [],
        defaultIndex: 0,
      },
    ],
    endColums: [
      {
        values: [],
        defaultIndex: 0,
      },
      {
        values: [],
        defaultIndex: 0,
      },
    ],

    startDatePopup: false, // 选择开始时间的popup
    endDatePopup: false, // 选择结束时间的popup
  },
  
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      id: options.id
    })
    this.setData({
      defaultStarSelectDate: moment().year() + '-01',
      defaultEndSelectDate: moment().format('YYYY-MM'),
    })
    // 通过接口获取到结束日期时，如果格式是9999-01-01 00:00:00，那么要赋值为至今
    // this.setDateColums()
    if (this.data.id) {
      this.getMentorWork()
    } else {
      this.setDateColums()
    }
  },
  async getMentorWork() {
    const { data } = await getMentorWork({
      mentorProfessionalId: this.data.id
    })
    if (data.startDate) {
      data.startDate = moment(data.startDate).format('YYYY-MM')
    }
    if (data.endDate) {
      if (data.endDate === '9999-01-01 00:00:00' || data.endDate === '9999-12-01 00:00:00') {
        data.endDate = '至今'
      } else {
        data.endDate = moment(data.endDate).format('YYYY-MM')
      }
    }
    const workObject = {...this.data.workObject, ...data}
    this.setData({
      workObject
    })
    this.setDateColums()
  },

  popupClose() {
    this.setData({
      startDatePopup: false,
      endDatePopup: false
    })
  },
  showStartPopup() {
    this.setData({
      startDatePopup: true
    })
  },
  showEndPopup() {
    this.setData({
      endDatePopup: true
    })
  },
  onChange(event) {
    this.setData({
      [`workObject.${event.currentTarget.dataset.key}`]: event.detail
    })
  },
  expDelete() {
    const that = this
    wx.showModal({
      content: '确认删除该工作经历？',
      async success (res) {
        if (res.confirm) {
          const { success } = await deleteMentorWork({
            mentorProfessionalId: that.data.id
          })
          if (success) {
            wx.showToast({
              title: '删除工作经历成功',
              icon: 'none'
            })
            setTimeout(() => {
              wx.navigateBack()
            }, 1000)
          }
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  },
  expBack() {
    wx.navigateBack()
  },
  async expEdit() {
    for(const key in this.data.workObject) {
      if ((this.data.workObject[key] ?? '') === '') {
        wx.showToast({
          title: '请填写完整工作经历信息',
          icon: 'none'
        })
        return
      }
    }
    const obj = JSON.parse(JSON.stringify(this.data.workObject))
    obj.startDate += '-01 00:00:00'
    if (obj.endDate === '至今') {
      obj.endDate = '9999-01-01 00:00:00'
    } else {
      obj.endDate += '-01 00:00:00'
    }
    if (this.data.id) {
      obj.mentorProfessionalId = this.data.id
      const { success } = await editMentorWork(obj)
      if (success) {
        wx.showToast({
          title: '修改工作经历成功',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1000)
      }
    } else {
      const { success } = await postMentorWork(obj)
      if (success) {
        wx.showToast({
          title: '保存工作经历成功',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1000)
      }
    }
  },


  /**
   * 时间选择相关的方法
   */
  // 必须为YYYY-MM的格式。年份日期改变的时候也需要调用一下这个方法，为了同步月份的区别
  setDateColums() {
    const minDate = this.data.workObject.startDate || this.data.defaultStarSelectDate
    let maxDate = ''
    // const maxDate = this.data.workObject.endDate === '至今' ? moment().format('YYYY-MM') : this.data.workObject.endDate
    if (this.data.workObject.endDate) {
      maxDate = this.data.workObject.endDate === '至今' ? moment().format('YYYY-MM') : this.data.workObject.endDate
    } else {
      maxDate = this.data.defaultEndSelectDate
    }

    const startDate = moment(minDate);
    const endDate = moment(maxDate);

    const startYear = startDate.year();
    const startMonth = startDate.month() + 1;
    
    const endYear = endDate.year();
    const endMonth = endDate.month() + 1;

    const startYearArr = []
    const endYearArr = []
    const startMonthArr = []
    let endMonthArr = []
    if (endYear === startYear) {
      if (endYear === moment().year()) {
        // 处理开始月份的月份数组
        for (let i = 1; i <= endMonth; i++) {
          startMonthArr.push(i)
        }
        // 处理结束月份的月份数组
        if (this.data.workObject.endDate === '至今') {
          // 如果是至今，那么月份数组直接为空就行
          endMonthArr = []
        } else {
          for (let i = startMonth; i <= moment().month() + 1; i++) {
            endMonthArr.push(i)
          }
        }
      } else {
        for (let i = 1; i <= endMonth; i++) {
          startMonthArr.push(i)
        }
        // 处理结束月份的月份数组
        if (this.data.workObject.endDate === '至今') {
          // 如果是至今，那么月份数组直接为空就行
          endMonthArr = []
        } else {
          for (let i = startMonth; i <= 12; i++) {
            endMonthArr.push(i)
          }
        }
      }
    } else {
      if (endYear === moment().year()) {
        // 处理开始月份的月份数组
        for (let i = 1; i <= 12; i++) {
          startMonthArr.push(i)
        }
        // 处理结束月份的月份数组
        if (this.data.workObject.endDate === '至今') {
          // 如果是至今，那么月份数组直接为空就行
          endMonthArr = []
        } else {
          for (let i = startMonth; i <= moment().month() + 1; i++) {
            endMonthArr.push(i)
          }
        }
      } else {
        for (let i = 1; i <= 12; i++) {
          startMonthArr.push(i)
        }

        // 处理结束月份的月份数组
        if (this.data.workObject.endDate === '至今') {
          // 如果是至今，那么月份数组直接为空就行
          endMonthArr = []
        } else {
          for (let i = startMonth; i <= 12; i++) {
            endMonthArr.push(i)
          }
        }
      }
    }
    endYearArr.unshift('至今')
    for(let i = endYear ; i >= 1950; i--) {
      startYearArr.push(i)
    }
    for(let i = moment().year() ; i >= startYear ; i--) {
      endYearArr.push(i)
    }
    const startYearIndex = startYearArr.findIndex(item => item === startYear)
    const startMonthIndex = startMonthArr.findIndex(item => item === startMonth)
    let endYearIndex = 0
    if (this.data.workObject.endDate === '至今') {
      endYearIndex = 0
    } else {
      endYearIndex = endYearArr.findIndex(item => item === endYear)
    }
    let endMonthIndex = 0
    if (this.data.workObject.endDate === '至今') {
      endMonthIndex = 0
    } else {
      endMonthIndex = endMonthArr.findIndex(item => item === endMonth)
    }

    this.selectComponent('#startPicker').setColumnValues(0, startYearArr)
    this.selectComponent('#startPicker').setColumnValues(1, startMonthArr)
    this.selectComponent('#endPicker').setColumnValues(0, endYearArr)
    this.selectComponent('#endPicker').setColumnValues(1, endMonthArr)

    wx.nextTick(() => {
      setTimeout(() => {
        this.selectComponent('#startPicker').setColumnIndex(0, startYearIndex)
        this.selectComponent('#startPicker').setColumnIndex(1, startMonthIndex)
        this.selectComponent('#endPicker').setColumnIndex(0, endYearIndex)
        this.selectComponent('#endPicker').setColumnIndex(1, endMonthIndex)
      }, 300)
    })

  },
  setStartMonths(year) {
    const maxDate = this.data.workObject.endDate === '至今' ? moment().format('YYYY-MM') : this.data.workObject.endDate
    const endDate = moment(maxDate);
    const endYear = endDate.year();
    const endMonth = endDate.month() + 1;
    const startMonthArr = []
    if (year == endYear) {
      for (let i = 1; i <= endMonth; i++) {
        startMonthArr.push(i)
      }
    } else {
      for (let i = 1; i <= 12; i++) {
        startMonthArr.push(i)
      }
    }
    this.selectComponent('#startPicker').setColumnValues(1, startMonthArr)
    this.selectComponent('#startPicker').setColumnIndex(1, 0)
  },
  setEndMonths(year) {
    if (year === '至今') {
      this.selectComponent('#endPicker').setColumnValues(1, [])
      this.selectComponent('#endPicker').setColumnIndex(1, 0)
    } else {
      const minDate = this.data.workObject.startDate
      const startDate = moment(minDate);
      const startYear = startDate.year();
      const startMonth = startDate.month() + 1;
      
      const endMonthArr = []
      if (year == startYear) {
        if (year === moment().year()) {
          for (let i = startMonth; i <= moment().month() + 1; i++) {
            endMonthArr.push(i)
          }
        } else {
          for (let i = startMonth; i <= 12; i++) {
            endMonthArr.push(i)
          }
        }
      } else {
        for (let i = 1; i <= 12; i++) {
          endMonthArr.push(i)
        }
      }
      this.selectComponent('#endPicker').setColumnValues(1, endMonthArr)
      this.selectComponent('#endPicker').setColumnIndex(1, 0)
    }
  },
  startDateChange(value) {
    if (value.detail.index === 0) {
      // 获取年份，改变年份时动态修改月份的数组
      this.setStartMonths(value.detail.value[0])
    }
  },
  endDateChange(value) {
    if (value.detail.index === 0) {
      // 获取年份，改变年份时动态修改月份的数组
      this.setEndMonths(value.detail.value[0])
    }
  },
  startConfirm(value) {
    const startDate = value.detail.value[0] + '-' + (value.detail.value[1] < 10 ? ('0' + String(value.detail.value[1])) : value.detail.value[1])
    this.setData({
      'workObject.startDate': startDate
    })
    this.setDateColums()
    this.popupClose()
  },
  endConfirm(value) {
    let endDate = ''
    if (value.detail.value[0] === '至今') {
      endDate = value.detail.value[0]
    } else {
      endDate = value.detail.value[0] + '-' + (value.detail.value[1] < 10 ? ('0' + String(value.detail.value[1])) : value.detail.value[1])
    }
    this.setData({
      'workObject.endDate': endDate
    })
    this.setDateColums()
    this.popupClose()
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})