// pages/module-mentorShop/course-detail/components/tab.js
import {
  getCourseCommentList
} from "~/api/mentorShop";
import { processHtmlContent } from "~/utils/htmlProcessor";
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    courseType: {
      type: String,
      value: 'course' // 课程类型article:图文；course：专栏；其他是视频
    },
    isEdit: {
      type: Number,
      value: 1 // 是否为导师课程预览
    },
    courseInfo: {
      type: Object,
      value: {},
      observer(newVal) {
        if (newVal && newVal.courseDetail) {
          this.setData({
            'courseInfo.courseDetail': processHtmlContent(newVal.courseDetail)
          });
          // this.processHtml();
        }
      }
    },
    courseCommentScore: {
      type: Object,
      value: {} //课程评价分数
    },
    courseId: {
      type: String,
      value: '', //课程id
      observer(newVal) {
        if (newVal) {
          this.getCourseCommentList(newVal);
        }
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    activeTab: 'introduce', //introduce;comment
    isLoading: true,
    courseList: [],
    courseDescription: '',
    courseImage: '',
    commentStats: {
      score: 5.0,
      count: 200
    },
    lockIcons: {
      blue: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/blue-lock.png',
      grey: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/grey-lock.png',
      yellow: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/yellow-lock.png'
    },
    dynamicsList: []
  },

  lifetimes: {
    attached() {},
  },


  pageLifetimes: {
    show() {
      // 页面显示时，如果有 courseId 则重新获取评论列表
      if (this.properties.courseId) {
        this.getCourseCommentList(this.properties.courseId);
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 获取课程评价列表
    getCourseCommentList(courseId) {
      getCourseCommentList({
        courseId: courseId
      }).then(res => {
        this.setData({
          dynamicsList: res?.data
        })
      })
    },

    // 切换标签页
    switchTab(e) {
      if(this.properties.isEdit !== 1 ){
        wx.showModal({
          title: '',
          content: '预览模式不支持查看评论',
          showCancel: false,
          confirmText: '知道了'
        })
        return
      }
      const type = e.currentTarget.dataset.type;
      this.setData({
        activeTab: type
      });
      if(type === 'comment'){
        this.getCourseCommentList(this.properties.courseId);
      }
    },

    // 修改音频播放事件处理器
    handleAudioPlay(e) {
      // 将事件往上传递给父组件，包括音频时长
      this.triggerEvent('audioPlay', e.detail);
    },

    // 添加音频结束事件处理器
    handleAudioEnd() {
      this.triggerEvent('audioEnd');
    },

    // 添加音频暂停事件处理器
    handleAudioPause() {
      this.triggerEvent('audioPause');
    },

    /**
     * 处理图片点击预览
     * @param {Object} e 事件对象
     */
    onImgTap(e) {
      const current = e.detail.src;
      // mp-html 组件会自动收集所有图片
      const urls = e.detail.urls || [current];
      wx.previewImage({
        current, // 当前显示图片的链接
        urls,    // 需要预览的图片链接列表
        fail(err) {
          wx.showToast({
            title: '图片预览失败',
            icon: 'none'
          });
        }
      });
    },
  }
})