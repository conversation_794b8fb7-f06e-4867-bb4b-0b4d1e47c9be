// 引入 request 文件
import request from '../utils/request';

// 获取用户手机号
export const getUserPhoneNumber = (params) => {
  return request({
    url: '/user-front-service/wx/getUserPhoneNumber',
    method: 'GET',
    data: params
  });
};
// 导师注册
export const register = (params) => {
  return request({
    url: '/user-front-service/user/register',
    method: 'post',
    data: params
  })
}

// 游客注册
export const touristRegister = (params) => {
  return request({
    url: '/user-front-service/user/register/wxmp/mentor/enter',
    method: 'post',
    data: params
  })
}

// 游客的查看审核状态登录
export const touristAuthLogin = (params) => {
  return request({
    url: '/user-front-service/user/register/wxmp/auth/status',
    method: 'post',
    data: params
  })
}

// 一键登录
export const userLogin = (params) => {
  return request({
    url: '/user-front-service/user/app/login',
    method: 'post',
    data: params
  });
};

// 获取手机验证码
export const getPhoneCode = (params) => {
  return request({
    url: '/user-front-service/sms/send/code',
    method: 'get',
    data: params
  });
};

// 手机号登录
export const login = (params) => {
  return request({
    url: '/user-front-service/user/login',
    method: 'POST',
    data: params
  });
};

// 账号密码登录
export const loginAccount = (params) => {
  return request({
    url: '/user-front-service/user/pc/office/web/login',
    method: 'POST',
    data: params
  });
};

// 重置账号密码
export const resetPassword = (params) => {
  return request({
    url: '/user-front-service/user/pc/office/web/reset/password',
    method: 'POST',
    data: params
  });
};
