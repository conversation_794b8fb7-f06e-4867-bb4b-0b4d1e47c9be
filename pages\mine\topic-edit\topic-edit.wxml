<view class="exp-edit">

  <view class="exp-content">
    <view class="experience-edit">
      <view class="experience-edit-box">

        <van-field custom-class="required" input-align="right" border="{{ false }}" bind:change="onChange" data-key="title" model:value="{{ title }}" maxlength="30" label="话题标题" placeholder="请输入话题标题" />

        <van-field
        autosize="{{ autosize }}"
        maxlength='600'
        bind:change="onChange" data-key="content"
        model:value="{{ content }}"
        label="话题描述" 
        placeholder="请输入话题描述" 
        type="textarea"
        custom-class="required custom-textarea"/>
      </view>
      <view class="experience-card">
        <view class="cue-header">
          <view class="title">补充信息</view>
          <view class="add-cue" bind:tap="addCueList" wx:if="{{cueList.length < 10}}">
            <image src="/static/icon/add-btn-fill.png" mode=""/>
            <text>新增</text>
          </view>
        </view>
        <view class="warning-tips">
          <image src="/static/icon/warning-circle.png" mode=""/>
          <text>预约该话题时需要补充填写的信息</text>
        </view>
        <view class="input-box" wx:for="{{ cueList }}" wx:key="{{ index }}">
          <view class="input-content">
            <van-field input-align="left" border="{{ false }}" model:value="{{ item.tips }}"  bind:change="cueChange" data-index="{{ index }}"  maxlength="20" placeholder="请输入" />
          </view>
          <image src="/static/icon/delete-danger.png" mode="aspectFit" bind:tap="deleteCueList" data-index="{{ index }}"/>
        </view>
      </view>

      <view class="experience-card">
        <view class="cue-header">
          <view class="title {{ forceUploadAttachment === 1 ? 'required' : '' }}">附件信息</view>
          <view class="appoint-required" bind:tap="attachmentRequireChange">
            <image src="{{forceUploadAttachment === 1 ? 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/check-icon/check-active.png' : 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/check-icon/check-line.png'}}" mode=""/>
            <text>预约必填</text>
          </view>
        </view>
        <view class="input-box">
          <view class="input-content">
            <van-field bind:change="onChange" data-key="attachmentDescription" input-align="left" border="{{ false }}" model:value="{{ attachmentDescription }}" maxlength="20" placeholder="请输入附件上传的要求，如请上传简历附件" />
          </view>
        </view>
      </view>
    </view>
    <copyright></copyright>
  </view>
  <view class="exp-footer">
    <van-button wx:if="{{id}}" bind:tap="expDelete" custom-class="default-btn" type="default">删除</van-button>
    <van-button wx:else bind:tap="expBack" custom-class="default-btn" type="default">取消</van-button>
    <van-button bind:tap="expEdit" custom-class="info-btn" type="info">保存</van-button>
  </view>
</view>
