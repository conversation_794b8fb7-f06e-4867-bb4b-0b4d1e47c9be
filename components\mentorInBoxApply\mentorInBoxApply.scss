page{
  height: 100%;
}
.mentor-in-box{
  background: linear-gradient(180.00deg, rgb(245, 246, 255) 7.833%,rgb(252, 252, 255) 35.404%);
  height: 100%;
  display: flex;
  flex-direction: column;
  .mentor-in-content{
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .schedule{
      padding: 30rpx 64rpx 20rpx;
      .slider-box{
        padding: 0 24rpx;
      }
      .custom-slide-img{
        width: 40rpx;
        height: 40rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .step-items{
        margin-top: 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: rgb(51, 51, 51);
        font-size: 24rpx;
        font-weight: 400;
        line-height: 34rpx;
        .step-item{

        }
      }
    }
    .slot-box{
      flex: 1;
      padding: 10rpx 36rpx 22rpx;
    }
  }
  .mentor-in-footer{
    box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0,0,0,0.05);
    background: #FFF;
    height: 160rpx;
    padding: 24rpx 36rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    van-button{
      overflow: hidden;
      height: 100%;
      flex: 1;
      margin-right: 40rpx;
      button{
        font-size: 36rpx;
        // line-height: 48rpx;
        border-radius: 120rpx;
        width: 100%;
        height: 100%;
      }
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .default-btn{
      border: 1px solid #0057FF;
      background: #fff;
      color: #0057FF;
    }
    .info-btn{
      border: 1px solid #0057FF !important;
      background: #0057FF !important;
      color: rgb(255, 255, 255) !important;
    }
  }
}