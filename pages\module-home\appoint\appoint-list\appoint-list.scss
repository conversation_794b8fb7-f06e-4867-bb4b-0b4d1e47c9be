/* pages/module-home/appoint/appoint-list/appoint-list.wxss */
.van-tab--active{
  font-weight: 600 !important;
}
page{
  height: 100%;
}
.appoint-list{
  height: 100%;
  display: flex;
  flex-direction: column;
}
.appoint-data{
  flex: 1;
  overflow: auto;
  background: #f8f9fd;
  .scrollarea{
    height: 100%;
  }
  .appoint-data-list{
    padding: 28rpx 36rpx;
  }
  .empty{
    padding-top: 120rpx;
  }
  .appoint-item{
    margin-top: 28rpx;
    padding: 40rpx 28rpx;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 32rpx 0px rgba(99, 97, 155, 0.1);
    background-color: #FFF;
    .mentor-info{
      display: flex;
      align-items: center;
      position: relative;
      .appoint-type-image{
        width: 46rpx;
        height: 46rpx;
        border-radius: 50%;
        position: absolute;
        top: 0;
        right: 0;
      }
      .mentor-info-avatar{
        width: 110rpx;
        height: 110rpx;
        margin-right: 24rpx;
        border-radius: 50%;
        object-fit: cover;
      }
      .mentor-info-base{
        flex: 1;
        overflow: hidden;
        .name{
          color: rgb(51, 51, 51);
          font-size: 28rpx;
          font-weight: 700;
          line-height: 40rpx;
          display: flex;
          align-items: center;
          padding-right: 64rpx;
        }
        .address{
          display: flex;
          align-items: center;
          margin-top: 12rpx;
          image{
            width: 30rpx;
            height: 30rpx;
          }
          .address-value{
            flex: 1;
            margin-left: 8rpx;
          }
        }
      }
    }
    .appoint-topic{
      margin-top: 12rpx;
      color: rgb(51, 51, 51);
      font-size: 28rpx;
      font-weight: 400;
      line-height: 50rpx;
    }
    .appoint-status{
      padding-top: 20rpx;
      margin-top: 20rpx;
      border-top:  1px solid rgb(224, 224, 230);
      display: flex;
      justify-content: space-between;
      align-items: center;
      .appoint-date{
        color: rgb(115, 119, 131);
        font-size: 24rpx;
        font-weight: 400;
        line-height: 36rpx;
      }
      .status{
        padding: 4rpx 14rpx;
        font-size: 24rpx;
        font-weight: 400;
        line-height: 36rpx;
        border-radius: 8rpx;
      }
      .a{
        border: 2rpx solid rgb(31, 162, 26);
        background: rgba(31, 162, 26, 0.1);
        color: rgb(31, 162, 26);
      }
    }
    &:nth-child(1) {
      margin-top: 0;
    }
  }
}