<video id="addVideo" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/%E5%80%9F%E6%A2%A6.mp4" autoplay loop controls="{{false}}" muted show-center-play-btn="{{false}}" />
<view class="video-box">
  <!-- 专栏 -->
  <view class="section">
    <!-- 上传视频 -->
    <view class="cover-section">
      <view class="section-title">上传视频</view>
      <view class="upload-tip">（视频大小小于500M，仅支持MP4、MOV、AVI格式）</view>
      <view class="upload-area">
        <view wx:if="{{!form.videoUrl}}" class="upload-btn" bindtap="chooseVideo">
          <van-icon name="plus" color='#9EA3AC' size='30px' />
        </view>
        <view class="upload-image" wx:else>
          <van-icon wx:if="{{ !id || showVideDelete }}" name="cross" color='#fff' size='15px' bindtap="removeVideo" />
          <video id="myVideo" src="{{form.videoUrl}}" show-center-play-btn="{{false}}" mode="aspectFill" object-fit="cover" style="width: 100%; height: 100%;" catch:tap="previewVideo" controls="{{false}}"></video>
          <van-icon name="play-circle-o" size='40px' catch:tap="previewVideo" />
        </view>
        <view class="mask" wx:if="{{isUploading}}">
          <van-loading color="#fff" />
          <view>上传中...</view>
        </view>
      </view>
    </view>
    <!-- 上传视频封面 -->
    <view class="cover-section">
      <view class="section-title">上传视频封面</view>
      <view class="upload-tip">（按照16:9的比例平铺展示，图片大小小于5M，仅支持JPG、PNG格式）</view>
      <view class="upload-area">
        <view wx:if="{{!form.courseCoverUrl}}" class="upload-btn" bindtap="chooseVideoCover">
          <van-icon name="plus" color='#9EA3AC' size='30px' />
        </view>
        <view class="upload-image" wx:else>
          <van-icon name="cross" color='#fff' size='15px' bindtap="removeVideoCover" />
          <image src="{{form.courseCoverUrl}}" mode="" />
        </view>
      </view>
    </view>

    <!-- 视频名称 -->
    <view class="name-section">
      <view class="section-title">视频名称</view>
      <view class="section-input">
        <van-field value="{{form.courseName}}" bind:change="handleColumnNameChange" maxlength="30" rows="1" autosize="{{autosize}}" type="textarea" placeholder="请输入视频名称" show-word-limit />
      </view>
    </view>

    <van-cell is-link title="视频详情" link-type="navigateTo" bind:tap="onEditDetail" />



    <van-popup show="{{ showCrop }}" bind:close="cancelShowCrop" custom-style="height: 100%; width: 100%">
      <crop imgFile="{{imgFile}}" upLoadFlag="{{true}}" bind:onCropperDone="handleCropperDone" bind:onCancel="cancelShowCrop"></crop>
    </van-popup>
  </view>

  <!-- 售卖方式 -->
  <view class="sale-section">
    <view class="sale-type">
      <text>支持单独售卖</text>
      <van-switch size="24px" bind:change="handleSwitchChange" checked="{{form.isIndividualSale===1}}" active-color="#0057FF" />
    </view>
    <view class="sale-sell" bindtap="showActionSheet">
      <text>售卖方式</text>
      <view class="price-tag">
        <text>{{currentSaleType}}</text>
        <van-icon name="arrow" color="#0057FF" />
      </view>
    </view>

    <view class="sale-price" wx:if="{{currentSaleType==='付费'}}">
      <view class="section-title">商品售价</view>
      <van-field value="{{form.coursePrice}}" bind:input="handlePriceChange" bind:blur="handlePriceBlur" type="textarea" placeholder="请输入售价(0.01-100000元)" right-icon="gold-coin-o" />
      <!-- <van-field value="{{form.coursePrice}}" bind:change="handlePriceChange" type="number" placeholder="请输入售价(0.01-100000元)" right-icon="gold-coin-o" /> -->
    </view>

    <view class="sale-type">
      <view class="title">
        <text>支持关联售卖</text>
        <van-icon bind:tap="showTip" data-type="association" color='#AAAAAA' size='18px' name="question-o" />
      </view>
      <van-switch size="24px" bind:change="handleAssociationChange" checked="{{form.isBundleSale===1}}" active-color="#0057FF" />
    </view>
    <view class="sale-type" wx:if="{{form.isBundleSale===1}}">
      <text>关联专栏</text>
      <view bindtap="onAddColumn">
        <view class="relevance" wx:if="{{!form.bundleColumnsIds || form.bundleColumnsIds.length  === 0}}">
          请选择
          <van-icon name="add-o" size='20px' color='#0057FF' />
        </view>
        <view class="relevance" wx:elif="{{form.bundleColumnsIds.length>0}}">已关联{{form.bundleColumnsIds.length}}个专栏<van-icon name="add-o" size='20px' color='#0057FF' /></view>
      </view>
    </view>
  </view>

  <!-- 其他 -->
  <view class="sale-section sale-other">
    <!-- <view class="sale-type">
      <view class="title">
        <text>文字防复制</text>
        <van-icon bind:tap="showTip" data-type="copy" color='#AAAAAA' size='18px' name="question-o" />
      </view>
      <van-switch size="24px" bind:change="handleCopyChange" checked="{{form.isTextNoCopy===1}}" active-color="#0057FF" />
    </view> -->

    <view class="sale-type">
      <view class="title">
        <text>防录屏跑马灯</text>
        <van-icon bind:tap="showTip" data-type="run" color='#AAAAAA' size='18px' name="question-o" />
      </view>
      <van-switch size="24px" bind:change="handleRunChange" checked="{{form.isMarquee===1}}" active-color="#0057FF" />
    </view>

    <view class="sale-type" wx:if="{{!id}}">
      <view class="title">
        <text>发布至动态</text>
      </view>
      <van-switch size="24px" bind:change="handleDynamicChange" checked="{{form.isPublishToDynamic===1}}" active-color="#0057FF" />
    </view>
  </view>

  <!-- 预览层 -->
  <view class="video-preview" wx:if="{{showPreviewVideo}}">
    <view class="video-container">
      <view class="close-btn" catch:tap="closePreview">
        <van-icon name="cross" size="40rpx" color="#fff" />
      </view>
      <video id="previewVideo" src="{{form.videoUrl}}" show-center-play-btn='{{false}}' show-play-btn="{{true}}" controls autoplay style="width: 100vw; height: 100vh; position: fixed; top: 0; left: 0;"></video>
    </view>
  </view>

  <view class="bottm-button-bar">
    <van-button bind:tap="onSave" color="#0057FF">下一步</van-button>
  </view>
</view>