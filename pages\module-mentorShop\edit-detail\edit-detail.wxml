<!--pages/module-mentorShop/edit-detail/edit-detail.wxml-->

<scroll-box >
 <view slot='content'>  <view class="toolbar" catchtouchend="format">
    <i class="iconfont icon-wuxupailie {{formats.list === 'bullet' ? 'ql-active' : ''}}" data-name="list" data-value="bullet"></i>
    <i class="iconfont icon-youxupailie {{formats.list === 'ordered' ? 'ql-active' : ''}}" data-name="list" data-value="ordered"></i>
    <i class="iconfont icon-charutupian" catchtouchend="insertImage"></i>
  </view>
  <view class="editor-wrapper">
    <editor id="editor" class="ql-container" show-img-size show-img-toolbar show-img-resize placeholder="{{placeholder}}" bindready="onEditorReady" bindinput="onContentChange" bindstatuschange="onStatusChange">
    </editor>
    <view class="char-count">
      <text class="{{currentLength <= maxLength ? 'save' : 'warn'}}">{{currentLength}}</text>
      <text>/{{maxLength}}</text>
    </view>
  </view> </view>

  <view slot='bottom' class="bottm-button-bar">
    <van-button bind:tap="saveBonusContent" color="#0057FF">保存</van-button>
  </view>
</scroll-box>