// pages/appoint/report-detail/report-detail.js
import dayjs from 'dayjs'
// import { getReportDetail, getAppointDetail, getRecordTime } from '~/api/appoint'
import { getReportDetail, getAppointDetail } from '~/api/appoint'
const innerAudioContext = wx.createInnerAudioContext({
  useWebAudioImplement: false // 是否使用 WebAudio 作为底层音频驱动，默认关闭。对于短音频、播放频繁的音频建议开启此选项，开启后将获得更优的性能表现。由于开启此选项后也会带来一定的内存增长，因此对于长音频建议关闭此选项
})
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showUnfold2: false,
    unfoldClose2: true,
    htmlSnip2: '',

    showUnfold: false,
    unfoldClose: true,
    htmlSnip: '',

    audioList: [],
    audioPlayObject: {
      audioPlayIndex: null, // 正在播放的index，如果有这个值就代表是正在播放，没有的时候设置为null
      isPlay: false,
    },
    reportDetail: {},
    appointDetail: {},
    appointId: '',
    documentScale: 1,
    initRecord: false,
    showRecord: false,
    unfoldRecord: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      appointId: options.appointId
    })
    this.getReportDetail()
    this.getAppointDetail()
  },
  downLoadFileSummer() {
    wx.downloadFile({
      url: this.data.reportDetail.suggestionSummaryAttachmentUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            showMenu: true,
            success: () => {
              // resolve({
              //   success: true,
              //   filePath: res.tempFilePath
              // });
            },
            fail: (err) => {
              // reject(new Error('打开文档失败：' + err.errMsg));
            }
          });
        } else {
          // reject(new Error('下载失败，状态码：' + res.statusCode));
        }
      },
      fail: (err) => {
        // reject(new Error('下载失败：' + err.errMsg));
      }
    });
  },
  previewFile() {
    wx.downloadFile({
      url: this.data.reportDetail.appointLogProblem.accessoryUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            showMenu: true,
            success: () => {
              // resolve({
              //   success: true,
              //   filePath: res.tempFilePath
              // });
            },
            fail: (err) => {
              // reject(new Error('打开文档失败：' + err.errMsg));
            }
          });
        } else {
          // reject(new Error('下载失败，状态码：' + res.statusCode));
        }
      },
      fail: (err) => {
        // reject(new Error('下载失败：' + err.errMsg));
      }
    });
  },
  showImagePreview1(event) {
    wx.previewImage({
      current: this.data.reportDetail.appointLogProblem.picUrlList[event.currentTarget.dataset.index], // 当前显示图片的http链接
      urls: this.data.reportDetail.appointLogProblem.picUrlList, // 当前显示图片的http链接
    })
  },
  showImagePreview(event) {
    wx.previewImage({
      current: this.data.reportDetail.suggestionSummaryImgUrlList[event.currentTarget.dataset.index], // 当前显示图片的http链接
      urls: this.data.reportDetail.suggestionSummaryImgUrlList, // 当前显示图片的http链接
    })
  },
  getHtmlValue(value) {
    const checkOption = value?.replace(/\n/g, '<br/>')
    return checkOption
  },
  handleShowUnfold2() {
    let that = this
    const query = wx.createSelectorQuery();
    query.select('.mentor-introduce-position2').boundingClientRect(function(rect) {
      let ratio = 750 / wx.getSystemInfoSync().windowWidth
      let height = rect.height * ratio
      if (height > (21 * 3 * ratio + 10)) {
        // 超过三行，这里的18是根据行高的36rpx转换的，+10是为了有点缓冲高度
        that.setData({
          showUnfold2: true
        })
      } else {
        that.setData({
          showUnfold2: false
        })
      }
    }).exec();
  },
  unfoldChange() {
    this.setData({
      unfoldClose: !this.data.unfoldClose
    })
  },
  unfoldChange2() {
    this.setData({
      unfoldClose2: !this.data.unfoldClose2
    })
  },
  handleShowUnfold() {
    let that = this
    const query = wx.createSelectorQuery();
    query.select('.mentor-introduce-position').boundingClientRect(function(rect) {
      let ratio = 750 / wx.getSystemInfoSync().windowWidth
      let height = rect.height * ratio
      if (height > (21 * 3 * ratio + 10)) {
        // 超过三行，这里的18是根据行高的36rpx转换的，+10是为了有点缓冲高度
        that.setData({
          showUnfold: true
        })
      } else {
        that.setData({
          showUnfold: false
        })
      }
    }).exec();
  },
  getFileIcon(fileUrl) {
    if (fileUrl.includes('doc') || fileUrl.includes('docx')) {
      return 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/job/job-icon-word.png'
    } else if (fileUrl.includes('pdf')) {
      return 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/job/job-icon-pdf.png'
    } else if (fileUrl.includes('xls') || fileUrl.includes('xlsx')) {
      return 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/job/job-icon-excel.png'
    } else if (fileUrl.includes('ppt') || fileUrl.includes('pptx')) {
      return 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/job/job-icon-ppt.png'
    }
  },
  async getReportDetail() {
    const { data } = await getReportDetail({
      appointLogId: this.data.appointId
    })
    data.newAppointDateTime = data.appointDateTime ? data.appointDateTime.split(' ')[0] : ''
    const obj = {
      10: '电话咨询',
      20: '线下咨询',
      30: '面对面咨询',
      40: '线上咨询'
    }
    data.appointTypeText = obj[data.appointType]

    if (data.appointType === 10) {
      data.appointDateValue = Math.ceil(data.secretReport.sumDuration / 60)
    }
    if (data.appointLogProblem.accessoryUrl) {
      data.fileIconType = this.getFileIcon(data.appointLogProblem.accessoryUrl)
    }
    if (data.suggestionSummaryAttachmentUrl) {
      data.fileIconType2 = this.getFileIcon(data.suggestionSummaryAttachmentUrl)
    }
    const value = this.getHtmlValue(data.consultRecord)
    const value2 = this.getHtmlValue(data.suggestionSummary)
    // const value = this.getHtmlValue('asdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasd')
    // const value2 = this.getHtmlValue('asdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasd')
    this.setData({
      htmlSnip: value,
      htmlSnip2: value2
    })
    this.handleShowUnfold()
    this.handleShowUnfold2()
    this.setData({
      reportDetail: data
    })
    // 电话咨询并且存在录音
    if (this.data.reportDetail.appointType === 10 && this.data.reportDetail.secretReport && this.data.reportDetail.secretReport.innerSecretReportList) {
      const arr = this.data.reportDetail.secretReport.innerSecretReportList.map(item => {
        item.audioProgress = 0
        item.audioPlayStatus = false
        return item
      })
      this.setData({
        audioList: arr
      })
    }
    // this.$nextTick(() => {
    //   this.handleShowUnfold()
    //   this.handleShowRecord()
    // })
  },
  // 以下为录音相关
  recordClick(event) {
    innerAudioContext.onPlay(() => {
      this.setData({
        'audioPlayObject.isPlay': true
      })
    });
    innerAudioContext.onPause(() => {
      this.setData({
        'audioPlayObject.isPlay': false
      })
    });
    innerAudioContext.onEnded(() => {
      this.data.audioList.forEach((item, i) => {
        this.data.audioList[audioListIndex].audioProgress = 0
      })
      this.setData({
        'audioPlayObject.audioPlayIndex': null,
        'audioPlayObject.isPlay': false
      })
    });
    // 监听播放进度更新事件
    innerAudioContext.onTimeUpdate(() => {
      // 获取当前播放时间（单位：秒）
      const currentTime = innerAudioContext.currentTime;
      this.setData({
        [`audioList[${audioListIndex}].audioProgress`]: (currentTime / this.data.audioList[audioListIndex].duration) * 100
      })
    });

    const audioListIndex = event.currentTarget.dataset.index
    if (this.data.audioPlayObject.audioPlayIndex || this.data.audioPlayObject.audioPlayIndex === 0) {
      // 这时候是有音频在播放
      if (audioListIndex === this.data.audioPlayObject.audioPlayIndex) {
        if (this.data.audioPlayObject.isPlay) {
          // 那就暂停
          innerAudioContext.pause() // 暂停
          this.setData({
            'audioPlayObject.isPlay': false
          })
        } else {
          innerAudioContext.play() // 暂停
          this.setData({
            'audioPlayObject.isPlay': true
          })
        }
      } else {
        // 切换音频
        this.data.audioList.forEach((item, i) => {
          this.data.audioList[audioListIndex].audioProgress = 0
        })
        this.setData({
          'audioPlayObject.audioPlayIndex': audioListIndex
        })
        innerAudioContext.src = this.data.audioList[audioListIndex].recordingUrl
        innerAudioContext.play() // 播放
        this.setData({
          'audioPlayObject.isPlay': true
        })
      }
    } else {
      this.data.audioList.forEach((item, i) => {
        this.data.audioList[audioListIndex].audioProgress = 0
      })
      // 没有音频在播放
      this.setData({
        'audioPlayObject.audioPlayIndex': audioListIndex
      })
      innerAudioContext.src = this.data.audioList[audioListIndex].recordingUrl
      innerAudioContext.play() // 播放
      this.setData({
        'audioPlayObject.isPlay': true
      })
    }

    // if ((audioListIndex ?? '') === '') {
    //   if (this.data.audioPlayObject.audio.paused) {
    //     this.audioPlayObject.audio.play()
    //   } else {
    //     this.audioPlayObject.audio.pause() // 暂停 
    //   }
    // } else {
    //   const audio = document.querySelector(`#audioList${audioListIndex}`)
    //   if (audio.paused) {
    //     // 准备开始播放其中一个音频的时候，先把全部音频都暂停了
    //     const audioListValueAll = document.querySelectorAll('.audioListValue')
    //     audioListValueAll.forEach(item => {
    //       item.pause()
    //     })
    //     this.$nextTick(() => {
    //       audio.play()
    //     })
    //   } else {
    //     audio.pause() // 暂停 
    //   }
    // }
  },
  audioChange(event) {
    const value = event.detail
    const audioListIndex = event.currentTarget.dataset.index
    const position = (value / 100) * this.data.audioList[audioListIndex].duration
    if (innerAudioContext.duration > 0 && position <= innerAudioContext.duration) {
      innerAudioContext.seek(position); // 跳转到指定位置
      this.setData({
        [`audioList[${audioListIndex}].audioProgress`]: value
      })
      innerAudioContext.play(); // 开始播放
    } else {
      console.error('跳转位置无效');
    }
  },
  async getAppointDetail() {
    const { data } = await getAppointDetail({
      appointLogId: this.data.appointId
    })
    this.setData({
      appointDetail: data
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    innerAudioContext.destroy()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})