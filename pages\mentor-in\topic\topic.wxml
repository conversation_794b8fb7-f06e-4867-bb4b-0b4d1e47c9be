<mentorInBox sliderValue="{{ 73 }}" back="{{ !hideBack }}" bind:pageNext="pageNext" nextBtnValue="{{ true ? '下一步' : '稍后填写' }}">
  <view class="education-list">
    <view class="experience-box">
      <view class="experience-item" wx:for="{{ topicList }}" wx:key="{{ index }}" bind:tap="editExp" data-id="{{ item.id }}">
        <view class="experience-item-header experience-item-header-topic">
          <text class="topic-icon">#</text>
          <text class="experience-item-header-left textoverflow">{{item.title}}</text>
          <view class="experience-item-header-edit">
            <text>编辑</text>
            <image src="/static/icon/arrow-right-gray.png" mode="aspectFill"/>
            <!-- <svg-icon name="arrow-right" size="8" color="rgb(119, 119, 119)"></svg-icon> -->
          </view>
        </view>
        <view class="experience-item-discribe">
          {{item.content}}
        </view>
      </view>
      <view class="experience-add" bind:tap="editExp" wx:if="{{topicList.length < 10}}">
        <view class="experience-add-left">
          <view class="add-name">
            <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/mentor-in/mentor-in-topic.png" mode="aspectFill"/>
            <text>擅长话题</text>
          </view>
          <view class="add-tips">添加您的擅长话题</view>
        </view>
        <view class="experience-add-right">
          <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/add-icon.png"  mode="aspectFill"/>
        </view>
      </view>
    </view>
  </view>
</mentorInBox>
