// pages/components/uploader/uploader.js
const docIcon = 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/upload/word.png'
const pdfIcon = 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/upload/pdf.png'
const excelIcon = 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/upload/excel.png'
const uploadFileIcon = 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/upload/upload-icon.png'
import { webUpload } from "../../api/webUpload"
Component({
  properties: {
    value: {
      type: Array,
      value: []
    },
    pathImg: String,
    pathFile: String,
    pathVoice: String,
    api: String,
    path: String,
    tip: String,
    accept: String,
    previewImage: {
      type: Boolean,
      value: true
    },
    previewFullImage: {
      type: <PERSON>olean,
      value: true
    },
    previewOptions: Object,
    readonly: {
      type: Boolean,
      value: false
    },
    disabled: {
      type: Boolean,
      value: false
    },
    multiple: {
      type: Boolean,
      value: false
    },
    deletable: {
      type: Boolean,
      value: false
    },
    showUpload: {
      type: Boolean,
      value: true
    },
    lazyLoad: {
      type: Boolean,
      value: false
    },
    maxSize: Number,
    uploadWord: String,
    maxCount: {
      type: Number,
      value: 9
    },
    fileMaxCount: Number,
    imgMaxCount: Number,
    uploadIcon: String
  },

  data: {
    enableDisable: false,
    fileLists: [],
    previousFileLists: [],
    uploadIcons: uploadFileIcon
  },

  observers: {
    'value': function (data) {
      if (data && Array.isArray(data) && data.length > 0 && 'url' in data[0]) {
        const newFileLists = data.map(v => {
          const ary = v.url.split('/')
          const name = v.name || ary[ary.length - 1]
          const file = {
            name
          }
          const type = this.isImg(name) ? 'img' : this.isFile(name) ? 'file' : this.isVoice(name) ? 'voice' : ''
          return {
            file,
            url: v.url,
            status: 'done',
            type
          }
        })

        if (JSON.stringify(newFileLists) !== JSON.stringify(this.data.previousFileLists)) {
          this.setData({
            fileLists: newFileLists,
            previousFileLists: newFileLists
          })
          this.processFiles()
          this.change()
        }
      }
    }
  },

  methods: {
    // 在组件加载时处理每个文件的图标
    processFiles() {
      const filesWithIcons = this.data.fileLists.map(item => {
        return {
          ...item,
          icon: this.iconType(item.file.name)
        };
      });
      this.setData({
        fileLists: filesWithIcons
      });
    },
    iconType(val) {
      const name = val.toLowerCase()
      return name.includes('doc') || name.includes('docx') ? docIcon :
        name.includes('pdf') ? pdfIcon :
        name.includes('xlsx') || name.includes('xls') ? excelIcon :
        ''
    },

    isImg(val) {
      if (typeof val !== 'string') return false
      const name = val.toLowerCase()
      return name.includes('jpg') || name.includes('jpeg') || name.includes('png')
    },

    isFile(val) {
      if (typeof val !== 'string') return false
      const name = val.toLowerCase()
      return name.includes('doc') || name.includes('docx') || name.includes('pdf') ||
        name.includes('xlsx') || name.includes('xls')
    },

    isVoice(val) {
      if (typeof val !== 'string') return false
      const name = val.toLowerCase()
      return name.includes('mp3') || name.includes('mp4')
    },

    clickPreview(e) {
      const {
        url
      } = e.currentTarget.dataset
      const file = this.data.fileLists.find(f => f.url === url)

      if (!file) return

      // 如果是临时文件路径（本地路径），直接预览
      if (url.startsWith('wxfile://') || url.startsWith('http')) {
        const urls = this.data.fileLists
          .filter(item => item.type !== 'file') // 过滤掉文件类型
          .map(item => item.url)

        wx.previewImage({
          current: url,
          urls: urls,
          fail: (err) => {
            console.error('Preview failed:', err)
            wx.showToast({
              title: '预览失败',
              icon: 'none'
            })
          }
        })
      } else {

        // 这里可以添加链接处理逻辑
        const processedUrl = url // 处理url的逻辑
        const processedUrls = this.data.fileLists
          .filter(item => item.type !== 'file')
          .map(item => item.url) // 处理urls的逻辑

        wx.hideLoading()
        wx.previewImage({
          current: processedUrl,
          urls: processedUrls,
          fail: (err) => {
            console.error('Preview failed:', err)
            wx.showToast({
              title: '预览失败',
              icon: 'none'
            })
          }
        })
      }
    },

    checkMaxSize(file) {
      if (this.properties.maxSize && (file.size > this.properties.maxSize)) {
        const mb = this.properties.maxSize / 1024 / 1024
        wx.showToast({
          title: `${this.properties.uploadWord || '上传图片/附件'}请控制在${mb}MB以内`,
          icon: 'none'
        })
        return false
      }
      return true
    },

    checkAccept(file) {
      if (this.properties.accept) {
        const acceptLists = this.properties.accept.split(',')
          .map((item) => item.replace('.', '')).join('/')

        const fileLength = file.name.split('.').length
        const name = file.name.split('.')[fileLength - 1] || file.name.split('.')[0]
        const fileType = name.toLowerCase()
        if (!this.properties.accept.includes(fileType)) {
          wx.showToast({
            title: `只支持${acceptLists}格式`,
            icon: 'none'
          })
          return false
        }
      }
      return true
    },

    checkFileMaxNum(file) {
      const { fileLists } = this.data;

      if (this.isImg(file.name)) {
        const num = fileLists.filter(item => item.type === 'img').length;
        if (num >= this.properties.imgMaxCount) {
          wx.showToast({
            title: `只能上传${this.properties.imgMaxCount}张图片`,
            icon: 'none'
          });
          return false;
        }
      } else if (this.isFile(file.name)) {
        const num = fileLists.filter(item => item.type === 'file').length;
        if (num >= this.properties.fileMaxCount) {
          wx.showToast({
            title: `只能上传${this.properties.fileMaxCount}个附件`,
            icon: 'none'
          });
          return false;
        }
      } else if (this.isVoice(file.name)) {
        const num = fileLists.filter(item => item.type === 'voice').length;
        if (num >= this.properties.voiceMaxCount) {
          wx.showToast({
            title: `只能上传${this.properties.voiceMaxCount}个音频`,
            icon: 'none'
          });
          return false;
        }
      }
      return true;
    },

    customBeforeRead(file) {
      if (this.properties.beforeRead) {
        return this.triggerEvent('before-read', file)
      }
      return this.checkAccept(file) && this.checkMaxSize(file) && this.checkFileMaxNum(file)
    },
    async customAfterRead() {
      if (this.properties.afterRead) {
        this.triggerEvent('after-read')
        return
      }

      this.setData({
        enableDisable: true
      })

      try {
        const {
          accept
        } = this.properties
        const supportImage = this.isFileTypeSupported(accept, ['.jpg', '.jpeg', '.png'])
        const supportFile = this.isFileTypeSupported(accept, ['.doc', '.docx', '.pdf', '.xls', '.xlsx'])

        if (supportImage && supportFile) {
          // 如果同时支持图片和文件，显示选择菜单
          const {
            tapIndex
          } = await wx.showActionSheet({
            itemList: ['选择图片', '选择文件']
          })
          if (tapIndex === 0) {
            await this.chooseImage() // 选择图片
          } else {
            await this.chooseFile() // 选择文件
          }
        } else if (supportImage) {
          await this.chooseImage() // 仅支持图片
        } else if (supportFile) {
          await this.chooseFile() // 仅支持文件
        }
      } catch (error) {
        console.error('Choose failed:', error)
      } finally {
        this.setData({
          enableDisable: false
        })
      }
    },

    // 用于判断文件类型是否支持
    isFileTypeSupported(accept, supportedTypes) {
      if (!accept) return false
      const acceptedTypes = accept.split(',').map(type => type.toLowerCase().trim())
      return acceptedTypes.some(type => supportedTypes.includes(type))
    },

    // 上传文件的通用处理函数
    async handleFileChoose({
      chooseMethod,
      type,
      fileHandler,
      mediaType = ['image']
    }) {
      try {
        const remainingCount = this.properties.maxCount - (this.data.fileLists?.length || 0)
        const res = await chooseMethod({
          count: remainingCount,
          mediaType, // 设置mediaType限制
          sourceType: ['album', 'camera'],
          sizeType: ['original', 'compressed'],
          type: type
        })

        const files = res.tempFiles.map(file => ({
          file: {
            name: file.tempFilePath ? file.tempFilePath.split('/').pop() : file.name,
            size: file.size
          },
          url: file.tempFilePath || file.path,
          type,
          status: 'uploading'
        }))

        if (!this.customBeforeRead(files[0].file)) return

        await this.uploadFiles(files)
      } catch (error) {
        console.error(`${type} choose failed:`, error)
      }
    },

    async chooseImage() {
      // 确保只选择图片
      await this.handleFileChoose({
        chooseMethod: wx.chooseMedia,
        type: 'img',
        mediaType: ['image']
      })
    },

    async chooseFile() {
      // 确保只选择文件
      await this.handleFileChoose({
        chooseMethod: wx.chooseMessageFile,
        type: 'file'
      })
    },

    async uploadFiles(files) {
      wx.showLoading({
        title: '上传中...',
        mask: true
      })

      // 添加图标到文件对象中
      const filesWithIcons = files.map(file => ({
        ...file,
        status: 'uploading',
        icon: this.iconType(file.file.name)  // 添加图标
      }))
      
      const newFileList = [...this.data.fileLists, ...filesWithIcons]
      this.setData({
        fileLists: newFileList,
        isUploading: true
      })

      try {
        for (const file of files) {
          const result = await webUpload(file.url, 'mentor');
          
          // 更新文件状态为 done，并保持图标
          const index = this.data.fileLists.findIndex(item => item.url === file.url)
          if (index > -1) {
            const newFileList = [...this.data.fileLists]
            newFileList[index] = {
              ...newFileList[index],
              status: 'done',
              url: result.url,
              icon: this.iconType(file.file.name)  // 确保更新后也有图标
            }
            this.setData({
              fileLists: newFileList
            })
            this.triggerEvent('after-upload', {
              file: newFileList[index],
              fileLists: newFileList
            })
            this.change()
          }
        }
      } catch (error) {
        // 上传失败时，直接移除所有状态为 uploading 的文件
        const newFileList = this.data.fileLists.filter(item => item.status !== 'uploading')
        this.setData({
          fileLists: newFileList
        })
        
        console.error('上传失败详细信息:', error)
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        })
        this.triggerEvent('after-upload', {
          success: false
        })
      } finally {
        // 隐藏 loading
        wx.hideLoading()
        this.setData({
          isUploading: false
        })
      }
    },

    async handleDelete(e) {
      const {
        index
      } = e.currentTarget.dataset
      const file = this.data.fileLists[index]

      if (this.properties.beforeDelete) {
        const shouldDelete = await this.triggerEvent('before-delete', {
          file,
          index
        })
        if (!shouldDelete) return
      } else {
        const res = await wx.showModal({
          title: '提示',
          content: '确定要删除吗？'
        })
        if (!res.confirm) return
      }

      const newFileList = this.data.fileLists.filter((_, i) => i !== index)
      this.setData({
        fileLists: newFileList
      })
      this.triggerEvent('delete', {
        file,
        index
      })
      this.change()
    },

    change() {
      this.formatList()
      this.triggerEvent('change', {
        fileLists: this.data.fileLists
      })
    },

    formatList() {
      const formattedList = this.data.fileLists.map(item => {
        const name = item.file.name
        if (this.isImg(name)) {
          return {
            ...item,
            type: 'img'
          }
        } else if (this.isFile(name)) {
          return {
            ...item,
            type: 'file',
            name
          }
        } else if (this.isVoice(name)) {
          return {
            ...item,
            type: 'voice',
            name
          }
        }
        return item
      })

      this.setData({
        fileLists: formattedList
      })
    }
  }
})