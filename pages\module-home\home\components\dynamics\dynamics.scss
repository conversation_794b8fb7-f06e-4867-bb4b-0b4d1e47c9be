/* pages/appoint/mentor-appoint/components/dynamics/dynamics.wxss */

/* 动态列表样式 */
.dynamics-container {
  box-sizing: border-box;
  padding: 24rpx 36rpx 0;
  ::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
  }
  .dynamics-list {
    box-sizing: border-box;
    .dynamics-item {
      padding: 30rpx 30rpx 28rpx;
      margin-bottom: 24rpx;
      background-color: #fff;
      border-radius: 12rpx;
      box-shadow: 0 0 33rpx 0 rgba(99, 97, 155, 0.1);
      .user-info {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;

        .left {
          display: flex;
          align-items: center;
          flex: 1;

          .avatar {
            width: 74rpx;
            height: 74rpx;
            border-radius: 50%;
            margin-right: 16rpx;
            flex-shrink: 0;
          }

          .info {
            .name {
              font-size: 28rpx;
              font-weight: 500;
              color: #333;
              line-height: 41rpx;
              display: flex;
              align-items: center;
              margin:0 24rpx 8rpx 0;
            }

            .title {
              font-size: 24rpx;
              color: #737783;
              view{
                text-align: left;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
              }
            }
          }
        }

        .right {
          // position: absolute;
          // top: 0;
          // right: 0;
          // background-color: rgba(0, 87, 255, 0.80);
          // width: 182rpx;
          // height: 56rpx;
          // border-radius: 0rpx  0rpx  0rpx  24rpx;
          // display: inline-block;
          // line-height: 56rpx;
          // text-align: center;
          image {
            width: 32rpx;
            height: 32rpx;
            margin-right: 10rpx;
            vertical-align: middle;
            margin-bottom: 5rpx;
          }

          text {
            font-size: 24rpx;
            height: 42rpx;
            line-height: 42rpx;
            color: #FFFFFF;
          }
        }
        .interest {
          background-color:rgba(158, 163, 172, 0.80);
        }
      }

      // .content {
      //   font-size: 26rpx;
      //   color: #737783;
      //   line-height: 36rpx;
      //   margin-bottom: 18rpx;
      //   display: -webkit-box;
      //   -webkit-box-orient: vertical;
      //   -webkit-line-clamp: 4;
      //   overflow: hidden;
      //   text-overflow: ellipsis;
      // }

      .media-list {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10rpx;
        margin-bottom: 20rpx;
        .media-item {
          width: 100%;
          height: 200rpx;
          border-radius: 8rpx;
          object-fit: cover;
        }
      }

      .video-container {
        background: rgba(0, 87, 255, 0.1);
        padding: 16rpx 32rpx;
        margin-bottom: 20rpx;
        .video-wrapper {
          display: flex;
          align-items: center;
          .video-card {
            position: relative;
            width: 244rpx;
            height: 136rpx;
            margin-right: 16rpx;
            .video-cover {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 8rpx;
            }

            .play-btn {
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
          }

          .video-info {
            .video-title {
              font-size: 28rpx;
              font-weight: bolder;
              color: #333333;
              line-height: 33rpx;
              margin-bottom: 10rpx;
            }

            .video-desc {
              font-size: 24rpx;
              color: #9ea3ac;
            }
          }
        }
      }
      .video-dynamicVideo{
        position: relative;
        // video{
        //   width: 100rpx;
        //   height: 100rpx;
        // }
        // 添加播放按钮样式
        .van-icon-play-circle-o {
          position: absolute;
          top: 50% !important;
          left: 46%;
          transform: translate(-50%, -50%);
          color: #fff;
          background-color: transparent !important;
          z-index: 1;
          border-radius: 8rpx;
        }
      }

      .audio-container {
        margin-bottom: 20rpx;

        .audio-player {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .play-control {
            flex: 1;
            display: flex;
            align-items: center;
            margin-right: 24rpx;
            background: #4584ff;
            border-radius: 0rpx 24rpx 24rpx 24rpx;
            padding: 10rpx 22rpx;
            // box-shadow: 0 0 38rpx 0 #aecaff;
            .play-btn-wrapper {
              position: relative;
              width: 56rpx;
              height: 56rpx;
              margin-right: 16rpx;
              display: flex;
              align-items: center;
              justify-content: center;

              &::after {
                content: '';
                position: absolute;
                top: -20rpx;
                left: -20rpx;
                right: -20rpx;
                bottom: -20rpx;
                // 可以添加下面的代码来查看点击区域
                // background-color: rgba(255, 0, 0, 0.2);
              }
            }

            .play-btn {
              width: 56rpx;
              height: 56rpx;
              z-index: 1; // 确保图标在伪元素之上
            }

            .progress-bar {
              flex: 1;

              // .progress{
              .custom-button {
                width: 100rpx;
                height: 100rpx;
                background-color: transparent;
                border-radius: 50%;
                box-sizing: border-box;
                position: relative;
              }
              .custom-button::after {
                content: "";
                position: absolute;
                top: 50%;
                left: 50%;
                width: 26rpx;
                height: 26rpx;
                background-color: #fff;
                border-radius: 50%;
                transform: translate(-50%, -50%);
              }
              // }
            }

            .duration {
              color: #ffffff;
              font-size: 28rpx;
              flex-shrink: 0;
              margin-left: 12rpx;
            }
          }

          .audio-info {
            display: flex;
            align-items: flex-end;

            .headphone-icon {
              width: 47rpx;
              height: 47rpx;
              margin-right: 8rpx;
            }

            .play-count {
              color: #737783;
              font-size: 24rpx;
            }
          }
        }
      }

      .interaction {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 20rpx;
        border-top: 1px solid #EAEAEB;
        .date {
          font-size: 24rpx;
          color: #9ea3ac;
          display: flex;
          align-items: center;
          image {
            width: 32rpx;
            height: 32rpx;
            margin-left: 5rpx;
          }
        }
        .examine{
          display: flex;
          align-items: center;
          text{
            color: #9EA3AC;
            font-size: 24rpx;
          }
          image{
            width: 24rpx;
            height: 24rpx;
            margin-left: 8rpx;
          }
          .prompt-fail{
            color: #FF4040;
          }
        }

        .stats {
          display: flex;
          gap: 30rpx;

          .stat-item {
            display: flex;
            align-items: center;

            .icon {
              width: 32rpx;
              height: 32rpx;
              margin-right: 8rpx;
            }

            text {
              font-size: 24rpx;
              color: #8c919a;
            }
          }
        }
      }
    }
  }
  .all {
    width: 186rpx;
    height: 44rpx;
    color: #0057ff;
    border: 2rpx solid #0057ff;
    font-size: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 24rpx auto 72rpx;
    border-radius: 8rpx;
  }
  .comment-operation {
    margin-left: 12rpx;
    transition: all linear 0.2s;
    padding: 6rpx 10rpx;
    background: #0057ff;
    border-radius: 76rpx;
    position: relative;
    color: rgb(255, 255, 255);
    font-size: 24rpx;
    font-weight: 500;
    line-height: 28rpx;
    display: flex;
    align-items: center;

    .circle {
      width: 28rpx;
      height: 28rpx;
      border-radius: 50%;
      margin-left: 4rpx;
      background: #fff;
    }
  }

  .comment-operation-down {
    background: rgb(212, 212, 212);
    color: rgb(255, 255, 255);

    .circle {
      margin-left: 0;
      margin-right: 4rpx;
    }
  }
}

.content {
  .content-text {
    word-break: break-all;
    font-size: 26rpx;
    color: #737783;
    line-height: 36rpx;
    margin-bottom: 20rpx;
    &.text-ellipsis {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
      overflow: hidden;

      &.is-expanded {
        -webkit-line-clamp: initial;
        overflow: visible;
      }
    }
  }

  .expand-btn {
    color: #0057ff;
    font-size: 26rpx;
    text-align: right;

    .arrow-up {
      transform: rotate(180deg);
      transition: transform 0.3s ease;
    }

    van-icon {
      transition: transform 0.3s ease;
    }
  }
}
.video-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 1);
  z-index: 999;

  .video-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-btn {
    position: fixed;
    top: 40rpx;
    left: 40rpx;
    z-index: 1000;
    width: 60rpx;
    height: 60rpx;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
