<view class="personal-letter-list">
  <scroll-view class="scrollarea" scroll-y type="list" bindscrolltolower="loadMore">
    <view class="list-box">
      <view class="list" wx:for="{{ messageList }}" wx:key="{{ index }}" bind:tap="toLetterDetail" data-index="{{ index }}">
        <view class="left">
          <image src="{{item.fromUserAvatar}}" mode="aspectFill" />
        </view>
        <view class="right">
          <view class="top">
            <view class="name textoverflow">{{ item.fromUserName }}</view>
            <view class="time">{{ item.createdAt }}</view>
          </view>
          <view class="bottom">
            <view class="txt textoverflow">{{ item.showText }}</view>
            <!-- <view class="txt textoverflow" v-if="/\.(doc|docx|pdf|xls|xlsx|ppt|pptx)$/.test(item.content)">[文件]</view> -->
            <!-- <view class="txt textoverflow" v-else-if="/\.(jpg|jpeg|png)$/.test(item.content)">[图片]</view>
            <view class="txt textoverflow" v-else>{{ item.content }}</view> -->
            <!-- <view class="number" v-if="item.unReadCount > 0"> -->
              <!-- {{ item.unReadCount > Number(99) ? '99+' : item.unReadCount }} -->
            <view class="number" wx:if="{{item.unReadCount && item.unReadCount > 0}}">
              {{ item.unReadCount > 99 ? '99+' : item.unReadCount }}
            </view>
          </view>
        </view>
      </view>
      <view class="finshed-text" wx:if="{{ finished }}">没有更多了</view>
    </view>
  </scroll-view>
</view>
