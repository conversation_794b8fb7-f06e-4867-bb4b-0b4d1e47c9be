export const educationLevelFilter = (value) => {
	const educationList = [
		{
			label: '高中',
			value: 10
		},
		{
			label: '大专',
			value: 20
		},
		{
			label: '本科',
			value: 30
		},
		{
			label: '硕士',
			value: 40
		},
		{
			label: '博士',
			value: 50
    },
		{
			label: '博士后',
			value: 60
		},
  ]
  return value ? educationList.find(item => item.value === value)?.label : ''
}

export const positionCategoryFilter = (value) => {
	const positionList = [
		{
			label: '全职',
			value: 10
		},
		{
			label: '兼职',
			value: 20
		},
		{
			label: '实习',
			value: 30
		},
		{
			label: '见习',
			value: 40
		}
  ]
  return value ? positionList.find(item => item.value === value)?.label : ''
}

export const enterpriseCategoryFilter = (value) => {
	const enterpriseCategoryList = [
		{
			label: '央企',
			value: 10
		},
		{
			label: '国企',
			value: 20
		},
		{
			label: '外资企业',
			value: 30
		},
		{
			label: '合资企业',
			value: 40
		},
		{
			label: '民营企业',
			value: 50
		},
		{
			label: '政府机关/事业单位',
			value: 60
		},
		{
			label: '非盈利机构',
			value: 70
		},
		{
			label: '其他',
			value: 80
		}
  ]
  return value ? enterpriseCategoryList.find(item => item.value === value)?.label : ''
}
