// pages/mentor-in/education/education.js
import { getMentorEducationList } from '~/api/user'
import { educationLevelFilter } from '~/utils/filter'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    educationList: []
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    
  },
  async getMentorEducationList() {
    const { data } = await getMentorEducationList()
    if (data.length > 0) {
      data.forEach(item => {
        item.startDate = item.startDate.split('-')[0] + '-' + item.startDate.split('-')[1]
        item.endDate = item.endDate.split('-')[0] + '-' + item.endDate.split('-')[1]
        item.educationLevelText = educationLevelFilter(item.educationLevel)
      })
    }
    // this.educationList = data || []
    this.setData({
      educationList: data || []
    })
  },
  editExp(event) {
    if ((event.currentTarget.dataset.id ?? '') === '') {
      wx.navigateTo({
        url: `/pages/mine/education-edit/education-edit`,
      })
    } else {
      wx.navigateTo({
        url: `/pages/mine/education-edit/education-edit?id=${event.currentTarget.dataset.id}`,
      })
    }
  },
  pageBack() {
    wx.navigateBack()
  },
  pageNext() {
    if (this.data.educationList.length <= 0) {
      wx.showToast({
        title: '请至少添加一段教育经历',
        icon: 'none'
      })
      return
    }
    wx.navigateTo({
      url: '/pages/mentor-apply/mentor-apply-process/work/work',
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getMentorEducationList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})