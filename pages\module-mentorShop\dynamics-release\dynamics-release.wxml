<!--pages/module-mentorShop/dynamics-release/dynamics-release.wxml-->
<video id="myVideoLoop" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/%E5%80%9F%E6%A2%A6.mp4" autoplay loop controls="{{false}}" muted show-center-play-btn="{{false}}" />
<scroll-box focusHideBottom="{{true}}" backgroundColor='#f8f8f8'>
  <view slot="content" class="dynamics-release">
    <view class="dynamics-text">
      <!-- <view class="input-area"> -->
      <van-field value="{{ dynamicContent }}" type="textarea" placeholder="这一刻的想法..." autosize="{{ autosize }}" input-class="textarea" bindinput="handleInput" border="{{ false }}" maxlength="3000" />
      <!-- <textarea placeholder="这一刻的想法..." placeholder-class="placeholder" maxlength="3000" bindinput="handleInput" value="{{dynamicContent}}" auto-height /> -->
      <!-- </view> -->

      <view class="media-upload">
        <view class="media-list">
          <block wx:if="{{mediaType === 'image'}}">
            <block wx:for="{{dynamicImages}}" wx:key="index">
              <view class="media-item" bindtap="previewImage" data-img="{{item}}">
                <image src="{{item}}" mode="aspectFill" />
                <view class="delete-btn" catchtap="deleteMedia" data-index="{{index}}">×</view>
              </view>
            </block>
          </block>

          <block wx:if="{{mediaType === 'video'}}">
            <view class="media-item">
              <block wx:if="{{dynamicVideo}}">
                <video id='myVideo' src="{{dynamicVideo}}" show-center-play-btn="{{false}}" mode="aspectFill" object-fit="cover" controls="{{false}}" style="width: 100%; height: 100%;"></video>
                <van-icon name="play-circle-o" size='40px' catch:tap="previewVideo" />
                <view class="delete-btn" catchtap="deleteVideo">×</view>
              </block>
              <view class="mask" wx:if="{{isUploading}}">
                <van-loading color="#fff" />
                <view>上传中...</view>
              </view>
            </view>
          </block>

          <view class="upload-box" bindtap="chooseMedia" wx:if="{{(mediaType === '' || mediaType === 'image') && dynamicImages.length < 9 }}">
            <van-icon name="plus" color='#0057FF' size='30px' />
          </view>
          <view class="media-empty"></view>
        </view>
      </view>
      <view class="word-count">{{currentLength}}/3000</view>
    </view>

    <view class="voice-btn" bindtap="onVoiceEvaluate" wx:if="{{!audioPlayObject.audioSrc}}">
      <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/voice-icon.png" mode="aspectFit" />
      <text>添加语音</text>
    </view>
    <!-- 保存录音后显示音频播放器 -->
    <view class="audio-player" wx:else>
      <view class="play-control">
        <image class="play-btn" bindtap="recordClick" src="{{audioPlayObject.audioPlayStatus ? 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/pause.png' : 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/play.png'}}"></image>
        <view class="progress-bar">
          <van-slider custom-class="progress" use-button-slot active-color="#0057FF" inactive-color="rgb(231, 231, 231)" value="{{audioPlayObject.audioProgress}}" bind:change="audioChange">
            <view class="custom-button" slot="button"></view>
          </van-slider>
        </view>
        <text class="duration">{{audioPlayObject.audioDuration}}</text>
      </view>
      <view class="audio-info" bindtap="deleteAudio">
        <van-icon name="delete-o" size="30px" color='#4584ff' />
      </view>
    </view>

  <!-- 预览层 -->
  <view class="video-preview" wx:if="{{showPreviewVideo}}">
    <view class="video-container">
      <view class="close-btn" catch:tap="closePreview">
        <van-icon name="cross" size="40rpx" color="#fff" />
      </view>
      <video id="previewVideo" src="{{dynamicVideo}}" show-center-play-btn='{{false}}' show-background-playback-button='{{false}}' show-play-btn="{{true}}" controls autoplay style="width: 100vw; height: 100vh; position: fixed; top: 0; left: 0;"></video>
    </view>
  </view>

  </view>
  <view class="course-bottom" slot="bottom">
    <van-action-sheet close-on-click-overlay="{{false}}" show="{{ show }}" title="语音评价" bind:close="onCloseVoice">
      <view class="voice-record-content">
        <view class="timer">{{ tempAudioObject.audioDuration || '00:00.00' }}</view>
        <view class="timer-tip">{{ recordTip }}</view>

        <!-- 录音状态 -->
        <block wx:if="{{!tempAudioObject.audioSrc}}">
          <view class="mic-btn {{ isRecording ? 'recording' : '' }}" bindtap="toggleRecord">
            <view class="mic-content">
              <image wx:if="{{isRecording}}" class="wave" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/wave.gif" mode="" />
              <image class="mic-icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/Recording%20-%20In%20recording.png" mode="aspectFit" />
              <image wx:if="{{isRecording}}" class="wave" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/wave.gif" mode="" />
            </view>
            <text>{{ isRecording ? '点击结束录制' : '点击开始录制' }}</text>
          </view>
        </block>

        <!-- 录音完成状态 -->
        <block wx:else>
          <view class="control-buttons">
            <view class="control-btn" bindtap="reRecord">
              <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/Recording%20-%20Re%20recording.png" mode="aspectFit" />
              <text>重新录制</text>
            </view>
            <view class="control-btn control-pause" bindtap="togglePlay">
              <!-- <image src=" https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/Recording%20-%20{{isPlaying ? 'Audition' : 'Pause'}}.png" mode="aspectFit" /> -->
              <image wx:if="{{!isPlaying}}" src=" https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/Recording%20-%20Pause.png" mode="aspectFit" />
              <van-circle wx:else value="{{ value }}" stroke-width="3" size="{{circleSize}}" layer-color="#BCDAFC" color="#0057FF">
                <view class="van-circle-content">
                  <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/sound-recording.png" alt="" />
                </view>
              </van-circle>
              <text>{{ isPlaying ? '试听中' : '点击试听' }}</text>
            </view>
            <view class="control-btn" bindtap="saveRecord">
              <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/Recording%20-%20Save.png" mode="aspectFit" />
              <text>保存</text>
            </view>
          </view>
        </block>

        <!-- 进度条 -->
        <!-- <view class="progress-bar {{ isRecording || isPlaying ? 'active' : '' }}" style="{{progressStyle}}"></view> -->
      </view>
    </van-action-sheet>
    <van-button disabled="{{submitDisabled}}" bindtap="submitDynamic">发表</van-button>
  </view>
</scroll-box>