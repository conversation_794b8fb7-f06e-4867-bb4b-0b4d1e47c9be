.page{
  height: 100%;
}
.exp-edit{
  background: #FFF;
  height: 100%;
  display: flex;
  flex-direction: column;
  .exp-content{
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    padding: 28rpx 36rpx 24rpx;
    .experience-edit{
      flex: 1;
      width: 100%;
      .experience-edit-box{
        border-radius: 12rpx;
        padding: 0 30rpx;
        box-shadow: 0px 2px 16px 0px rgba(99, 97, 155, 0.1);
        .van-cell{
          padding: 24rpx 0;
          border-bottom: 2rpx solid #dfdfdf;
          .van-cell__title{
            width: 200rpx !important;
            margin: 0 !important;
            max-width: 200rpx !important;
            min-width: 200rpx !important;
            font-weight: 400;
          }
        }
        .custom-textarea{
          border: none;
          flex-direction: column;
          padding-top: 24rpx;
          padding-bottom: 30rpx;
          .van-field__control{
            margin-top: 8rpx;
            background: rgb(248, 249, 254);
            border-radius: 12rpx;
            padding: 26rpx;
          }
          &::after{
            display: none;
          }
        }
        van-field{
          &:nth-last-child(1) {
            .van-cell{
              border: none;
            }
          }
        }
      }
    }
  }
  .exp-footer{
    box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0,0,0,0.05);
    background: #FFF;
    height: 160rpx;
    padding: 24rpx 36rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    van-button{
      overflow: hidden;
      height: 100%;
      flex: 1;
      margin-right: 40rpx;
      button{
        font-size: 36rpx;
        // line-height: 48rpx;
        border-radius: 120rpx;
        width: 100%;
        height: 100%;
      }
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .default-btn{
      border: 1px solid #0057FF;
      background: #fff;
      color: #0057FF;
    }
    .info-btn{
      border: 1px solid #0057FF !important;
      background: #0057FF !important;
      color: rgb(255, 255, 255) !important;
    }
  }
}