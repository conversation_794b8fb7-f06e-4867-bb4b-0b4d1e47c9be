const app = getApp();
let lastNavigateTime = 0;
const MIN_INTERVAL = 300; // ms 防止连续跳转

/**
 * 构建带参数的完整 URL（支持对象自动 JSON 序列化）
 * @param {string} path 页面路径，如 '/pages/index/index'
 * @param {Object} query 参数对象
 * @returns {string}
 */
function buildUrl(path, query = {}) {
  const queryStr = Object.keys(query)
    .filter(k => query[k] != null) // 过滤 null 和 undefined
    .map(k => {
      const val = typeof query[k] === 'object' ? JSON.stringify(query[k]) : query[k];
      return `${k}=${encodeURIComponent(val)}`;
    })
    .join('&');

  return queryStr ? `${path}?${queryStr}` : path;
}

/**
 * 修正页面路径，确保以 / 开头
 * @param {string} path
 */
function normalizePath(path) {
  return path.startsWith('/') ? path : `/${path}`;
}

/**
 * 安全裁剪 pageList，防止越界
 * @param {number} delta
 */
function trimPageList(delta) {
  const list = app?.globalData?.pageList;
  if (!Array.isArray(list)) return;
  const newLength = Math.max(0, list.length - delta);
  app.globalData.pageList = list.slice(0, newLength);
}

/**
 * 智能页面跳转函数 - 自动处理页面栈溢出问题
 * @param {string} path 页面路径（不带参数）
 * @param {Object} query 路由参数对象
 * @param {'navigateTo'|'redirectTo'|'reLaunch'|'switchTab'|'auto'} method 跳转方式，默认 'auto'
 * @param {boolean} forceRedirect 是否强制使用 redirectTo
 */

/**
    navigateAndAddPage('/pages/detail/index', { id: 1001 }); 简单跳转
    navigateAndAddPage('/pages/detail/index', { id: 1001 }, 'navigateTo'); 普通跳转
    navigateAndAddPage('/pages/order/pay', {}, 'auto', true); 强制 redirectTo（页面栈未满也强跳）
    navigateAndAddPage('/pages/search/result', {
      keyword: '夏日',
      filter: { type: 'tag', tags: ['热门', '推荐'] }
    }); 跳转传对象参数
*/

export function navigateAndAddPage(path, query = {}, method = 'auto', forceRedirect = false) {
  const now = Date.now();
  if (now - lastNavigateTime < MIN_INTERVAL) {
    // console.warn('跳转过快，已阻止');
    return;
  }
  lastNavigateTime = now;

  const fullPath = normalizePath(path);
  const url = buildUrl(fullPath, query);
  const pages = getCurrentPages();
  const pageStackLength = pages.length;
  // const currentPage = pages[pageStackLength - 1];

  // 判断是否重复跳转同一页面（只比 route）
  // if (currentPage?.route === fullPath.slice(1)) {
  //   console.log('已在当前页面，无需跳转');
  //   return;
  // }

  // 记录访问历史
  if (typeof app?.globalData?.addPage === 'function') {
    app.globalData.addPage(url);
  }

  // 明确指定跳转方式
  if (method !== 'auto') {
    if (typeof wx[method] === 'function') {
      wx[method]({
        url,
        fail: err => {
          console.error(`跳转失败(${method}):`, err);
          // wx.showToast({ title: '跳转失败，请重试', icon: 'none' });
        }
      });
    } else {
      console.warn(`不支持的跳转方法: ${method}`);
    }
    return;
  }

  // 智能判断跳转方式
  if (pageStackLength >= 9 || forceRedirect) {
    // console.log(`页面栈深(${pageStackLength})，使用 redirectTo 跳转: ${url}`);
    wx.redirectTo({ url });
  } else {
    // console.log(`页面栈安全(${pageStackLength})，使用 navigateTo 跳转: ${url}`);
    wx.navigateTo({ url });
  }
}

/**
 * 普通返回上一页
 * @param {number} delta 返回页数
 */
export function navigateBack(delta = 1) {
  wx.navigateBack({ delta });
  trimPageList(delta);
}

/**
 * 智能返回函数 - 如果没有上一页则跳转到首页
 * @param {number} delta 返回页数
 * @param {string} homePage 首页路径
 */
export function safeNavigateBack(delta = 1, homePage = '/pages/index/index') {
  const pages = getCurrentPages();
  if (pages.length > delta) {
    wx.navigateBack({ delta });
    trimPageList(delta);
  } else {
    const home = normalizePath(homePage);
    // console.log(`页面栈不足，重定向到首页: ${home}`);
    wx.reLaunch({ url: home });
    if (Array.isArray(app?.globalData?.pageList)) {
      app.globalData.pageList = [];
    }
  }
}
