/* pages/module-mentorShop/add-column/add-column.wxss */
page {
  background-color: #f8f8f8;
  padding: 30rpx 36rpx;
  height: 100%;
}

.video-section {

  .empty {
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 180rpx 0;

    image {
      width: 220rpx;
      height: 220rpx;
      margin-bottom: 28rpx;
    }

    text {
      color: #333333;
      font-size: 26rpx;
      font-weight: bold;
    }
  }
}

.course {
  border-radius: 16rpx;
  box-shadow: 0px 0px 16px 0px rgba(99, 97, 155, 0.1);
  background: #fff;
  padding: 30rpx 30rpx;
  height: 89%;

  .tab-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2rpx solid #c4c4c4;
    padding-top: 20rpx;

    view:nth-child(1) {
      display: flex;
    }

    .filter {
      color: #777777;
      font-size: 24rpx;
      position: relative;
      display: flex;
      align-items: center;

      .van-icon {
        margin-left: 4rpx;
        transition: transform 0.3s ease;
      }

      .filter-dropdown {
        position: absolute;
        top: calc(100% + 20rpx);
        right: 0;
        width: 120rpx;
        background: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        border-radius: 4px;
        z-index: 100;
        display: none;

        &.show {
          display: block;
        }

        .filter-item {
          display: flex;
          align-items: center;
          justify-content: space-around;
          padding: 12rpx;
          font-size: 24rpx;
          color: #777777;
          margin: 13rpx;
          background-color: #f8f9fe;
          border-radius: 8rpx;

          &.active {
            color: #0057ff;
            border: 2rpx solid #0057ff;
          }

          &:hover {
            background: #f5f5f5;
          }
        }
      }
    }

    .tab {
      position: relative;
      padding: 10rpx 0;
      margin-right: 48rpx;
      font-size: 30rpx;
      color: #797979;

      &.active {
        color: #0057ff;
        font-weight: bold;

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 48rpx;
          height: 4rpx;
          background: #0057ff;
          border-radius: 26rpx;
        }
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
  .box {
    .course-itema {
      display: flex;
      align-items: center;
      .course-item {
        border-bottom: 2rpx solid #c4c4c4;
      }
      &:last-child {
        .course-item {
          border-bottom: none;
          flex: 1;
        }
      }
    }
  }

  .van-popup--bottom.van-popup--safe {
    padding-bottom: 0;
  }

  .van-action-sheet {
    bottom: 194rpx;

    .empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 42rpx 0 94rpx;

      image {
        width: 236rpx;
        height: 192rpx;
        margin-bottom: 24rpx;
      }

      text {
        color: #aaaaaa;
        font-size: 34rpx;
      }
    }

    .item-box {
      border-bottom: 2rpx solid #dfdfdf;
    }
  }

  .course-list {
    max-height: 60vh;
  }

  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 8;
    height: 194rpx;

    .association {
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 4rpx 0 20rpx 0 rgba(0, 0, 0, 0.05);
    }

    .selected-count {
      font-size: 30rpx;
      color: #0057ff;
    }

    .association {
      padding: 58rpx 40rpx;
    }

    .confirm-btn {
      width: 278rpx;
      height: 70rpx;
      background: #0057FF;
      border-radius: 36rpx;
      color: #ffffff;
      font-size: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        opacity: 0.9;
      }
    }
  }
}
