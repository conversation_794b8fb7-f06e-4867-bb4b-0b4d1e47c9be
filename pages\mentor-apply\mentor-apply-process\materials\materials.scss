.materials{
  // padding: 24rpx 36rpx;
  .materials-upload{
    background: #FFFFFF;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    padding: 28rpx 30rpx;
    box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(99,97,155,0.1);
    .title{
      font-weight: bold;
      font-size: 30rpx;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .text{
      font-weight: 400;
      font-size: 24rpx;
      color: #979797;
      line-height: 40rpx;
      text-align: justified;
      font-style: normal;
      text-transform: none;
      margin-top: 16rpx;
    }
    .quetion-file{
      margin-top: 26rpx;
    }
  }
  .commitment{
    margin-top: 30rpx;
    .title{
      font-weight: bold;
      font-size: 30rpx;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 16rpx;
    }
    .text{
      font-weight: 400;
      font-size: 22rpx;
      color: #979797;
      line-height: 40rpx;
      text-align: justified;
      font-style: normal;
      text-transform: none;
    }
  }
  .agreement{
    margin-top: 16rpx;
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 13px;
    color: #999999;
    line-height: 15px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    width: 100%;
    justify-content: flex-end;
    .van-checkbox__label{
      padding-left: 12rpx;
    }
    // .van-checkbox{
    //   display: flex;
    //   align-items: center;
    //   .label-class{
    //     margin-left: 12rpx;
    //   }
    //   .van-checkbox__icon{
    //     width: 28rpx;
    //     height: 28rpx;
    //     font-size: 20rpx;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //   }
    // }
    .text-value{
      font-weight: 400;
      font-size: 13px;
      color: #999999;
      line-height: 15px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    text{
      color: #0057FF;
    }
  }
}
.qrcode-dialog{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  border-radius: 0px 0px 0px 0px;
  z-index: 99;
  .qrcode-dialog-content{
    z-index: 99;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 678rpx;
    height: 632rpx;
    background: #FFFFFF;
    border-radius: 32rpx 32rpx 32rpx 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 36rpx 36rpx;
    .title{
      font-weight: bold;
      font-size: 34rpx;
      color: #333333;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .qrcode{
      margin-top: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      image{
        width: 260rpx;
        height: 260rpx;
      }
    }
    .tips{
      font-weight: 500;
      font-size: 30rpx;
      color: #919BAE;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-top: 30rpx;
    }
    .btns{
      margin-top: 46rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      van-button{
        overflow: hidden;
        height: 100%;
        flex: 1;
        margin-right: 50rpx;
        button{
          font-size: 30rpx;
          // line-height: 48rpx;
          border-radius: 120rpx;
          width: 278rpx;
          height: 70rpx;
        }
        &:nth-last-child(1) {
          margin-right: 0;
        }
      }
      .default-btn{
        border: 1px solid #0057FF;
        background: #fff;
        color: #0057FF;
      }
      .info-btn{
        border: 1px solid #0057FF !important;
        background: #0057FF !important;
        color: rgb(255, 255, 255) !important;
      }
    }
  }
}