import { getMentorWorkList } from '~/api/user'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    workList: [],
  },
  async getMentorWorkList() {
    const { data } = await getMentorWorkList()
    if (data.length > 0) {
      data.forEach(item => {
        if (item.endDate === '9999-01-01 00:00:00' || item.endDate === '9999-12-01 00:00:00') {
          item.endDate = '至今'
        } else {
          item.endDate = item.endDate.split('-')[0] + '-' + item.endDate.split('-')[1]
        }
        item.startDate = item.startDate.split('-')[0] + '-' + item.startDate.split('-')[1]
      })
    }
    this.setData({
      workList: data || []
    })
  },
  editExp(event) {
    if ((event.currentTarget.dataset.id ?? '') === '') {
      wx.navigateTo({
        url: `/pages/mine/work-edit/work-edit`,
      })
    } else {
      wx.navigateTo({
        url: `/pages/mine/work-edit/work-edit?id=${event.currentTarget.dataset.id}`,
      })
    }
  },  
  pageBack() {
    wx.navigateBack()
  },
  pageNext() {
    if (this.data.workList.length <= 0) {
      wx.showToast({
        title: '请至少添加一段工作经历',
        icon: 'none'
      })
      return
    }
    wx.navigateTo({
      url: '/pages/mentor-apply/mentor-apply-process/materials/materials',
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getMentorWorkList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})