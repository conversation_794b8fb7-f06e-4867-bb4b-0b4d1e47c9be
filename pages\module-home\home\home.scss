/* pages/module-home/home/<USER>/

page{
  height: 100%;
}
.home-page{
  height: 100%;
  display: flex;
  flex-direction: column;
  .home-page-content{
    overflow: auto;
    flex: 1;
    .page-home-header{
      padding: 32rpx 36rpx 48rpx;
      width: 100%;
      // height: 382rpx;
      background-image: url('https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/mentor-app/home-bg.png');
      background-size: 100%;
      .user-info{
        display: flex;
        align-items: center;
        overflow: auto;
        .user-left{
          width: 148rpx;
          height: 148rpx;
          margin-right: 20rpx;
          border-radius: 50%;
          // overflow: hidden;
          flex-shrink: 0;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .user-right{
          flex: 1;
          // overflow: auto;
          overflow: hidden;
          // display: flex;
          // align-items: center;
          .user-name{
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .user-name-content{
              flex: 1;
              margin-right: 24rpx;
              display: flex;
              .user-name-content-box{
                max-width: 100%;
                display: flex;
                align-self: flex-start;
                align-items: center;
                // width: 100%;
                .name{
                  // width: 100rpx;
                  flex: 1;
                  font-weight: bold;
                  font-size: 28rpx;
                  color: #FFFFFF;
                  line-height: 33rpx;
                  text-align: center;
                  font-style: normal;
                  text-transform: none;
                }
                .label{
                  // white-space: nowrap;
                  margin-left: 12rpx;
                  padding: 4rpx 8rpx;
                  background: linear-gradient( 180deg, #FFEE93 0%, #FFB830 100%);
                  border-radius: 0rpx 0rpx 0rpx 0rpx;
                  font-weight: 500;
                  font-size: 20rpx;
                  color: #0057FF;
                  line-height: 23rpx;
                  text-align: center;
                  font-style: normal;
                  text-transform: none;
                }
              }
            }
            .user-name-edit{
              display: flex;
              align-items: center;
              font-weight: 400;
              font-size: 26rpx;
              color: #FFFFFF;
              line-height: 30rpx;
              text-align: center;
              font-style: normal;
              image{
                width: 32rpx;
                height: 32rpx;
                margin-right: 8rpx;
              }
            }
          }
          .user-rate{
            margin: 18rpx 0;
            display: flex;
            align-items: center;
            image{
              width: 24rpx;
              height: 24rpx;
              margin-right: 4rpx;
            }
            text{
              margin-left: 4rpx;
              font-weight: 500;
              font-size: 20rpx;
              color: #FFFFFF;
              line-height: 23rpx;
              text-align: left;
              font-style: normal;
              text-transform: none;
            }
          }
          .user-company{
            width: 100%;
            font-weight: 500;
            font-size: 24rpx;
            color: #FFFFFF;
            line-height: 28rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .user-position{
            margin-top: 10rpx;
            width: 100%;
            font-weight: 500;
            font-size: 24rpx;
            color: #FFFFFF;
            line-height: 28rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
      }
      .user-grade{
        margin-top: 32rpx;
        background: #FFFFFF;
        box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(99,97,155,0.1);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        padding: 24rpx 0 22rpx;
        display: flex;
        align-items: center;
        .grade-item{
          flex: 1;
          flex-direction: column;
          align-items: center;
          .grade-header{
            font-weight: 400;
            font-size: 24rpx;
            color: #777777;
            line-height: 28rpx;
            text-align: center;
            font-style: normal;
            text-transform: none;
            .weight{
              font-weight: bold;
              font-size: 44rpx;
              color: #333333;
              line-height: 52rpx;
              text-align: left;
              font-style: normal;
              text-transform: none;
              margin-right: 6rpx;
            }
          }
          .grade-footer{
            margin-top: 18rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #777777;
            line-height: 28rpx;
            font-style: normal;
            text-transform: none;
            text-align: center;
            position: relative;
            &::after{
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              content: "";
              width: 2rpx;
              height: 20rpx;
              right: 0;
              background: rgb(165, 165, 165);
            }
          }
          &:nth-last-child(1) {
            .grade-footer{
              &::after{
                display: none;
              }
            }
          }
        }
      }
    }
    .page-home-data-box{
      width: 100%;
      background: #FFFFFF;
      border-radius: 16rpx 16rpx 0rpx 0rpx;
      margin-top: -16rpx;
      // padding: 0 36rpx 0;
      .page-tab{

      }
      .page-tabs{
        padding: 8rpx 0 0;
        .van-tabs__scroll--line {
          height: auto;
          border-color: #c4c4c4 !important;
        }
        .van-tabs__scroll {
          background-color: transparent;
          border-bottom: 2rpx solid #737783;
        }
        .van-tab {
          color: #797979;
          font-size: 30rpx;
        }
        .van-tab--active {
          color: #333333;
          font-size: 36rpx;
          font-weight: bolder;
        }
        // .van-tabs__wrap{
        //   border-bottom: 2rpx solid #737783 !important;
        // }
        .van-tabs__line{
          width: 68rpx;
          height: 0 !important;
          background-color: #0057FF;
          border-radius: 12rpx;
          border: 4rpx solid #0057FF;
          margin-bottom: 0 !important;
        }
        // .van-tab{
        //   box-sizing: border-box;
        //   padding-top: 28rpx;
        //   display: flex;
        //   align-items: flex-start;
        //   justify-content: center;
        //   height: 94rpx;
        // }
        // .van-ellipsis{
        //   font-weight: bold;
        //   font-size: 30rpx;
        //   color: #797979;
        //   line-height: 35rpx;
        //   text-align: center;
        //   font-style: normal;
        //   text-transform: none;
        // }
      }
    }
  }
  .home-page-footer{
    box-shadow: 4rpx 0rpx 20rpx 0rpx rgba(0,0,0,0.05);
    background: #FFF;
    height: 160rpx;
    padding: 24rpx 36rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    van-button{
      overflow: hidden;
      height: 100%;
      flex: 1;
      margin-right: 32rpx;
      button{
        font-size: 36rpx;
        // line-height: 48rpx;
        border-radius: 120rpx;
        width: 100%;
        height: 100%;
      }
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .default-btn{
      border: 1px solid linear-gradient( 171deg, #27E673 0%, #13CB89 100%);
      background: linear-gradient( 171deg, #27E673 0%, #13CB89 100%);
      color: #FFF;
      .message-number {
        color: #ffffff;
        font-size: 12px;
        height: 17px;
        line-height: 14px;
        background: #ff5d39;
        border-radius: 13px;
        padding: 0 6px;
        margin-left: 6px;
      }
    }
    .info-btn{
      border: 1px solid #0057FF;;
      background: #0057FF;;
      color: rgb(255, 255, 255) !important;
    }
  }
}
.loading{
  padding: 100rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.float-buttons {
  position: fixed;
  /* 默认位置改为由JS控制 */
  /* right: 20rpx;
  bottom: 80rpx; */
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  z-index: 100;
  touch-action: none; /* 防止浏览器默认行为 */

  .float-button {
    width: 100rpx;
    height: 100rpx;
    background: #4a90e2;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(to bottom, #647cf8, #428dfe);
    box-shadow: 8rpx 12rpx 38rpx 0px #a6b7fa;
    z-index: 101;
    position: relative;
  }

  .float-add {
    position: relative;

    .article,
    .video {
      position: absolute;
      transition: all 0.3s ease;
    }

    .article {
      right: 120rpx;
      bottom: 0;
    }

    .video {
      right: 70rpx;
      bottom: 120rpx;
    }
  }

  text {
    font-size: 26rpx;
    color: #ffffff;
  }
}