// pages/mine/topic-list/topic-list.js
import { getMentorTopicList, deleteMentorTopic } from '~/api/user'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    topicList: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    
  },
  getHtmlValue(value) {
    const checkOption = value.replace(/\n/g, '<br/>')
    return checkOption
  },
  async getMentorTopicList() {
    const { data } = await getMentorTopicList()
    // this.topicList = data
    if (data && data.length > 0) {
      data.forEach(item => {
        item.nodeValue = this.getHtmlValue(item.content)
      })
    }
    this.setData({
      topicList: data
    })
  },
  editExp(event) {
    if ((event.currentTarget.dataset.id ?? '') === '') {
      wx.navigateTo({
        url: `/pages/mine/topic-edit/topic-edit`,
      })
    } else {
      wx.navigateTo({
        url: `/pages/mine/topic-edit/topic-edit?id=${event.currentTarget.dataset.id}`,
      })
    }
  },
  topicDelte(event) {
    const id = event.currentTarget.dataset.id
    const that = this
    wx.showModal({
      title: '确定删除话题？',
      content: '删除话题后已预约的沟通过仍可继续',
      async success (res) {
        if (res.confirm) {
          const { success } = await deleteMentorTopic({
            mentorTopicId: id
          })
          if (success) {
            wx.showToast({
              title: '删除成功',
              icon: 'none'
            })
            that.getMentorTopicList()
          }
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getMentorTopicList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})