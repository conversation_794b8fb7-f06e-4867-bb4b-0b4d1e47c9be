// pages/module-mentorShop/add-course/add-course.js
import moment from 'moment'
import { getMentorCourseList, addCourseToColumn } from '~/api/mentorShop';
import {  navigateBack } from '~/utils/pageNavigation';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    courseType: 10,//10视频、20图文
    activeTab: 'video', // 默认选中视频标签
    result: [], // 存储选中的id数组
    selectedItems: [], // 新增：存储选中的完整item数据
    show: false,
    courses: [],
    videoCourses: [], // 存储视频课程
    articleCourses: [], // 存储图文课程
    page: {
      pageSize: 15,
      pageNumber: 1,
    },
    videoPage: {
      pageSize: 15,
      pageNumber: 1,
    },
    articlePage: {
      pageSize: 6,
      pageNumber: 1,
    },
    finished: false,
    columnsId: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      columnsId: options.columnsId
    }, () => {
      this.getMentorCourseList();
    })
  },

  async getMentorCourseList() {
    if (this.data.loading) return;

    this.setData({
      loading: true,
    });

    const currentPage = this.data.courseType === 10 ? this.data.videoPage : this.data.articlePage;

    const {
      data
    } = await getMentorCourseList({
      ...currentPage,
      courseType: this.data.courseType,
      columnsId: this.data.columnsId
    });

    // 过滤掉 columnsRelate 为 true 的课程
    const filteredData = data?.filter(item => !item.columnsRelate);

    // 使用过滤后的数据长度来判断是否加载完成
    if (filteredData.length < currentPage.pageSize) {
      this.setData({
        finished: true,
      });
    }

    const formattedData = filteredData?.map(item => ({
      ...item,
      courseDuration: item.courseDuration ? moment.utc(item.courseDuration * 1000).format('HH:mm:ss') : '00:00:00'
    }));

    const isVideo = this.data.courseType === 10;
    const currentList = isVideo ? this.data.videoCourses : this.data.articleCourses;
    const newList = currentPage.pageNumber === 1 ? formattedData : [...currentList, ...formattedData];

    this.setData({
      [isVideo ? 'videoCourses' : 'articleCourses']: newList,
      courses: newList,
      loading: false
    });
  },

  // 切换标签页
  switchTab(e) {
    const type = e.currentTarget.dataset.type;
    const courseType = type === 'video' ? 10 : 20;
    
    this.setData({
      activeTab: type,
      courseType: courseType,
      courses: [],  // 清空当前显示的课程列表
      [courseType === 10 ? 'videoCourses' : 'articleCourses']: [], // 清空对应类型的课程列表
      finished: false,
      [`${courseType === 10 ? 'videoPage' : 'articlePage'}.pageNumber`]: 1 // 重置页码
    }, () => {
      // 重新加载数据
      this.getMentorCourseList();
    });
  },

  loadMore() {
    if (this.data.loading || this.data.finished) return;
    const isVideo = this.data.courseType === 10;
    const pageKey = isVideo ? 'videoPage' : 'articlePage';
    
    this.setData({
      [`${pageKey}.pageNumber`]: this.data[pageKey].pageNumber + 1,
    }, () => {
      this.getMentorCourseList();
    });
  },

  onChange(event) {
    const checkedList = event.detail;
    // 从所有已加载的课程中筛选选中项
    const allCourses = [...this.data.videoCourses, ...this.data.articleCourses];
    
    const selectedItems = allCourses.filter(item => {
      return checkedList.includes(String(item.id)) || checkedList.includes(Number(item.id));
    });

    this.setData({
      result: checkedList,
      selectedItems: selectedItems
    });
  },
  onClose() {
    this.setData({
      show: false
    });
  },
  onToggleSheet() {
    this.setData({
      show: !this.data.show
    });
  },

  async handleConfirm() {
    const { result, columnsId } = this.data;
    if (result.length === 0) {
      wx.showToast({
        title: '请至少选择一门课程',
        icon: 'none'
      });
      return;
    }

    const addCourseData = {
      columnsId,
      coursesIdList: result
    };
    const { success } = await addCourseToColumn(addCourseData);
    if (success) {
      wx.showToast({
        title: '添加成功',
        icon: 'none'
      });
      this.setData({
        show: false
      });
      navigateBack();
    }
  },

  onCourseRemove(e) {
    const { courseId } = e.detail;
    this.setData({
      result: this.data.result.filter(id => Number(id) !== Number(courseId))
    });
    this.setData({
      selectedItems: this.data.selectedItems.filter(item => item.id !== courseId)
    });
  },
})