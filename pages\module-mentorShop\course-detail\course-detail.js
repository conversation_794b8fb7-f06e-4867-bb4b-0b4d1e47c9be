
// pages/module-mentorShop/course-detail/course-detail.js
import { getUserFullInfo } from "~/api/user";
import { getCourseDetail, getCourseCommentScore, getStudentList, addCourse } from "~/api/mentorShop";
import { navigateAndAddPage } from '~/utils/pageNavigation';
Page({
  /**
   * 页面的初始数据
   */
  data: {
    mentorInfo: {
      mentorBase: {
        user: {},
      },
      mentorCategoryList: [],
      mentorEducationList: [],
      mentorProfessionalList: [],
      mentorSkillFieldList: [],
      mentorTradeList: [],
    },
    htmlSnip: '',
    showUnfold: false,
    unfoldClose: true,
    mentorId: 0,
    tabType: '',
    isSaving: false, // 添加保存状态标志，防止重复提交
    starImages: {
      full: "https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/IM/star.png",
      half: "https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/IM/half-star.png",
      empty: "https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/IM/not-star.png",
    },
    courseInfo: {},
    studentAvatars: [],
    srccc: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/%E5%80%9F%E6%A2%A6.mp4',
    danmuList:
      [{
        text: '预览弹幕',
        color: '#ff0000',
        time: 1
      }, {
        text: '预览弹幕',
        color: '#ff00ff',
        time: 3
      }],
    videoContext: null,
    isVideoPlaying: false, // 记录视频当前是否在播放
    wasVideoPlaying: false, // 记录音频开始时视频是否在播放
    courseCommentScore: {
      averageScore:0,
      reviewsCount:0
    },
    total: 0,
    isEdit: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // isEdit 1 编辑
    // video,article,imageText
    this.setData({
      mentorId: options.mentorId,
      tabType: options.type,
      courseId: options.id,
      isEdit: options.isEdit
    })
    // 根据 isEdit 参数设置不同的导航栏标题
    wx.setNavigationBarTitle({
      title: options.isEdit === '1' ? '课程详情' : '课程预览'
    });
    this.getMentorFullInfo()
    if (options.id) {
      // this.getCourseDetail(options.id);
      this.getCourseCommentScore(options.id);
      this.getStudentList(options.id);
    }

    const tempForm = wx.getStorageSync('tempVideoForm');
    if (tempForm) {
      this.setData({
        courseInfo: tempForm
      })
    } else {
      this.getCourseDetail(options.id);
    }

    // 创建视频上下文
    if (this.data.tabType === 'video') {
      this.setData({
        videoContext: wx.createVideoContext('courseVideo')
      });
    }
  },
  // 获取课程详情
  getCourseDetail(courseId) {
    getCourseDetail({ coursesId: courseId }).then(res => {
      this.setData({
        courseInfo: res.data
      })
    })
  },

  // 获取课程评价分数
  getCourseCommentScore(courseId) {
    getCourseCommentScore({ courseId: courseId }).then(res => {
      this.setData({
        courseCommentScore: res.data
      })
    })
  },

  // 获取学员列表
  getStudentList(courseId) {
    getStudentList({ courseId: courseId, pageSize: 10, pageNumber: 1 }).then(res => {
      this.setData({
        studentAvatars: res.data.splice(0, 8),
        total: res.paging.total
      })
    })
  },

  // 获取导师详情
  async getMentorFullInfo() {
    const {
      data
    } = await getUserFullInfo();
    const processedData = {
      ...data,
      stars: this.getStars(data.mentorBase.score),
    };
    this.setData({
      mentorInfo: processedData || {},
    });
    const value = this.getHtmlValue(processedData.mentorBase.introduction)
    this.setData({
      htmlSnip: value
    })
    this.handleShowUnfold()
  },
  unfoldChange() {
    this.setData({
      unfoldClose: !this.data.unfoldClose
    })
  },
  handleShowUnfold() {
    let that = this
    const query = wx.createSelectorQuery();
    query.select('.mentor-introduce-position').boundingClientRect(function (rect) {
      let ratio = 750 / wx.getSystemInfoSync().windowWidth
      let height = rect.height * ratio
      if (height > (18 * 3 * ratio + 10)) {
        // 超过三行，这里的18是根据行高的36rpx转换的，+10是为了有点缓冲高度
        that.setData({
          showUnfold: true
        })
      } else {
        that.setData({
          showUnfold: false
        })
      }
    }).exec();
  },
  getHtmlValue(value) {
    const checkOption = value?.replace(/\n/g, '<br/>')
    return checkOption
  },
  getStars(score) {
    if (!score) return [];

    const starCount = 5;
    score = parseFloat(score);
    if (isNaN(score)) return [];

    const stars = [];
    const integerPart = Math.floor(score);
    const decimalPart = score - integerPart;

    for (let i = 0; i < starCount; i++) {
      if (i < integerPart) {
        stars.push(this.data.starImages.full);
      } else if (i === integerPart && decimalPart > 0.5) {
        stars.push(this.data.starImages.full);
      } else if (i === integerPart && decimalPart > 0 && decimalPart <= 0.5) {
        stars.push(this.data.starImages.half);
      } else {
        stars.push(this.data.starImages.empty);
      }
    }
    return stars;
  },

  toAllStudents() {
    navigateAndAddPage('/pages/module-mentorShop/study-progress/study-progress',{
      courseId:this.data.courseId
    })
    // wx.navigateTo({
    //   url: `/pages/module-mentorShop/study-progress/study-progress?courseId=${this.data.courseId}`,
    // })
  },

  toEditCourse() {
    if (this.data.courseInfo.auditStatus === 10) {
      wx.showToast({
        title: '审核中的视频不允许编辑',
        icon: 'none'
      })
      return
    }
    if (this.data.courseInfo.courseType === 10) {
      navigateAndAddPage('/pages/module-mentorShop/add-video/add-video',{
        id:this.data.courseId
      },'auto', true)
      // wx.navigateTo({
      //   url: `/pages/module-mentorShop/add-video/add-video?id=${this.data.courseId}`
      // })
    } else if (this.data.courseInfo.courseType === 20 || this.data.courseInfo.courseType === 30) {
      navigateAndAddPage('/pages/module-mentorShop/add-article/add-article',{
        id:this.data.courseId,
        courseType:this.data.courseInfo.courseType
      })
      // wx.navigateTo({
      //   url: `/pages/module-mentorShop/add-article/add-article?id=${this.data.courseId}&courseType=${this.data.courseInfo.courseType}`
      // })
    }
  },
  toSave() {
    // 如果已经在保存中，直接返回，防止重复提交
    if (this.data.isSaving) {
      wx.showToast({
        title: '保存中，请稍候...',
        icon: 'none'
      });
      return;
    }

    // 设置保存状态为true
    this.setData({
      isSaving: true
    });

    // 显示加载提示
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    console.log(this.data.courseInfo.courseType, 'this.data.courseInfo.courseType')
    addCourse(this.data.courseInfo).then(res => {
      // 隐藏加载提示
      wx.hideLoading();

      if (res.success) {
        wx.setStorageSync('courseRefresh', true)
        // wx.showToast({
        //   title: '保存成功',
        //   icon:'none'
        // });
        wx.removeStorage({
          key: 'tempVideoForm'
        });
        if (this.data.courseInfo.courseType === 10) {
          wx.showModal({
            title: '提交成功！',
            content: '预计3个工作日审核结束',
            showCancel: false,
            complete: (res) => {
              if (res.confirm) {
                navigateAndAddPage('/pages/module-home/home/<USER>', { active: 1 }, 'reLaunch')
              }
            }
          })
        } else {
          wx.showToast({
            title: '保存成功',
            icon:'none'
          });
          navigateAndAddPage('/pages/module-home/home/<USER>', { active: 1 }, 'reLaunch')
        }

        // 设置需要刷新的标志
        // const pages = getCurrentPages();
        // const prevPage = pages[pages.length - 2]; // 上一个页面
        // if (prevPage && prevPage.route === 'pages/module-home/home/<USER>') {
        //   // 如果上一个页面是home页面，设置刷新标志
        //   const shopComponent = prevPage.selectComponent('#shop');
        //   if (shopComponent) {
        //     shopComponent.setNeedRefresh();
        //   }
        // }
        // navigateAndAddPage('/pages/module-home/home/<USER>', { active: 1 }, 'reLaunch')

        // wx.reLaunch({
        //   url: `/pages/module-home/home/<USER>
        // })
      }
    }).catch((err) => {
      console.log(err, 'err')
      // 发生错误时也需要隐藏加载提示
      wx.hideLoading();
      wx.showToast({
        title: err.message || '保存失败，请重试',
        icon: 'none'
      });
      this.setData({
        isSaving: false
      });
    }).finally(() => {
      // 无论成功失败，最后都要重置保存状态
      this.setData({
        isSaving: false
      });
    });
  },
  toUpShelf() {
    // updateCourseStatus({
    //   coursesId: this.data.courseId,
    //   loadStatus: this.data.courseInfo.loadStatus === 1 ? 0 : 1
    // }).then(res => {
    //   if (res.success) {
    //     wx.showToast({
    //       title: `${this.data.courseInfo.loadStatus === 1 ? '上架' : '下架'}成功`,
    //       icon: 'none'
    //     })
    //     wx.navigateTo({
    //       url: `/pages/module-home/home/<USER>
    //     })
    //   }
    // })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: this.data.courseInfo.title,
      path: "/pages/module-mentorShop/course-detail/course-detail?id=" + courseId,
    };
  },
  // 修改视频播放处理方法
  handleVideoPlay() {
    // console.log('视频开始播放');
    // 设置视频播放状态
    this.setData({
      isVideoPlaying: true
    });

    // 获取 tab 组件
    const tabComponent = this.selectComponent('#tabComponent');
    if (tabComponent) {
      // 获取 dynamics 组件，使用类名选择器
      const dynamicsComponent = tabComponent.selectComponent('.dynamics-component');
      if (dynamicsComponent) {
        // 调用组件的暂停音频方法
        dynamicsComponent.pauseAudio();
        // 重置音频进度条
        const currentAudioIndex = dynamicsComponent.data.audioPlayObject.audioPlayIndex;
        if (currentAudioIndex !== null) {
          dynamicsComponent.setData({
            [`limitedDynamicsList[${currentAudioIndex}].audioInfo.audioProgress`]: 0,
            'audioPlayObject.audioPlayIndex': null,
            'audioPlayObject.isPlay': false
          });
        }
      }
    }
  },
  // 修改视频暂停处理方法
  handleVideoPause() {
    // console.log('视频暂停');
    // 只有在不是因为音频播放而暂停时，才更新状态
    if (!this.data.wasVideoPlaying) {
      this.setData({
        isVideoPlaying: false
      });
    }
  },
  // 修改视频结束处理方法
  handleVideoEnded() {
    // console.log('视频播放结束');
    this.setData({
      isVideoPlaying: false,
      wasVideoPlaying: false // 确保重置状态
    });
  },
  // 修改音频播放处理方法
  handleAudioPlay(e) {
    // console.log('音频播放', this.data.videoContext, this.data.isVideoPlaying);
    if (this.data.videoContext) {
      const videoContext = this.data.videoContext;

      // 检查视频是否正在播放
      if (this.data.isVideoPlaying) {
        // 先记录视频播放状态，再暂停视频
        this.setData({
          wasVideoPlaying: true
        }, () => {
          videoContext.pause();
        });
      } else {
        // 如果视频没在播放，确保状态是正确的
        this.setData({
          wasVideoPlaying: false
        });
      }
    }
  },
  // 修改音频暂停处理方法
  handleAudioPause() {
    // console.log('音频暂停');
    // 只有当视频之前在播放且是因为音频播放而暂停时，才恢复视频播放
    if (this.data.wasVideoPlaying && this.data.videoContext) {
      // 恢复视频播放
      this.data.videoContext.play();
      // 重置状态
      this.setData({
        wasVideoPlaying: false,
        isVideoPlaying: true  // 添加这行，因为视频会继续播放
      });
    }
  },
  // 修改音频结束处理方法
  handleAudioEnd() {
    // console.log('音频结束');
    // 只有当视频之前在播放且是因为音频播放而暂停时，才恢复视频播放
    if (this.data.wasVideoPlaying && this.data.videoContext) {
      // 恢复视频播放
      this.data.videoContext.play();
      // 重置状态
      this.setData({
        wasVideoPlaying: false,
        isVideoPlaying: true  // 添加这行，因为视频会继续播放
      });
    }
  },
});
