//获取应用实例
const app = getApp()
import {
  webUpload
} from "../../api/webUpload"
Component({
  properties: {
    imgFile: {
      type: Object,
      value: {},
      observer: function (newVal) {
        if (newVal && newVal.url) {
          this.setData({
            src: newVal.url
          });

          // 重置裁切框尺寸到初始值
          this.setData({
            width: newVal.width || 250, // 重置为初始宽度
            height: newVal.height || 250 // 重置为初始高度
          });

          // 如果有初始图片,重置裁剪框
          if (this.cropper) {
            // 重置图片和裁切框
            this.cropper.imgReset();
            // 重置裁切框大小
            if (newVal.width && newVal.height) {
              this.cropper.setCutSize(newVal.width, newVal.height);
            } else {
              this.cropper.setCutSize(250, 250);
            }
            // 重新居中裁切框
            this.cropper.setCutCenter();
          }
        }
      }
    },
    // 添加默认图片支持
    defaultImage: {
      type: String,
      value: ''
    },
    // 是否调用上传接口
    upLoadFlag: {
      type: Boolean,
      value: true
    }
  },
  data: {
    src: '',
    width: 250, //初始宽度
    height: 250, //初始高度
    max_width: 300,
    max_height: 300,
    disable_rotate: true, //是否禁用旋转
    disable_ratio: true, //锁定比例
    limit_move: true, //是否限制移动
  },

  lifetimes: {
    attached() {
      // 组件初始化时,如果有默认图片则显示
      if (this.data.defaultImage) {
        this.setData({
          src: this.data.defaultImage
        });
      }
      this.cropper = this.selectComponent("#image-cropper");
    }
  },

  methods: {
    cropperload(e) {
      console.log('cropper加载完成');
    },

    loadimage(e) {
      wx.hideLoading();
      // console.log('图片');
      this.cropper.imgReset();
    },

    clickcut(e) {
      //图片预览
      wx.previewImage({
        current: e.detail.url, // 当前显示图片的http链接
        urls: [e.detail.url] // 需要预览的图片http链接列表
      })
    },

    upload() {
      let that = this;
      wx.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success(res) {
          wx.showLoading({
            title: '加载中',
          })
          const tempFilePaths = res.tempFilePaths[0];
          //重置图片角度、缩放、位置
          that.cropper.imgReset();
          that.setData({
            src: tempFilePaths
          });
        }
      })
    },

    setWidth(e) {
      this.setData({
        width: e.detail.value < 10 ? 10 : e.detail.value
      });
      this.setData({
        cut_left: this.cropper.data.cut_left
      });
    },

    setHeight(e) {
      this.setData({
        height: e.detail.value < 10 ? 10 : e.detail.value
      });
      this.setData({
        cut_top: this.cropper.data.cut_top
      });
    },

    switchChangeDisableRatio(e) {
      this.setData({
        disable_ratio: e.detail.value
      });
    },

    setCutTop(e) {
      this.setData({
        cut_top: e.detail.value
      });
      this.setData({
        cut_top: this.cropper.data.cut_top
      });
    },

    setCutLeft(e) {
      this.setData({
        cut_left: e.detail.value
      });
      this.setData({
        cut_left: this.cropper.data.cut_left
      });
    },

    switchChangeDisableRotate(e) {
      if (!e.detail.value) {
        this.setData({
          limit_move: false,
          disable_rotate: e.detail.value
        });
      } else {
        this.setData({
          disable_rotate: e.detail.value
        });
      }
    },

    switchChangeLimitMove(e) {
      if (e.detail.value) {
        this.setData({
          disable_rotate: true
        });
      }
      this.cropper.setLimitMove(e.detail.value);
    },

    switchChangeDisableWidth(e) {
      this.setData({
        disable_width: e.detail.value
      });
    },

    switchChangeDisableHeight(e) {
      this.setData({
        disable_height: e.detail.value
      });
    },

    async submit() {
      const cropperResult = await new Promise(resolve => {
        this.cropper.getImg((obj) => {
          resolve(obj);
        });
      });

      if (this.properties.upLoadFlag) {
        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        try {
          // const { data, success } = await uploadFile({
          //   filePath: cropperResult.url
          // });
          const {
            url
          } = await webUpload(cropperResult.url, 'mentor');
          this.triggerEvent('onCropperDone', {
            url: url,
            width: cropperResult.width,
            height: cropperResult.height
          });
        } catch (error) {
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          });
        } finally {
          wx.hideLoading();
        }
      } else {
        this.triggerEvent('onCropperDone', {
          url: cropperResult.url,
          width: cropperResult.width,
          height: cropperResult.height
        });
      }
      this.triggerEvent('onCancel');
    },

    rotate() {
      this.cropper.setAngle(this.cropper.data.angle += 90);
    },

    top() {
      this.data.top = setInterval(() => {
        this.cropper.setTransform({
          y: -3
        });
      }, 1000 / 60)
    },

    bottom() {
      this.data.bottom = setInterval(() => {
        this.cropper.setTransform({
          y: 3
        });
      }, 1000 / 60)
    },

    left() {
      this.data.left = setInterval(() => {
        this.cropper.setTransform({
          x: -3
        });
      }, 1000 / 60)
    },

    right() {
      this.data.right = setInterval(() => {
        this.cropper.setTransform({
          x: 3
        });
      }, 1000 / 60)
    },

    narrow() {
      this.data.narrow = setInterval(() => {
        this.cropper.setTransform({
          scale: -0.02
        });
      }, 1000 / 60)
    },

    enlarge() {
      this.data.enlarge = setInterval(() => {
        this.cropper.setTransform({
          scale: 0.02
        });
      }, 1000 / 60)
    },

    end(e) {
      clearInterval(this.data[e.currentTarget.dataset.type]);
    },

    cancel() {
      this.triggerEvent('onCancel'); // 触发取消事件
    },

    // 修改还原方法
    reset() {
      // 重置图片角度、缩放、位置
      this.setData({
        angle: 0,
        scale: 1,
        width: 250, // 重置为初始宽度
        height: 250 // 重置为初始高度
      });

      if (this.cropper) {
        // 重置裁剪框
        this.cropper.imgReset();
        // 重置裁切框大小
        this.cropper.setCutSize(250, 250);
        // 重新居中裁切框
        this.cropper.setCutCenter();
      }
    },
  }
})