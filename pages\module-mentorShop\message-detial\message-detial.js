// pages/module-mentorShop/message-detial/message-detial.js
import { getDynamicMessageList, markDynamicAsRead } from '~/api/mentorShop';
import { navigateAndAddPage } from '~/utils/pageNavigation';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    page: {
      pageSize: 20, // 每页数量
      pageNumber: 1, // 当前页码
    },
    finished: false,
    dynamicMessageList: [],//动态消息列表
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getDynamicMessageList();
    this.markDynamicAsRead();
  },

  // 动态标记已读
  async markDynamicAsRead() {
   await markDynamicAsRead();
  },


  async getDynamicMessageList() {
    if (this.data.loading) return;

    this.setData({
      loading: true,
    });

    const {
      data
    } = await getDynamicMessageList({
      ...this.data.page,
    });

    if (data.length < this.data.page.pageSize) {
      this.setData({
        finished: true
      });
    }
    const newList = this.data.page.pageNumber === 1 ?
      data :
      [...this.data.dynamicMessageList, ...data];

    this.setData({
      dynamicMessageList: newList,
      loading: false
    });
  },

  // 添加滚动监听方法
  onScrollToLower() {
    if (this.data.loading || this.data.finished) return;
    this.setData({
      'page.pageNumber': this.data.page.pageNumber + 1,
    }, () => {
      this.getDynamicMessageList();
    });
  },

  handleMessageItemTap(e) {
    const id = e.currentTarget.dataset.id;
    if(id){
      navigateAndAddPage('/pages/module-mentorShop/dynamics-detial/dynamics-detial',{
        id
      })
    }else{
      wx.showToast({
        title: '该动态已删除',
        icon: 'none'
      });
    }
    // wx.navigateTo({
    //   url: `/pages/module-mentorShop/dynamics-detial/dynamics-detial?id=${id}`
    // });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
  }
})