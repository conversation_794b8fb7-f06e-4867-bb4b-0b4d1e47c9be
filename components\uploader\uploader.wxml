<!--pages/components/uploader/uploader.wxml-->
<view class="uploader-wrap">
  <view class="uploader-box">
    <!-- 文件列表 -->
    <view class="image-list">
      <view class="image-item" wx:for="{{fileLists}}" wx:key="url">
        <!-- 文件类型 -->
        <view
          wx:if="{{item.icon}}"
          class="preview-cover"
          bindtap="clickPreview"
          data-url="{{item.url}}"
        >
          <image class="file-icon" src="{{item.icon}}" />
          <view class="name">{{item.file.name}}</view>
        </view>

        <!-- 图片类型 -->
        <image
          class="file-img"
          wx:el
          src="{{item.url}}"
          mode="aspectFill"
          bindtap="clickPreview"
          data-url="{{item.url}}"
        />

        <!-- 删除按钮 -->
        <view
          class="preview-cover-delete"
          wx:if="{{!readonly && deletable}}"
          bindtap="handleDelete"
          data-index="{{index}}"
          data-item="{{item}}"
        ></view>

        <!-- 上传状态 -->
        <view class="upload-status" wx:if="{{item.status === 'uploading'}}">
          <view class="upload-loading"></view>
        </view>
      </view>

      <!-- 上传按钮 -->
      <view
        class="image-item uploader-content"
        bindtap="customAfterRead"
        wx:if="{{!readonly && showUpload && fileLists.length < maxCount}}"
      >
        <image class="uploader-file-icon" src="{{uploadIcons}}" />
        <view
          >{{uploadWord || (accept === 'image' ? '上传图片' : accept === 'file'
          ? '上传文件' : '上传图片/文件')}}</view
        >
      </view>
    </view>
  </view>

  <view class="uploader-tip" wx:if="{{tip}}">{{tip}}</view>
</view>
