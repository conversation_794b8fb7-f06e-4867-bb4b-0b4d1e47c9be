<view class="appoint-detail">
  <van-popup
  close-on-click-overlay="{{ true }}"
  show="{{ showAppointCancelPopup }}"
  position="bottom"
  round
  bind:click-overlay="cancelAppointPopup"
  bind:close="cancelAppointPopup"
  custom-class="appoint-cancel-popup"
  >
    <view class="cancel-popup">
      <view class="close-icon" bind:tap="cancelAppointPopup">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/popup-close.png" mode=""/>
      </view>
      <view class="header">
        请选择取消原因
      </view>
      <view class="content">
        <view class="reason-item" wx:for="{{cancelAppointReasonList}}" wx:key="{{index}}" bind:tap="reasonChange" data-id="{{item.id}}">
          <text>{{item.reason}}</text>
          <image src="{{item.id === cancelRadio ? 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-active.png' : 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/radio-icon/radio-icon.png' }}" mode=""/>
        </view>
        <view class="reason-value" wx:if="{{cancelRadio === 3}}">
          <van-field
            autosize="{{ autosize }}"
            maxlength='60'
            model:value="{{ customReason }}"
            placeholder="请输入取消预约的原因" 
            type="textarea"
            custom-class="custom-textarea"
            show-word-limit="{{true}}"
          />

        </view>
      </view>
      <view class="btn">
        <van-button custom-class="info-btn" type="info" bind:tap="toCancelAppoint">取消预约</van-button>
      </view>
    </view>
  </van-popup>


  <view class="appoint-detail-content">
    <view class="appoint-status" wx:if="{{appointDetail.appointStatus}}">
      <image wx:if="{{appointDetail.appointStatus === 10}}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/status-icon/to-be-communicated.png" mode="aspectFill" />
      <image wx:if="{{appointDetail.appointStatus === 20}}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/status-icon/underway.png" mode="aspectFill" />
      <image wx:if="{{appointDetail.appointStatus === 30}}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/status-icon/communicated.png" mode="aspectFill" />
      <image wx:if="{{appointDetail.appointStatus === 40}}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/status-icon/cancel.png" mode="aspectFill" />
      <image wx:if="{{appointDetail.appointStatus === 50 && appointDetail.appointReportStatus === 10}}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/status-icon/communicated.png" mode="aspectFill" />
      <image wx:if="{{appointDetail.appointStatus === 50 && appointDetail.appointReportStatus === 20}}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/status-icon/finished.png" mode="aspectFill" />
      <image wx:if="{{appointDetail.appointStatus === 60}}" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/appoint/status-icon/miss.png" mode="aspectFill" />

      <view class="status-text">
        <view class="status-text-title" wx:if="{{appointDetail.appointStatus === 10}}">待沟通</view>
        <view class="status-text-title" wx:if="{{appointDetail.appointStatus === 20}}">进行中</view>
        <view class="status-text-title" wx:if="{{appointDetail.appointStatus === 30}}">已沟通</view>
        <view class="status-text-title" wx:if="{{appointDetail.appointStatus === 40}}">已取消</view>
        <view class="status-text-title" wx:if="{{appointDetail.appointStatus === 50 && appointDetail.appointReportStatus === 10}}">已沟通</view>
        <view class="status-text-title" wx:if="{{appointDetail.appointStatus === 50 && appointDetail.appointReportStatus === 20}}">已完成</view>
        <view class="status-text-title" wx:if="{{appointDetail.appointStatus === 60}}">已错过</view>

        <view class="status-text-subtitle">
          <view wx:if="{{appointDetail.appointStatus === 10 && appointDetail.appointType === 10}}">请等待咨询在咨询时间与您电话沟通</view>
          <view wx:if="{{appointDetail.appointStatus === 10 && appointDetail.appointType === 20}}">请等待咨询者到预约地点开始面谈</view>

          <view wx:if="{{appointDetail.appointStatus === 20 && appointDetail.appointType === 10}}">咨询时间已到达，请开始咨询</view>
          <view wx:if="{{appointDetail.appointStatus === 20 && appointDetail.appointType === 20}}">请告知咨询者签到口令，开始面谈</view>

          <view wx:if="{{appointDetail.appointStatus === 30 && appointDetail.appointReportStatus === 10}}">请您编辑并发送咨询报告</view>
          <view wx:if="{{appointDetail.appointStatus === 30 && appointDetail.appointReportStatus === 20}}">咨询报告已发送，请等待咨询者评价</view>

          <view wx:if="{{appointDetail.appointStatus === 40 && appointDetail.appointCancelReason !== 3 && appointDetail.appointCancelReason !== 13}}">{{appointDetail.appointCancelReasonText }}</view>
          <view wx:if="{{appointDetail.appointStatus === 40 && appointDetail.appointCancelReason === 3}}">导师取消：{{appointDetail.appointCancelDesc}}</view>
          <view wx:if="{{appointDetail.appointStatus === 40 && appointDetail.appointCancelReason === 13}}">学生取消：{{appointDetail.appointCancelDesc}}</view>

          <view wx:if="{{appointDetail.appointStatus === 50 && appointDetail.appointReportStatus === 10}}">请您编辑并发送咨询报告</view>
          <view wx:if="{{appointDetail.appointStatus === 50 && appointDetail.appointReportStatus === 20}}"></view>

          <view wx:if="{{appointDetail.appointStatus === 60}}"></view>

        </view>
      </view>
    </view>
    <view class="card-box" wx:if="{{appointDetail.appointType === 20}}">
      <view class="header">
        <view class="title">
          面谈签到口令
        </view>
        <view class="header-right {{appointDetail.appointSignIn.signInStatus === 10 ? 'no-sign-in' : ''}}">
          {{appointDetail.appointSignIn.signInStatus === 10 ? '未签到' : '已签到'}}
        </view>
      </view>
      <view class="sign-in-number" wx:if="{{appointDetail.appointSignIn}}">
        <view class="sign-number-item" wx:for="{{ appointDetail.appointSignIn.signInPassword }}" wx:key="{{index}}">{{item}}</view>
      </view>
      <view class="sign-in-date">
        <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/date-icon.png" mode="aspectFill" />
        <text>签到时间段:{{appointDetail.appointSignIn.validDateTime}}</text>
      </view>
    </view>

    <view class="card-box" wx:if="{{appointDetail.mentorTopicSnapshot}}">
      <view class="header">
        <view class="title">
          预约详情
        </view>
      </view>
      <view class="appoint-base">
        <view class="appoint-base-item">
          <view class="appoint-base-item-label">预约方式</view>
          <!-- <view class="appoint-base-item-value">{{appointDetail.appointType | filterAppointType}}</view> -->
          <view class="appoint-base-item-value">{{appointDetail.appointTypeText}}</view>
        </view>
        <view class="appoint-base-item">
          <view class="appoint-base-item-label">预约话题</view>
          <view class="appoint-base-item-value" wx:if="{{appointDetail.mentorTopicSnapshot}}">
            {{appointDetail.mentorTopicSnapshot.title}}
          </view>
        </view>
        <view class="appoint-base-item">
          <view class="appoint-base-item-label">辅导时间</view>
          <view class="appoint-base-item-value">{{appointDetail.appointDateTime}}</view>
        </view>
        <view class="appoint-base-item" wx:if="{{appointDetail.appointType === 20}}">
          <view class="appoint-base-item-label">预约地点</view>
          <view class="appoint-base-item-value" wx:if="{{appointDetail.mentorTopicSnapshot}}">{{appointDetail.mentorTopicSnapshot.address}}</view>
        </view>
      </view>
    </view>

    <view class="card-box" wx:if="{{appointDetail.appointLogProblem}}">
      <view class="header">
        <view class="title">
          问题描述
        </view>
      </view>
      <view class="appoint-detail-file">
        <view class="question-directive" wx:if="{{appointDetail.appointLogProblem && appointDetail.appointLogProblem.problemDescription}}">{{appointDetail.appointLogProblem.problemDescription.other}}</view>
        <view class="file-type" bind:tap="previewFile" wx:if="{{appointDetail.appointLogProblem.accessoryName && appointDetail.appointLogProblem.accessoryUrl}}">
          <image src="{{appointDetail.fileTypeImage}}" mode="aspectFill"/>
          <text wx:if="{{appointDetail.appointLogProblem}}">{{appointDetail.appointLogProblem.accessoryName}}</text>
        </view>
        <view class="image-type-list" wx:if="{{appointDetail.appointLogProblem && appointDetail.appointLogProblem.picUrlList && appointDetail.appointLogProblem.picUrlList.length > 0}}">
          <view class="image-type" wx:for="{{appointDetail.appointLogProblem.picUrlList}}" wx:key="{{index}}" bind:tap="imgPreview" data-item="{{item}}">
            <image src="{{item}}" mode="aspectFill"/>
          </view>
        </view>

        <view class="appoint-describe-supplementary" wx:if="{{appointDetail.mentorTopicSnapshot && appointDetail.mentorTopicSnapshot.appointLogCueList && appointDetail.mentorTopicSnapshot.appointLogCueList.length > 0}}">
            <view class="appoint-describe-supplementary-title">补充信息</view>
            <view class="appoint-describe-supplementary-item" wx:for="{{appointDetail.mentorTopicSnapshot.appointLogCueList}}" :key="{{index}}">
              <view class="appoint-describe-supplementary-label">{{item.problem}}</view>
              <view class="appoint-describe-supplementary-value">{{item.answer}}</view>
            </view>
          </view>
      </view>
    </view>

    <view class="card-box" wx:if="{{appointDetail.user}}">
      <view class="header">
        <view class="title">
          咨询方信息
        </view>
      </view>
      <view class="student-info">
        <view class="student-info-item">
          <view class="student-info-item-label">手机号码</view>
          <view class="student-info-item-value textoverflow">{{appointDetail.user.phone || '-'}}</view>
        </view>
        <view class="student-info-item">
          <view class="student-info-item-label">姓名</view>
          <view class="student-info-item-value textoverflow">{{appointDetail.user.userName || '-'}}</view>
        </view>
        <view class="student-info-item">
          <view class="student-info-item-label">性别</view>
          <!-- <view class="student-info-item-value textoverflow" wx:if="{{appointDetail.user.sex === 0 || appointDetail.user.sex}}">{{appointDetail.user.sex | filterSex}}</view> -->
          <view class="student-info-item-value textoverflow" wx:if="{{appointDetail.user.sex === 0 || appointDetail.user.sex}}">{{appointDetail.user.newSex}}</view>
          <view class="student-info-item-value textoverflow" wx:else>-</view>
        </view>
        <view class="student-info-item">
          <view class="student-info-item-label">出生年份</view>
          <!-- <view class="student-info-item-value textoverflow" wx:if="{{appointDetail.user.birthday}}">{{appointDetail.user.birthday | filterBirthday}}</view> -->
          <view class="student-info-item-value textoverflow" wx:if="{{appointDetail.user.newBirthday}}">{{appointDetail.user.newBirthday}}</view>
          <view class="student-info-item-value textoverflow" wx:else>-</view>
        </view>
        <view class="student-info-item">
          <view class="student-info-item-label">身份</view>
          <view class="student-info-item-value textoverflow">{{appointDetail.user.identityName || '-'}}</view>
        </view>
        <view class="student-info-item" wx:if="{{appointDetail.user.school}}">
          <view class="student-info-item-label">学校</view>
          <view class="student-info-item-value textoverflow">{{appointDetail.user.school || '-'}}</view>
        </view>
        <view class="student-info-item" wx:if="{{appointDetail.user.college}}">
          <view class="student-info-item-label">学院</view>
          <view class="student-info-item-value textoverflow">{{appointDetail.user.college || '-'}}</view>
        </view>
        <view class="student-info-item" wx:if="{{appointDetail.user.major}}">
          <view class="student-info-item-label">专业</view>
          <view class="student-info-item-value textoverflow">{{appointDetail.user.major || '-'}}</view>
        </view>
      </view>
    </view>
  </view>
  <view class="appoint-detail-footer"
    wx:if="{{((appointDetail.appointStatus === 30 || appointDetail.appointStatus === 50) && appointDetail.appointReportStatus === 10) || ((appointDetail.appointStatus === 30 || appointDetail.appointStatus === 50) && appointDetail.appointReportStatus !== 10) || (appointDetail.appointStatus === 10)}}"
  >
    <!-- <van-button bind:tap="pageBack" custom-class="default-btn" type="default" wx:if="{{ back }}">上一步</van-button> -->
    <van-button custom-class="info-btn" type="info" wx:if="{{(appointDetail.appointStatus === 30 || appointDetail.appointStatus === 50) && appointDetail.appointReportStatus === 10}}" bind:tap="reportEdit">编辑咨询报告</van-button>
    <van-button custom-class="info-btn" type="info" wx:if="{{(appointDetail.appointStatus === 30 || appointDetail.appointStatus === 50) && appointDetail.appointReportStatus !== 10}}" bind:tap="reportEcho">查看咨询报告</van-button>
    <van-button custom-class="info-btn" type="info" wx:if="{{appointDetail.appointStatus === 10}}" bind:tap="showCancelAppointPopup">取消预约</van-button>
  </view>


  <view class="appoint-detail-footer" wx:elif="{{appointDetail.appointType === 40 && appointDetail.appointStatus === 20}}">
    <van-button custom-class="info-btn" type="info"  bind:tap="enterMeeting">开启咨询</van-button>
  </view>
</view>