// pages/module-home/appoint/appoint-report-edit/apppoint-report-edit.js
import { getAppointDetail, sendAppointReport } from '~/api/appoint'
import moment from 'moment'
const docIcon = 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/upload/word.png'
const pdfIcon = 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/upload/pdf.png'
const excelIcon = 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/upload/excel.png'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    autosize: {
      minHeight: 120,
    },
    appointId: '',
    appointDetail: {
      appointStatus: '',
      appointType: '',
      appointSignIn: {
        validDateTime: '',
        signInPassword: []
      },
      user: {
        phone: '',
        userName: '',
        sex: '',
        birthday: '',
        identityName: '',
        school: '',
        college: '',
        major: '',
      },
      appointLogProblem: {
        problemDescription: {
          basicInfo: "",
          coreProblem: "",
          pastExperience: "",
          needProvideHelp: "",
          other: ""
        },
        picUrlList: [],
        accessoryName: "",
        accessoryUrl: ""
      },
      mentorTopicSnapshot: {
        appointLogCueList: [
          // {
          //   problem: '',
          //   answer: '',
          // },
          // {
          //   problem: '',
          //   answer: '',
          // },
        ]
      }
    },
    appointHistory: '',
    suggest: '',
    fileList: [
      // {
      //   type: 'img',
      //   url: 'https://img1.baidu.com/it/u=496472491,938876912&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=889.png'
      // }
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.appointId) {
      this.setData({
        appointId: options.appointId
      })
      this.getAppointDetail()
    }
  },
  async sendReport() {
    if (this.data.appointHistory.trim() === '') {
      // this.$toast('咨询过程记录不能为空')
      wx.showToast({
        title: '咨询过程记录不能为空',
        icon: 'none'
      })
      return
    }
    if (this.data.suggest.trim() === '') {
      // this.$toast('咨询建议不能为空')
      wx.showToast({
        title: '咨询建议不能为空',
        icon: 'none'
      })
      return
    }

    // 整合附件信息
    const picUrlList = []
    let accessoryName = ''
    let accessoryUrl = ''
    if (this.data.fileList && this.data.fileList.length > 0) {
      this.data.fileList.forEach(item => {
        if (item.type === 'img') {
          picUrlList.push(item.url)
        }
        if (item.type === 'file') {
          accessoryName = item.name || 'file'
          accessoryUrl = item.url || 'file'
        }
      })
    }
    const { success } = await sendAppointReport({
      appointLogId: this.data.appointId,
      userId: this.data.appointDetail.user.id,
      content: this.data.appointHistory,
      suggestionSummary: this.data.suggest,
      suggestionSummaryImgUrlList: picUrlList,
      suggestionSummaryAttachmentUrl: accessoryUrl,
      suggestionSummaryAttachmentName: accessoryName
    })
    if (success) {
      // this.$toast('报告发送成功')
      wx.showToast({
        title: '报告发送成功',
        icon: 'none'
      })
      setTimeout(() => {
        // this.$router.back()
        wx.navigateBack()
      }, 1000)
    }
  },

  onAfterUpload() {},
  onFileDelete() {},
  // 文件列表变化时触发
  onFileChange(e) {
    const {
      fileLists
    } = e.detail;
    this.setData({
      fileList: fileLists
    });
  },
  async getAppointDetail() {
    const { data } = await getAppointDetail({
      appointLogId: this.data.appointId
    })
    if (data.appointType) {
      const obj = {
        10: '电话咨询',
        20: '线下咨询',
        30: '面对面咨询',
        40: '线上咨询'
      }
      data.appointTypeText = obj[data.appointType]
    }
    if (data.appointLogProblem.accessoryName && data.appointLogProblem.accessoryUrl) {
      data.fileTypeImage = this.iconType(data.appointLogProblem.accessoryName)
    }
    if (data.user.sex || data.user.sex === 0) {
      const sexObject = {
        0: '男',
        1: '女'
      }
      data.user.newSex = sexObject[data.user.sex]
    }
    if (data.user.birthday) {
      data.user.newBirthday = moment(data.user.birthday).format('YYYY')
    }
    this.setData({
      appointDetail: data
    })
  },
  iconType(fileName) {
    const name = fileName.toLowerCase()
    return name.includes('doc') || name.includes('docx') ? docIcon : name.includes('pdf') ? pdfIcon
      : name.includes('xlsx') || name.includes('xls') ? excelIcon : ''
  },
  imgPreview(event) {
    wx.previewImage({
      current: event.currentTarget.dataset.item, // 当前显示图片的http链接
      urls: this.data.appointDetail.appointLogProblem.picUrlList // 需要预览的图片http链接列表
    })
  },
  previewFile() {
    wx.downloadFile({
      url: this.data.appointDetail.appointLogProblem.accessoryUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            showMenu: true,
            success: () => {
            },
            fail: (err) => {
              // reject(new Error('打开文档失败：' + err.errMsg));
            }
          });
        } else {
          // reject(new Error('下载失败，状态码：' + res.statusCode));
        }
      },
      fail: (err) => {
        // reject(new Error('下载失败：' + err.errMsg));
      }
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})