Component({
  properties: {
    width: {
      type: Number,
      value: 340
    },
    height: {
      type: Number, 
      value: 210
    }
  },

  data: {
    imgs: []
  },

  methods: {
    drawImage() {
      const self = this;
      
      this.wxml2canvas = new Wxml2Canvas({
        width: this.properties.width,
        height: this.properties.height,
        element: 'resumeCanvas',
        background: '#f0f0f0',
        progress(percent) {
          // 可以添加进度回调
        },
        finish(url) {
          const imgs = self.data.imgs;
          imgs.push(url);
          
          self.setData({ imgs });
          
          // 触发完成事件
          self.triggerEvent('finish', { url });
        },
        error(res) {
          // 触发错误事件
          self.triggerEvent('error', res);
        }
      });

      const data = {
        list: [{
          type: 'wxml',
          class: '.resume-content',  // 需要绘制的内容类名
          limit: '.resume-canvas',   // 绘制范围
          x: 0,
          y: 0
        }]
      };

      this.wxml2canvas.draw(data);
    },

    // 对外暴露的生成图片方法
    generateImage() {
      this.drawImage();
    }
  }
}); 