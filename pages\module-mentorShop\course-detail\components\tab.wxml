<!--pages/module-mentorShop/course-detail/components/tab.wxml-->
<view class="course">
  <view class="tab-container">
    <view class="tab {{activeTab === 'introduce' ? 'active' : ''}}" bindtap="switchTab" data-type="introduce">
      {{courseType === ('article'||'imageText')?'内容':'介绍'}}
    </view>
    <view class="tab {{activeTab === 'comment' ? 'active' : ''}}" bindtap="switchTab" data-type="comment">
      <view wx:if="{{courseType === 'course'}}">所含课程</view>
      <view wx:else>评论<text>（{{courseCommentScore.averageScore}}分/{{courseCommentScore.reviewsCount}}次评分）</text></view>
    </view>
  </view>
  <view class="container">
    <!-- 课程介绍内容 -->
    <view class="course-content" wx:if="{{activeTab === 'introduce'}}">
      <view class="course-desc {{courseInfo.isTextNoCopy === 1 ? 'no-copy' : ''}}">
        <mp-html  content="{{courseInfo.courseDetail}}" bindimgtap="onImgTap" class="mp-html-content"></mp-html>
        <!-- selectable="{{true}}"可以选择文字复制 -->
        <!-- <rich-text 
          class="desc-title" 
          nodes="{{modifiedCourseDetail}}" 
          user-select="{{courseInfo.isTextNoCopy === 1 ? 'false' : 'true'}}" 
          bindtap="handleTap"
        /> -->
        <!-- <view class="desc-title">课程描述</view>
        <view class="desc-content ">
          <view class="desc-txt">描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述</view>
          <image src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentorShop/mentor-bgc.png" mode="widthFix" lazy-load="true" />
        </view> -->
      </view>
      <view class="source" wx:if="{{courseInfo.courseType === 30 && courseInfo.source}}">
        <text>文章来源：</text>
        <text>{{courseInfo.source}}</text>
      </view>
    </view>
    <!-- 评论内容 -->
    <view class="comment-content" wx:if="{{activeTab === 'comment'}}">

      <view class="dynamics-list" wx:if="{{dynamicsList.length > 0}}">
        <dynamics class="dynamics-component" wx:if="{{dynamicsList.length > 0}}" courseId="{{courseId}}" padding="0" dynamicsList="{{dynamicsList}}" user-role="student" isCourseComment="{{true}}" isExpand="{{true}}" limit="3" 
        showFollowStatus="{{true}}" bind:audioPlay="handleAudioPlay" bind:audioEnd="handleAudioEnd" bind:audioPause="handleAudioPause" />
      </view>
      <view class="dynamics-list" wx:else>
        <custom-empty-new describe="暂无评论" />
      </view>
    </view>
  </view>
</view>