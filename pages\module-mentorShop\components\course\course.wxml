<!--pages/module-mentorShop/components/course/course.wxml-->
<view class="course-list {{selected ? 'course-list-select':''}}">
  <view class="course-item {{selected ? 'course-item-select':''}}">
    <view class="image-wrapper">
      <image class="course-image" src="{{columnInfo.courseCoverUrl}}" mode="aspectFill" />
    </view>
    <view class="course-info">
      <view class="course-meta">
        <view class="course-title {{add ? 'course-title-select':''}}">{{columnInfo.courseName}}</view>
        <view class="duration" wx:if="{{columnInfo.courseType === 10}}" >{{columnInfo.courseDuration}}</view>
        <view class="duration" wx:else style="opacity: 0;">111</view>
        <view class="price-box">
          <text class="currency" wx:if="{{ columnInfo.saleMethod === 10 }}">¥</text>
          <text class="price">{{columnInfo.saleMethod === 10 ? columnInfo.coursePrice:'免费'}}</text>
        </view>
      </view>
      <view class="right">
        <view class="rating">
          <text class="score">评分：</text>
          <text class="value">{{columnInfo.avgScore>0?columnInfo.avgScore:'暂无评'}}分</text>
        </view>
        <view class="student-count">{{columnInfo.addCount}}人购买</view>
        <view class="right-bottom" catch:tap="showReason" data-course-id="{{columnInfo.id}}" wx:if="{{!add}}">
          移除
        </view>
      </view>
    </view>
  </view>
</view>