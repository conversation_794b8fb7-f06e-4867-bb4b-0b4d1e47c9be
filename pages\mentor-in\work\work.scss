/* pages/mentor-in/education/education.wxss */
.education-list{
  width: 100%;
	padding: 30rpx 28rpx;
	border-radius: 12rpx;
  background: #fff;
  box-shadow: 0px 2px 16px 0px rgba(99, 97, 155, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  .experience-box{
    width: 100%;
    // padding-top: 13px;
    .experience-item{
      margin-top: 20rpx;
      background: rgb(248, 249, 254);
      border-radius: 12rpx;
      padding: 26rpx;
      .experience-item-header{
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: rgb(51, 51, 51);
        font-size: 28rpx;
        font-weight: 600;
        line-height: 40rpx;
        .experience-item-header-left{
          flex: 1;
        }
        .experience-item-header-edit{
          color: rgb(119, 119, 119);
          font-size: 24rpx;
          font-weight: 400;
          line-height: 32rpx;
          display: flex;
          align-items: center;
          margin-left: 16rpx;
          image{
            // margin-top: 1px;
            margin-left: 6rpx;
            width: 16rpx;
            height: 16rpx;
          }
        }
      }
      .experience-item-header-topic{
        color: #0057FF;
        .topic-icon{
          margin-right: 4rpx;
        }
      }
      .experience-item-center{
        margin-top: 18rpx;
        display: flex;
        color: rgb(51, 51, 51);
        font-size: 24rpx;
        font-weight: 500;
        line-height: 40rpx;
        display: flex;
        text{
          margin-right: 20rpx;
          max-width: 60%;
          &:nth-last-child(1) {
            margin-right: 0;
          }
        }
      }
      .experience-item-date{
        margin-top: 6rpx;
        color: rgb(51, 51, 51);
        font-size: 24rpx;
        font-weight: 500;
        line-height: 40rpx;
      }
      .experience-item-discribe{
        margin-top: 18rpx;
        color: rgb(115, 119, 131);
        font-size: 24rpx;
        font-weight: 400;
        line-height: 40rpx;
      }
      &:nth-child(1) {
        margin-top: 0;
      }
    }
    .experience-add{
      border: 4rpx dashed rgb(223, 223, 223);
      border-radius: 12rpx;
      margin-top: 20rpx;
      padding: 40rpx 34rpx 42rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .experience-add-left{
        .add-name{
          display: flex;
          align-items: center;
          image{
            width: 32rpx;
            height: 32rpx;
            margin-right: 8rpx;
          }
          text{
            color: rgb(51, 51, 51);
            font-size: 28rpx;
            font-weight: 500;
            line-height: 40rpx;
          }
        }
        .add-tips{
          margin-top: 10rpx;
          color: rgba(51, 51, 51, 0.75);
          font-size: 24rpx;
          font-weight: 400;
          line-height: 30rpx;
        }
      }
      .experience-add-right{
        display: flex;
        image{
          width: 36rpx;
          height: 36rpx;
        }
      }
    }
  }
}
