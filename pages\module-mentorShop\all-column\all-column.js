// pages/module-mentorShop/all-column/all-column.js
import { getColumnList, updateColumnStatus, getCourseColumnList, getSelectedColumnList } from '~/api/mentorShop'
import { navigateAndAddPage, navigateBack } from '~/utils/pageNavigation';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    show: false,
    result: [], // 存储选中的id数组
    selectedItems: [], // 新增：存储选中的完整item数据
    showFilter: false,
    loadStatus: '', // 默认显示全部 0-下架,1-上架
    filterText: '全部',
    add: false,
    loading: false,
    page: {
      pageSize: 20, // 每页数量
      pageNumber: 1, // 当前页码
    },
    finished: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.type === 'add') {
      this.setData({
        add: true
      });
      
    }

    // 从缓存中获取已选择的专栏ID，并确保转换为字符串类型
    const tempForm = wx.getStorageSync('tempVideoForm');
    if (tempForm && tempForm.bundleColumnsIds) {
      const stringIds = tempForm.bundleColumnsIds.map(id => String(id));
      this.setData({
        result: stringIds,
      },()=>{
        this.getSelectedColumnList(tempForm.bundleColumnsIds)
        // this.getCourseColumnList(tempForm.id);
      });
    }
    
    // this.getColumnList();
  },
  onShow() {
    this.setData({
      page: {
        pageSize: 20,
        pageNumber: 1
      },
      finished: false,
      loading: false,
      list:[]
    })
    this.getColumnList();
  },

  async getColumnList() {
    if (this.data.loading) return;

    this.setData({
      loading: true,
    });

    const {
      data
    } = await getColumnList({
      ...this.data.page,
      loadStatus: this.data.loadStatus
    });

    if (data.length < this.data.page.pageSize) {
      this.setData({
        finished: true,
      });
    }

    const newList = this.data.page.pageNumber === 1 ?
      data :
      [...this.data.list, ...data];

    // 更新选中项的完整数据
    // const selectedItems = newList.filter(item => 
    //   this.data.result.includes(String(item.id)) || 
    //   this.data.result.includes(Number(item.id))
    // );

    this.setData({
      list: newList,
      loading: false
    });

  },

  loadMore() {
    if (this.data.loading || this.data.finished) return;
    this.setData(
      {
        'page.pageNumber': this.data.page.pageNumber + 1,
      },
      () => {
        this.getColumnList();
      }
    );
  },

  //获取已选择的专栏列表
  getSelectedColumnList(columnsId){
    const columnsIds = columnsId;
    getSelectedColumnList({columnsIds}).then(res => {
      this.setData({
        selectedItems: res.data
      });
    })
  },

  // 获取课程已关联的专栏列表
  getCourseColumnList(id) {
    getCourseColumnList({ coursesId: id }).then(res => {
      this.setData({
        selectedItems: res.data
      });
    });
  },


  // 点击筛选按钮
  onFilterTap() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 选择筛选选项
  onFilterSelect(e) {
    const type = e.currentTarget.dataset.type;
    let filterText = '已上架';

    switch (type) {
      case '':
        filterText = '全部';
        break;
      case '1':
        filterText = '已上架';
        break;
      case '0':
        filterText = '已下架';
        break;
    }

    this.setData({
      list: [],
      page: {
        pageSize: 20,
        pageNumber: 1
      },
      finished: false,
      loading: false,
      loadStatus: type,
      filterText: filterText,
      showFilter: false // 关闭下拉框
    });

    this.getColumnList();
  },

  onClose() {
    this.setData({
      show: false
    });
  },

  onAddColumn() {
    navigateAndAddPage('/pages/module-mentorShop/add-column/add-column')
    // wx.navigateTo({
    //   url: '/pages/module-mentorShop/add-column/add-column'
    // });
  },

  onToggleSheet() {
    this.setData({
      show: !this.data.show
    });
  },
  // 复选框变化事件处理
  onChange(event) {
    const checkedList = event.detail.map(id => String(id));
    
    // 找出新增的选中项
    const newSelectedItems = this.data.list.filter(item => 
      checkedList.includes(String(item.id)) && 
      !this.data.result.includes(String(item.id))
    );
    
    // 找出需要移除的项
    const removedIds = this.data.result.filter(id => !checkedList.includes(id));
    
    // 更新selectedItems：保留未被移除的项，并添加新选中的项
    const updatedSelectedItems = [
      ...this.data.selectedItems.filter(item => !removedIds.includes(String(item.id))),
      ...newSelectedItems
    ];

    this.setData({
      result: checkedList,
      selectedItems: updatedSelectedItems
    });
  },

  // 上架/下架
  showReason(e) {
    const id = e.currentTarget.dataset.id;
    const status = e.currentTarget.dataset.status;
    const index = e.currentTarget.dataset.index;
    const updateStatus = (newStatus, successMsg) => {
      updateColumnStatus({ columnsId: id, loadStatus: newStatus })
        .then(() => {
          this.setData({ [`list[${index}].loadStatus`]: newStatus });
          if (successMsg) {
            wx.showToast({ title: successMsg, icon: 'none' });
          }
        })
        .catch(() => {
          wx.showToast({ title: '操作失败，请重试', icon: 'none' });
        });
    };
    if (status === 1) {
      wx.showModal({
        title: '下架',
        content: '下架后，已购用户仍可以学习，未购用户将无法查看和购买，确认下架？',
        showCancel: true,
        cancelText: '取消',
        confirmText: '确认',
        success: (res) => {
          if (res.confirm) {
            if (res.confirm) {
              updateStatus(0);
            }
          }
        }
      })
    } else if (status === 0) {
      // 上架
      updateStatus(1, '已上架');
    }
  },

  // 编辑
  onEdit(e) {
    const id = e.currentTarget.dataset.id;
    navigateAndAddPage('/pages/module-mentorShop/add-column/add-column',{
      id
    })
    // wx.navigateTo({
    //   url: `/pages/module-mentorShop/add-column/add-column?id=${id}`
    // });
  },

  // 点击专栏
  onCourseClick(e) {
    const courseId = e.currentTarget.dataset.id;
    navigateAndAddPage('/pages/module-mentorShop/column-detail/column-detail',{
      id:courseId,
      type:'course'
    })
    navigateAndAddPage('/pages/module-mentorShop/column-detail/column-detail',{
      id:courseId,
      type:'course'
    })
    // wx.navigateTo({
    //   url: `/pages/module-mentorShop/column-detail/column-detail?id=${courseId}&type=course`
    // });
  },

  // 确认
  handleConfirm() {
    const tempForm = wx.getStorageSync('tempVideoForm');
    tempForm.bundleColumnsIds = this.data.result;
    wx.setStorageSync('tempVideoForm', tempForm);
    navigateBack();
  }
})