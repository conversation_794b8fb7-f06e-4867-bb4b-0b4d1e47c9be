.mentor-apply-welcome{
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .welcome-bg{
    width: 100%;
    .bg{
      width: 100%;
    }
  }
  .process{
    width: 678rpx;
    margin-top: -110rpx;
    background: #FFFFFF;
    box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(99,97,155,0.1);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    padding: 30rpx 30rpx 38rpx;
    .title{
      font-weight: bold;
      font-size: 32rpx;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .process-box{
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-top: 16rpx;
      padding: 0 30rpx;
      .process-item{
        display: flex;
        flex-direction: column;
        align-items: center;
        .process-item-title{
          font-weight: 600;
          font-size: 48rpx;
          color: #333333;
          text-align: center;
          font-style: normal;
          text-transform: none;
          position: relative;
          .icon{
            position: absolute;
            background: rgba(0,87,255,0.6);
            border-radius: 4rpx 4rpx 4rpx 4rpx;
            width: 48rpx;
            height: 8rpx;
            bottom: 10rpx;
            left: 50%;
            transform: translateX(-50%);
          }
        }
        .process-item-text{
          margin-top: 8rpx;
          font-weight: 500;
          font-size: 26rpx;
          color: #333333;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
      .process-transition{
        padding-top: 8rpx;
        color: rgba(0,87,255,0.8);
        image{
          height: 22rpx;
          width: 22rpx;
        }
      }
    }
  }
  .btns{
		display: flex;
		flex-direction: column;
    align-items: center;
    padding: 0 64rpx 100rpx;
    margin-top: 60rpx;
		.login-accredit{
      width: 100%;
			margin-bottom: 24rpx;
			width: 622rpx;
			height: 96rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #0057FF;;
			border-radius: 62rpx;
			font-weight: 600;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 32rpx;
			text-align: center;
      font-style: normal;
      margin-left: 0;
      margin-right: 0;
    }
    .tips{
      font-weight: 400;
      font-size: 26rpx;
      color: #0057FF;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .to-student{
      margin-top: 58rpx;
      border-radius: 62rpx 62rpx 62rpx 62rpx;
      border: 2rpx solid #0057FF;
      width: 246rpx;
      height: 52rpx;
      font-weight: bold;
      font-size: 30rpx;
      color: #0057FF;
      line-height: 32rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}