/* pages/module-home/home/<USER>/mine/mine.wxss */
rich-text{
  word-break: break-all !important;
}
.mine-component{
  padding: 0 36rpx 0;
  .mentor-label {
    .mentor-label-item {
      margin-top: 24rpx;
      .header {
        display: flex;
        align-items: center;
        image {
          width: 32rpx;
          height: 32rpx;
          margin-right: 8rpx;
        }
        text {
          color: rgb(51, 51, 51);
          font-size: 28rpx;
          font-weight: 500;
          line-height: 40rpx;
        }
      }
      .value {
        margin-top: 12rpx;
        background: rgb(248, 249, 254);
        border-radius: 8rpx;
        padding: 24rpx;
        color: rgb(115, 119, 131);
        font-size: 28rpx;
        font-weight: 400;
        line-height: 40rpx;
        display: flex;
        align-items: center;
        rich-text{
          color: rgb(115, 119, 131);
          font-size: 28rpx;
          font-weight: 400;
          line-height: 40rpx;
        }
        text {
          display: flex;
          align-items: center;
          &::after {
            content: '/';
          }
          &:nth-last-child(1) {
            &::after {
              display: none;
            }
          }
        }
      }
      &:nth-child(1) {
        margin-top: 28rpx;
      }
    }
  }
  .card-box{
    margin-top: 28rpx;
    padding: 30rpx;
    box-shadow: 0px 2px 16px 0px rgba(99, 97, 155, 0.1);
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    .header{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .name{
        font-weight: 600;
        font-size: 30rpx;
        color: #333333;
        line-height: 43rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .operation{
        font-weight: 400;
        font-size: 24rpx;
        color: #777777;
        line-height: 28rpx;
        text-align: center;
        font-style: normal;
        text-transform: none;
        image{
          margin-left: 2rpx;
          width: 20rpx;
          height: 20rpx;
        }
      }
    }
    .appoint-content{
      padding-top: 4rpx;
      .appoint-item {
        margin-top: 20rpx;
        background: rgb(248, 249, 254);
        border-radius: 12rpx;
        padding: 26rpx;
        .appoint-item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .appoint-user-info {
            display: flex;
            align-items: center;
            flex: 1;
            image {
              width: 48rpx;
              height: 48rpx;
              border-radius: 50%;
              margin-right: 14rpx;
            }
            text {
              color: rgb(51, 51, 51);
              font-size: 28rpx;
              font-weight: 500;
              line-height: 48rpx;
            }
          }
          .appoint-type {
            margin-left: 16rpx;
            background: rgba(15, 147, 216, 0.1);
            border: 2rpx solid rgb(15, 147, 216);
            border-radius: 4rpx;
            padding: 4rpx 14rpx;
            color: rgb(15, 147, 216);
            font-size: 24rpx;
            font-weight: 400;
            line-height: 32rpx;
          }
        }
        .appoint-item-topic {
          margin-top: 16rpx;
          color: #0057ff;
          font-size: 28rpx;
          font-weight: 500;
          line-height: 40rpx;
        }
        .appoint-item-date {
          margin-top: 16rpx;
          color: rgb(158, 163, 172);
          font-size: 24rpx;
          font-weight: 400;
          line-height: 34rpx;
        }
      }
    }
    .topic-list {
      .topic-item {
        background: #F8F9FE;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        margin-top: 20rpx;
        padding: 8rpx 14rpx;
        // border-bottom: 2rpx solid rgb(235, 235, 237);
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
        display: flex;
        align-items: center;
        .icon{
          width: 32rpx;
          height: 32rpx;
          background: #0057FF;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #FFF;
          margin-right: 12rpx;
          border-radius: 50%;
        }
        .value{
          flex: 1;
        }
        &:nth-child(1) {
          // padding-top: 24rpx;
          margin-top: 24rpx;
        }
      }
    }
    .appoint-set {
      .appoint-set-date {
        // padding-top: 4px;
        .appoint-set-date-item {
          margin-top: 16rpx;
          background: rgb(248, 249, 254);
          border-radius: 12rpx;
          padding: 14rpx 20rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .appoint-set-date-item-left {
            display: flex;
            align-items: center;
            image {
              width: 36rpx;
              height: 36rpx;
              margin-right: 8rpx;
              line-height: 0;
            }
            text {
              color: rgb(51, 51, 51);
              font-size: 28rpx;
              font-weight: 400;
              line-height: 40rpx;
            }
          }
          .appoint-set-date-item-right {
            color: rgb(51, 51, 51);
            font-size: 28rpx;
            font-weight: 400;
            line-height: 40rpx;
          }
          &:nth-child(1) {
            margin-top: 36rpx;
          }
        }
      }
    }
    .comment-list {
      padding-top: 8rpx;
      .comment-item {
        padding: 26rpx 0 24rpx;
        border-bottom: 2rpx solid rgb(235, 235, 237);
        .comment-item-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .comment-item-header-left {
            display: flex;
            align-items: center;
            image {
              width: 48rpx;
              height: 48rpx;
              margin-right: 14rpx;
              border-radius: 50%;
            }
            text {
              color: rgb(51, 51, 51);
              font-size: 28rpx;
              font-weight: 500;
              line-height: 48rpx;
              flex: 1;
            }
          }
          .comment-item-header-right {
            margin-left: 16rpx;
            display: flex;
            align-items: center;
            background-color: #f1f2f6;
            border-radius: 12rpx;
            box-shadow: 0 0 32rpx rgba(99, 97, 155, 0.1);
            padding: 10rpx 12rpx;
            height: 36rpx;
            image {
              width: 24rpx;
              height: 24rpx;
              margin-right: 8rpx;
            }
            text {
              color: #4b5362;
              font-size: 20rpx;
              line-height: 24rpx;
            }
          }
          .comment-hide {
            background: rgba(121, 121, 121, 0.1);
            border: 2rpx solid rgb(121, 121, 121);
            color: rgb(121, 121, 121);
          }
        }
        .comment-item-value {
          margin-top: 12rpx;
          color: rgb(115, 119, 131);
          font-size: 24rpx;
          font-weight: 400;
          line-height: 36rpx;
          word-break: break-all;
        }
        .comment-item-topic {
          display: inline-block;
          margin-top: 12rpx;
          padding: 6rpx 10rpx;
          color: rgb(51, 51, 51);
          font-size: 24rpx;
          font-weight: 400;
          line-height: 28rpx;
          background: rgb(242, 244, 255);
          border-radius: 8rpx;
        }
        .comment-item-bottom {
          margin-top: 16rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: rgb(158, 163, 172);
          font-size: 24rpx;
          font-weight: 400;
          line-height: 34rpx;
          .comment-item-bottom-like {
            display: flex;
            align-items: center;
            color: rgb(140, 145, 154);
            image {
              width: 30rpx;
              height: 30rpx;
            }
          }
        }
        &:nth-last-child(1) {
          padding-bottom: 0;
          border: none;
        }
      }
    }
    .empty{
      padding: 20rpx 0 60rpx;
    }
    &:nth-last-child(2) {
      margin-bottom: 24rpx;
    }
  }
}