// pages/mine/education-edit/education-edit.js
// import { postMentorEducation, editMentorEducation, getMentorEducation, deleteMentorEducation } from '~/api/user'
import { postMentorEducation, editMentorEducation, getMentorEducation, deleteMentorEducation } from '~/api/user'
import moment from 'moment'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '', // 是否具有编辑的id

    // defaultStartDate: '', // 默认的开始时间选择列表
    // defaultEndDate: '', // 默认的结束日期选择列表

    defaultStarSelectDate: '', // 默认的开始时间选择时间
    defaultEndSelectDate: '', // 默认的结束日期选择时间
    educationObject: {
      universityName: '',
      levelName: '',
      educationLevel: null,
      majorName: '',
      startDate: '',
      endDate: ''
    },

    startColums: [
      {
        values: [],
        defaultIndex: 0,
      },
      {
        values: [],
        defaultIndex: 0,
      },
    ],
    endColums: [
      {
        values: [],
        defaultIndex: 0,
      },
      {
        values: [],
        defaultIndex: 0,
      },
    ],

    educationLevelPopup: false, // 展示选择学历层次的popup
    startDatePopup: false, // 选择开始时间的popup
    endDatePopup: false, // 选择结束时间的popup
    educationListLevel: [
      {
        text: '高中',
        id: 10
      },
      {
        text: '大专',
        id: 20
      },
      {
        text: '本科',
        id: 30
      },
      {
        text: '硕士',
        id: 40
      },
      {
        text: '博士',
        id: 50
      },
      {
        text: '博士后',
        id: 60
      },
    ]
  },
  
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      id: options.id || ''
    })
    this.setData({
      defaultStarSelectDate: moment().year() + '-01',
      defaultEndSelectDate: moment().format('YYYY-MM'),
    })
    if (this.data.id) {
      this.getMentorEducation()
    } else {
      this.setDateColums()
    }
  },
  async getMentorEducation() {
    const { data } = await getMentorEducation({
      mentorEducationId: this.data.id
    })
    if (data.startDate) {
      data.startDate = moment(data.startDate).format('YYYY-MM')
    }
    if (data.endDate) {
      data.endDate = moment(data.endDate).format('YYYY-MM')
    }
    const educationObject = {...this.data.educationObject, ...data}
    this.setData({
      educationObject
    })
    if (data.educationLevel) {
      const levelName = this.data.educationListLevel.filter(item => item.id === data.educationLevel)[0].text
      this.setData({
        'educationObject.levelName': levelName
      })
    }
    this.setDateColums()
  },
  educationLevelConfirm(event) {
    this.setData({
      'educationObject.levelName': event.detail.value.text,
      'educationObject.educationLevel': event.detail.value.id
    })
    this.popupClose()
  },
  popupClose() {
    this.setData({
      educationLevelPopup: false,
      startDatePopup: false,
      endDatePopup: false
    })
  },
  showEducationLevelPopup() {
    this.setData({
      educationLevelPopup: true
    })
  },
  showStartPopup() {
    this.setData({
      startDatePopup: true
    })
  },
  showEndPopup() {
    this.setData({
      endDatePopup: true
    })
  },
  onChange(event) {
    this.setData({
      [`educationObject.${event.currentTarget.dataset.key}`]: event.detail
    })
  },
  expDelete() {
    const that = this
    wx.showModal({
      content: '确认删除该教育经历？',
      async success (res) {
        if (res.confirm) {
          const { success } = await deleteMentorEducation({
            mentorEducationId: that.data.id
          })
          if (success) {
            wx.showToast({
              title: '删除教育经历成功',
              icon: 'none'
            })
            setTimeout(() => {
              wx.navigateBack()
            }, 1000)
          }
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  },
  expBack() {
    wx.navigateBack()
  },
  async expEdit() {
    for(const key in this.data.educationObject) {
      if ((this.data.educationObject[key] ?? '') === '') {
        wx.showToast({
          title: '请填写完整教育经历信息',
          icon: 'none'
        })
        return
      }
    }
    const obj = JSON.parse(JSON.stringify(this.data.educationObject))
    obj.startDate += '-01 00:00:00'
    obj.endDate += '-01 00:00:00'
    if (this.data.id) {
      obj.mentorEducationId = this.data.id
      const { success } = await editMentorEducation(obj)
      if (success) {
        wx.showToast({
          title: '修改教育经历成功',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1000)
      }
    } else {
      const { success } = await postMentorEducation(obj)
      if (success) {
        wx.showToast({
          title: '保存教育经历成功',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1000)
      }
    }
  },




  /**
   * 时间选择相关的方法
   */
  setDateColums() {
    const minDate = this.data.educationObject.startDate || this.data.defaultStarSelectDate
    const maxDate = this.data.educationObject.endDate || this.data.defaultEndSelectDate

    const startDate = moment(minDate);
    const endDate = moment(maxDate);

    const startYear = startDate.year();
    const startMonth = startDate.month() + 1;
    
    const endYear = endDate.year();
    const endMonth = endDate.month() + 1;

    const startYearArr = []
    const endYearArr = []
    const startMonthArr = []
    const endMonthArr = []
    if (endYear === startYear) {
      if (endYear === moment().year()) {
        // 处理开始月份的月份数组
        for (let i = 1; i <= endMonth; i++) {
          startMonthArr.push(i)
        }
        for (let i = startMonth; i <= moment().month() + 1; i++) {
          endMonthArr.push(i)
        }
      } else {
        for (let i = 1; i <= endMonth; i++) {
          startMonthArr.push(i)
        }
        for (let i = startMonth; i <= 12; i++) {
          endMonthArr.push(i)
        }
      }
    } else {
      if (endYear === moment().year()) {
        // 处理开始月份的月份数组
        for (let i = 1; i <= 12; i++) {
          startMonthArr.push(i)
        }
        for (let i = startMonth; i <= moment().month() + 1; i++) {
          endMonthArr.push(i)
        }
      } else {
        for (let i = 1; i <= 12; i++) {
          startMonthArr.push(i)
        }
        for (let i = startMonth; i <= 12; i++) {
          endMonthArr.push(i)
        }
      }
    }

    for(let i = endYear ; i >= 1950; i--) {
      startYearArr.push(i)
    }
    for(let i = moment().year() ; i >= startYear ; i--) {
      endYearArr.push(i)
    }
    
    // let startYearIndex = ''
    // let startMonthIndex = ''
    // if (this.data.educationObject.startDate) {
    //   startYearIndex = startYearArr.findIndex(item => item === startYear)
    //   startMonthIndex = startMonthArr.findIndex(item => item === startMonth)
    // } else {
    //   startYearIndex = 0
    //   startMonthIndex = 0
    // }
    const startYearIndex = startYearArr.findIndex(item => item === startYear)
    const startMonthIndex = startMonthArr.findIndex(item => item === startMonth)
    const endYearIndex = endYearArr.findIndex(item => item === endYear)
    const endMonthIndex = endMonthArr.findIndex(item => item === endMonth)

    this.selectComponent('#startPicker').setColumnValues(0, startYearArr)
    this.selectComponent('#startPicker').setColumnValues(1, startMonthArr)
    this.selectComponent('#endPicker').setColumnValues(0, endYearArr)
    this.selectComponent('#endPicker').setColumnValues(1, endMonthArr)

    wx.nextTick(() => {
      setTimeout(() => {
        this.selectComponent('#startPicker').setColumnIndex(0, startYearIndex)
        this.selectComponent('#startPicker').setColumnIndex(1, startMonthIndex)
        this.selectComponent('#endPicker').setColumnIndex(0, endYearIndex)
        this.selectComponent('#endPicker').setColumnIndex(1, endMonthIndex)
      }, 300)
    })

  },
  setStartMonths(year) {
    const maxDate = this.data.educationObject.endDate || this.data.defaultEndDate
    const endDate = moment(maxDate);
    const endYear = endDate.year();
    const endMonth = endDate.month() + 1;
    const startMonthArr = []
    if (year == endYear) {
      for (let i = 1; i <= endMonth; i++) {
        startMonthArr.push(i)
      }
    } else {
      for (let i = 1; i <= 12; i++) {
        startMonthArr.push(i)
      }
    }
    this.selectComponent('#startPicker').setColumnValues(1, startMonthArr)
    this.selectComponent('#startPicker').setColumnIndex(1, 0)
  },
  setEndMonths(year) {
    const minDate = this.data.educationObject.startDate || this.data.defaultStartDate
    const startDate = moment(minDate);
    const startYear = startDate.year();
    const startMonth = startDate.month() + 1;
    
    const endMonthArr = []
    if (year == startYear) {
      if (year === moment().year()) {
        for (let i = startMonth; i <= moment().month() + 1; i++) {
          endMonthArr.push(i)
        }
      } else {
        for (let i = startMonth; i <= 12; i++) {
          endMonthArr.push(i)
        }
      }
    } else {
      for (let i = 1; i <= 12; i++) {
        endMonthArr.push(i)
      }
    }
    this.selectComponent('#endPicker').setColumnValues(1, endMonthArr)
    this.selectComponent('#endPicker').setColumnIndex(1, 0)
  },
  startDateChange(value) {
    if (value.detail.index === 0) {
      // 获取年份，改变年份时动态修改月份的数组
      this.setStartMonths(value.detail.value[0])
    }
  },
  endDateChange(value) {
    if (value.detail.index === 0) {
      // 获取年份，改变年份时动态修改月份的数组
      this.setEndMonths(value.detail.value[0])
    }
  },
  startConfirm(value) {
    const startDate = value.detail.value[0] + '-' + (value.detail.value[1] < 10 ? ('0' + String(value.detail.value[1])) : value.detail.value[1])
    this.setData({
      'educationObject.startDate': startDate
    })
    this.setDateColums()
    this.popupClose()
  },
  endConfirm(value) {
    const endDate = value.detail.value[0] + '-' + (value.detail.value[1] < 10 ? ('0' + String(value.detail.value[1])) : value.detail.value[1])
    this.setData({
      'educationObject.endDate': endDate
    })
    this.setDateColums()
    this.popupClose()
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})