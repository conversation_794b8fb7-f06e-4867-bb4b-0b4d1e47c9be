// pages/mentor-apply/mentor-apply-process/materials/materials.js
import { postTouristApplyInfo, getTouristAuthenticationInfo, getGZHQRcode, pollingGZHStatus, getUserLikeGzhStatus } from '~/api/user'
let GZHInterval = null
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showDialog: false,
    checked: false,
    fileList: [
      // {
      //   type: 'img',
      //   url: 'https://img1.baidu.com/it/u=496472491,938876912&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=889.png'
      // }
    ],

    GZHQRcode: '',
    sceneStr: '',
    pollingNumber: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getTouristAuthenticationInfo()
  },
  async getTouristAuthenticationInfo() {
    getTouristAuthenticationInfo().then(res => {
      if (res.data) {
        const fileList = []
        if (res.data.proofMaterials) {
          res.data.proofMaterials.forEach(item => {
            fileList.push({
              url: item
            })
          })
        }
        this.setData({
          fileList: fileList,
          checked: res.data.proofMaterials.length > 0 ? true : false
        })
        // wx.reLaunch({
        //   url: '/pages/mentor-apply/mentor-apply-process/base-info/base-info',
        // })
      }
    })
  },
  pageBack() {
    wx.navigateBack()
  },
  // 提交数据
  async pageNext() {
    // this.setData({
    //   showDialog: true
    // })
    // this.getGZHQRcode()
    // return
    if (!this.data.checked) {
      wx.showToast({
        title: '请阅读并同意毕师父服务承诺',
        icon: 'none'
      })
      return
    }
    if (this.data.fileList.length <= 0) {
      wx.showToast({
        title: '请上传证明材料',
        icon: 'none'
      })
      return
    }
    const proofMaterialList = []
    this.data.fileList.forEach(item => {
      proofMaterialList.push(item.url)
    })

    wx.showModal({
      title: '提交审核',
      content: '确定提交审核信息吗？',
      complete: async (res) => {
        if (res.cancel) {
          
        }
    
        if (res.confirm) {
          const { success } = await postTouristApplyInfo({
            proofMaterialList
          })
          if (success) {
            const { data } = await getUserLikeGzhStatus()
            // 字符串的0是没关注，1是已关注
            if (data === '1') {
              // 已关注过公众号
              wx.reLaunch({
                url: '/pages/mentor-apply/menrot-apply-result/menrot-apply-result',
              })
            } else {
              this.setData({
                showDialog: true
              })
              this.getGZHQRcode()
            }
          }
        }
      }
    })
  },
  async getGZHQRcode() {
    const { data } = await getGZHQRcode({})
    this.setData({
      GZHQRcode: data.qrCodeUrl,
      sceneStr: data.sceneStr
    })

    if (this.data.GZHQRcode && this.data.sceneStr) {
      GZHInterval = setInterval(() => {
        if (this.data.pollingNumber >= 120) {
          // clearInterval(GZHInterval)
          // this.pollingNumber = 0
          this.cancelGZHInterval()
        } else {
          // this.pollingNumber = this.pollingNumber + 1
          this.setData({
            pollingNumber: this.data.pollingNumber + 1
          })
          this.pollingGZHStatus()
        }
      }, 1000)
    } else {
      // this.$message.warning('缺少公众号授权')
      wx.showToast({
        title: '缺少公众号授权',
        icon: 'none'
      })
    }
  },
  async pollingGZHStatus() {
    const { data } = await pollingGZHStatus({
      sceneStr: this.data.sceneStr
    })
    try {
      if (data) {
        const key = data.split(':')[0]
        const value = data.split(':')[1]
        if (key === 'init') {
          // 继续挂载
        } else {
          // 取消轮询
          this.toResult()
        }
      }
    } catch(err) {
      this.cancelGZHInterval()
    }
  },
  // 取消轮询
  cancelGZHInterval() {
    clearInterval(GZHInterval)
    this.setData({
      pollingNumber: 0
    })
  },
  toResult() {
    this.cancelGZHInterval()
    wx.reLaunch({
      url: '/pages/mentor-apply/menrot-apply-result/menrot-apply-result',
    })
  },
  onChange(event) {
    this.setData({
      checked: event.detail
    })
  },
  onAfterUpload() {},
  onFileDelete() {},
  // 文件列表变化时触发
  onFileChange(e) {
    const {
      fileLists
    } = e.detail;
    this.setData({
      fileList: fileLists
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.cancelGZHInterval()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})