// var dayjs = require('dayjs')
module.exports = {
  // 学历等级过滤器
  filterEducationLevel: function(value) {
    var educationLevel = [
      { label: '大专', value: 20 },
      { label: '本科', value: 30 },
      { label: '硕士', value: 40 },
      { label: '博士', value: 50 }
    ];
    if (value === null || value === undefined) {
      return '-';
    }
    for (var i = 0; i < educationLevel.length; i++) {
      if (educationLevel[i].value === value) {
        return educationLevel[i].label;
      }
    }
    return '-';
  },

  // 职位类型过滤器
  filterPosition: function(value) {
    var positionCategory = [
      { label: '全职', value: 10 },
      { label: '兼职', value: 20 },
      { label: '实习', value: 30 },
      { label: '见习', value: 40 }
    ];
    if (value === null || value === undefined) {
      return '-';
    }
    for (var i = 0; i < positionCategory.length; i++) {
      if (positionCategory[i].value === value) {
        return positionCategory[i].label;
      }
    }
    return '-';
  },

  // 日期过滤器 (YYYY.MM)
  filterDate: function(value) {
    if (value) {
      var dateParts = value.split('-');
      if (dateParts.length >= 2) {
        var year = dateParts[0];
        var month = dateParts[1];
        return year + '.' + month;
      }
    }
    return '-';
  },

  // 生日过滤器 (YYYY-MM)
  filterBirthday: function(value,type) {
    if (value) {
      var dateParts = value.split('-');
      if (dateParts.length >= 2) {
        var year = dateParts[0];
        var month = dateParts[1];
        return year + '-' + month;
      }
    }
    return type || '-';
  },

  // 政治面貌过滤器
  filterPoliticCountenance: function(value,type) {
    var politicsStatusCategory = [
      { label: '群众', value: 10 },
      { label: '共青团员', value: 20 },
      { label: '党员', value: 30 }
    ];
    if (value === null || value === undefined || value === '') {
      return type || '-';
    }
    for (var i = 0; i < politicsStatusCategory.length; i++) {
      if (politicsStatusCategory[i].value === value) {
        return politicsStatusCategory[i].label;
      }
    }
    return type || '-';
  },

  // 性别过滤器
  filterSex: function(value) {
    var sexCategory = [
      { label: '男', value: 0 },
      { label: '女', value: 1 }
    ];
    if (value === null || value === undefined || value === '') {
      return '-';
    }
    for (var i = 0; i < sexCategory.length; i++) {
      if (sexCategory[i].value === value) {
        return sexCategory[i].label;
      }
    }
    return '-';
  },

  // HTML内容过滤器
  // getHtmlValue: function(value) {
  //   if (!value) return '';
  //   return value.replace(/\n/g, '<br/>');
  // }
};
