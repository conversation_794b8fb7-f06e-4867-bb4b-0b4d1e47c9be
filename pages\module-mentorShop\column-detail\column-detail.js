// pages/module-mentorShop/column-detail/column-detail.js
// import { removeCourseFromColumn } from '~/api/mentorShop'
import { getCourseListByColumnId, removeCourseFromColumn, updateCourseStatus } from '~/api/mentorShop';
import moment from 'moment'
import { navigateAndAddPage } from '~/utils/pageNavigation';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    courseStatus: '', // 默认显示全部
    showFilter: false,
    filterText: '全部',
    loading: false,
    page: {
      pageSize: 100, // 每页数量
      pageNumber: 1, // 当前页码
    },
    finished: false,
    courses: [],
    id: ''
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      id: options.id
    }, () => {
      this.getCourseListByColumnId();
    })
  },
  onShow() {
    this.getCourseListByColumnId();
  },
  // 点击筛选按钮
  onFilterTap() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },
  // 选择筛选选项
  onFilterSelect(e) {
    const type = e.currentTarget.dataset.type;
    let filterText = '已上架';

    switch (type) {
      case '':
        filterText = '全部';
        break;
      case '1':
        filterText = '已上架';
        break;
      case '0':
        filterText = '已下架';
        break;
      case '2':
        filterText = '审核中';
        break;
      case '3':
        filterText = '审核失败';
        break;
    }

    this.setData({
      page: {
        pageSize: 20,
        pageNumber: 1
      },
      finished: false,
      loading: false,
      courses: [],
      courseStatus: type,
      filterText: filterText,
      showFilter: false // 关闭下拉框
    });

    this.getCourseListByColumnId();
  },
  showReason(e) {
    const { courseId, status, shelfReason, index, courseType } = e.currentTarget.dataset;
    this.setData({ currentCourseId: courseId });

    const updateStatus = (newStatus, successMsg) => {
      updateCourseStatus({ coursesId: courseId, loadStatus: newStatus })
        .then(() => {
          this.setData({ [`courses[${index}].loadStatus`]: newStatus });
          if (successMsg) {
            // this.setData({
            //   finished: false,
            //   loading: false,
            //   'page.pageNumber': 1,
            //   courses: []
            // })
            // this.getCourseList()
            console.log(this.data.courseStatus, 'this.data.courseStatus')
            if (this.data.courseStatus.toString() === '1' || this.data.courseStatus.toString() === '0' || this.data.courseStatus.toString() === '2' || this.data.courseStatus.toString() === '3') {
              const filterData = this.data.courses.filter(item => {
                return item.id !== courseId
              })
              console.log(filterData, 'filterData')
              this.setData({
                courses: filterData
              })
            }
            wx.showToast({ title: successMsg, icon: 'none' });
          }
        })
        .catch(() => {
          wx.showToast({ title: '操作失败，请重试', icon: 'none' });
        });
    };

    // 下架原因弹窗
    if (status === 0 && shelfReason) {
      return wx.showModal({
        title: '下架原因',
        content: shelfReason,
        showCancel: true,
        cancelText: '取消',
        confirmText: '前往修改',
        success: (res) => {
          if (res.confirm) {
            if( courseType === 10 ){
              navigateAndAddPage('/pages/module-mentorShop/add-video/add-video',{
                id:courseId
              })
              // wx.navigateTo({
              //   url: `/pages/module-mentorShop/add-video/add-video?id=${courseId}`
              // });
            }else{
              navigateAndAddPage('/pages/module-mentorShop/add-article/add-article',{
                id:courseId,
                courseType:courseType
              })
              // wx.navigateTo({
              //   url: `/pages/module-mentorShop/add-article/add-article?id=${courseId}&courseType=${courseType}`
              // });
            }
          }
        }
      });
    }

    // 状态切换逻辑
    if (status === 1) {
      // 确认下架弹窗
      wx.showModal({
        title: '下架',
        content: '下架后，已购用户仍可以学习，未购用户将无法查看和购买，确认下架？',
        showCancel: true,
        cancelText: '取消',
        confirmText: '确认',
        success: (res) => {
          if (res.confirm) {
            updateStatus(0, '已下架');
          }
        }
      });
    } else if (status === 0) {
      // 上架
      updateStatus(1, '已上架');
    }
  },
  reviewFailedReason(e) {
    const { courseId, shelfReason, courseType } = e.currentTarget.dataset;
    wx.showModal({
      title: '失败原因',
      content: shelfReason,
      showCancel: true,
      cancelText: '取消',
      confirmText: '前往修改',
      success: (res) => {
        if (res.confirm) {
          if( courseType === 10 ){
            navigateAndAddPage('/pages/module-mentorShop/add-video/add-video',{
              id:courseId
            })
          }else{
            navigateAndAddPage('/pages/module-mentorShop/add-article/add-article',{
              id:courseId,
              courseType:courseType
            })
          }
        }
      }
    })
  },
  removeCourse(e) {
    const courseId = e.currentTarget.dataset.courseId
    wx.showModal({
      title: '移除',
      content: '移除后，已购用户仍可以学习，未购用户将无法查看和购买，确认移除？',
      success: (res) => {
        if (res.confirm) {
          removeCourseFromColumn({
            columnsId: this.data.id,
            coursesId: courseId
          }).then(res => {
            wx.showToast({
              title: '移除成功',
              icon: 'none'
            })
            this.handleRemoveCourse(courseId)
            // 触发自定义事件通知父组件更新
            // this.triggerEvent('removeCourse', {
            //   coursesId: coursesId
            // })
          })
        }
      }
    })
  },
  // 根据专栏id获取课程列表
  async getCourseListByColumnId() {
    if (this.data.loading) return;

    this.setData({
      loading: true,
    });
    let courseStatus = ''
    let auditStatus = ''
    if (this.data.courseStatus === '') {
      courseStatus = ''
      auditStatus = ''
    } else if (this.data.courseStatus === '1') {
      courseStatus = '1'
      auditStatus = '20'
    } else if (this.data.courseStatus === '0') {
      courseStatus = '0'
      auditStatus = '20'
    } else if (this.data.courseStatus === '2') {
      courseStatus = ''
      auditStatus = '10'
    } else if (this.data.courseStatus === '3') {
      courseStatus = ''
      auditStatus = '30'
    }
    const {
      data
    } = await getCourseListByColumnId({
      ...this.data.page,
      columnsId: this.data.id,
      courseStatus,
      auditStatus
    });

    if (data.length < this.data.page.pageSize) {
      this.setData({
        finished: true,
      });
    }
    const formattedData = data?.map(item => ({
      ...item,
      courseDuration: item.courseDuration ? moment.utc(item.courseDuration * 1000).format('HH:mm:ss') : '00:00:00'
    }));

    const newList = this.data.page.pageNumber === 1 ?
      formattedData :
      [...this.data.courses, ...formattedData];

    this.setData({
      courses: newList,
      loading: false
    });

  },

  loadMore() {
    if (this.data.loading || this.data.finished) return;
    this.setData(
      {
        'page.pageNumber': this.data.page.pageNumber + 1,
      },
      () => {
        this.getCourseListByColumnId();
      }
    );
  },

  onAddColumn(){
    navigateAndAddPage('/pages/module-mentorShop/add-course/add-course',{
      columnsId:this.data.id
    })
    // wx.navigateTo({
    //   url: `/pages/module-mentorShop/add-course/add-course?columnsId=${this.data.id}`
    // });
  },

  handleRemoveCourse(coursesId) {
    const courseList = this.data.courses.filter(item => item.id !== coursesId);
    this.setData({
      courses: courseList
    });
  },
  onCourseClick(e) {
    const tempForm = wx.getStorageSync('tempVideoForm');
    if (tempForm) {
      wx.removeStorage({
        key: 'tempVideoForm'
      });
    }
    const courseId = e.currentTarget.dataset.id;
    const mentorId = e.currentTarget.dataset.mentorId;
    const courseType = e.currentTarget.dataset.type;
    const type = courseType === '10' ? 'video' : courseType === '20' ? 'imageText' : 'article'
    navigateAndAddPage('/pages/module-mentorShop/course-detail/course-detail',{
      id:courseId,
      mentorId,
      type,
      isEdit:1
    })
      // wx.navigateTo({
      //   url: `/pages/module-mentorShop/course-detail/course-detail?id=${courseId}&mentorId=${mentorId}&type=${type}&isEdit=1`
      // });
  },
})