// pages/mentor-apply/mentor-apply-introduction/mentor-apply-introduction.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // introduction && login
    type: '',
    previewImage: 'https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/upload-example2.png',
    tabIndex: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // introduction && login 两种类型的区别
    this.setData({
      type: options.type
    })
  },
  preview() {
    wx.previewImage({
      urls: [this.data.previewImage]
    })
  },
  tabChange(event) {
    this.setData({
      tabIndex: Number(event.currentTarget.dataset.key)
    })
  },
  exit() {
    wx.showModal({
      title: '确定退出',
      content: '您确定退出导师申请流程吗？',
      showCancel: true,
      complete: (res) => {
        if (res.cancel) {
        }
    
        if (res.confirm) {
          wx.navigateBack()
        }
      }
    })
  },
  toRegister(event) {
    wx.navigateTo({
      url: `/pages/mentor-apply/mentor-apply-register/mentor-apply-register?type=${event.currentTarget.dataset.type}`,
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})