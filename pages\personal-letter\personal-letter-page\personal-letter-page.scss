
page{
  height: 100%;
  background-color: rgb(239, 241, 247);
  .personal-letter{
    height: 100%;
    display: flex;
    flex-direction: column;
    .student-detail {
      box-shadow: 0 8rpx 10rpx 0px rgba(61, 65, 78, 0.08);
      background: linear-gradient(to bottom, #eff5fe, #ffffff);
      padding: 24rpx 40rpx;
      display: flex;
      align-items: center;
      .student-img {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
      }
      .right {
        margin-left: 24rpx;
        .p {
          margin: 0;
        }
        .student-name {
          color: #333333;
          font-size: 28rpx;
          font-weight: 600;
          line-height: 32rpx;
        }
        .school {
          margin: 8rpx 0;
        }
        .school,
        .major {
          display: flex;
          align-items: center;
          image {
            width: 24rpx;
            height: 24rpx;
            margin-right: 12rpx;
          }
          .p {
            color: #4b5362;
            font-size: 24rpx;
            line-height: 28rpx;
            text:nth-child(2) {
              display: inline-block;
              width: 1rpx;
              height: 20rpx;
              background-color: #bbbbbb;
              margin: 0 16rpx;
            }
          }
        }
      }
    }


    .message-content{
      flex: 1;
      overflow: auto;
      // padding: 0 36rpx 40rpx;
      scroll-view{
        box-sizing: border-box;
        height: 100%;
        padding-top: 32rpx;
        .scroll-box{
          box-sizing: border-box;
          padding: 0 36rpx 40rpx;
        }
        .scroll-bottom{
          width: 1px;
          height: 1px;
          background-color: none;
        }
      }
      .message-number{
        width: 100%;
        padding: 22rpx 36rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        // margin-top: 32rpx;
        font-size: 24rpx;
        color: #4c4e61;
        line-height: 28rpx;
        border-radius: 18rpx;
        background-color: #dee2ef;
        .active{
          color: #0057ff;
        }
      }
      .message-content2{
        // padding-top: 32rpx;
        .data {
          color: #8f94a8;
          font-size: 22rpx;
          text-align: center;
          line-height: 26rpx;
          margin: 32rpx 0;
        }
        
        .cont {
          display: flex;
          .userImg {
            margin-right: 24rpx;
            image {
              width: 60rpx;
              height: 60rpx;
              border-radius: 50%;
            }
          }
        }
        .ask-txt {
          word-break: break-all;
          font-size: 28rpx;
          line-height: 40rpx;
          padding: 30rpx;
        }
        .left-msg {
          max-width: 80%;
          margin-right: auto;
          display: flex;
          .ask-txt {
            word-break: break-all;
            color: #ffffff;
            background-color: #4585ff;
            border-radius: 0px 30rpx 30rpx 30rpx;
            box-shadow: 8rpx 12rpx 38rpx 0 #aecaff;
          }
        }
        
        .right-msg {
          max-width: 80%;
          margin-left: auto;
          .ask-txt {
            word-break: break-all;
            color: #333333;
            background-color: #fff;
            box-shadow: 8rpx 12rpx 38rpx 0 #e3e7f8;
            border-radius: 30rpx 0 30rpx 30rpx;
          }
        }
        .file-list {
          border-radius: 20rpx;
          width: 80%;
          background-color: #fff;
          padding: 30rpx;
          .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .file-item-base {
              display: flex;
              align-items: flex-start;
              flex: 1;
              margin-right: 12px;
              image {
                margin-top: 4rpx;
                width: 56rpx;
                height: 66rpx;
                margin-right: 32rpx;
              }
              .file-value {
                flex: 1;
                .file-name {
                  font-size: 28rpx;
                  font-weight: 400;
                  color: #333333;
                  line-height: 40rpx;
                  word-break: break-all;
                }
                .file-date {
                  margin-top: 4rpx;
                  font-size: 24rpx;
                  font-weight: 400;
                  color: #9499ad;
                  line-height: 34rpx;
                }
              }
            }
          }
        }
        .file-img {
          margin-right: auto;
          image {
            max-width: 500rpx;
            height: 196rpx;
          }
        }
      }
    }
    
    .chat-container{
      padding: 20rpx 0;
      flex: 1;; /* 让容器撑满屏幕高度 */
      overflow-y: auto; /* 开启滚动 */
      .message-list {
        display: flex;
        flex-direction: column-reverse;
        padding: 0 24rpx;
        .data {
          color: #8f94a8;
          font-size: 22rpx;
          text-align: center;
          line-height: 26rpx;
          margin: 32rpx 0;
        }
        
        .cont {
          display: flex;
          .userImg {
            margin-right: 24rpx;
            image {
              width: 60rpx;
              height: 60rpx;
              border-radius: 50%;
            }
          }
        }
        .ask-txt {
          word-break: break-all;
          font-size: 28rpx;
          line-height: 40rpx;
          padding: 30rpx;
        }
        .left-msg {
          max-width: 80%;
          margin-right: auto;
          display: flex;
          .ask-txt {
            word-break: break-all;
            color: #ffffff;
            background-color: #4585ff;
            border-radius: 0px 30rpx 30rpx 30rpx;
            box-shadow: 8rpx 12rpx 38rpx 0 #aecaff;
          }
        }
        
        .right-msg {
          max-width: 80%;
          margin-left: auto;
          .ask-txt {
            word-break: break-all;
            color: #333333;
            background-color: #fff;
            box-shadow: 8rpx 12rpx 38rpx 0 #e3e7f8;
            border-radius: 30rpx 0 30rpx 30rpx;
          }
        }
        .file-list {
          border-radius: 20rpx;
          width: 80%;
          background-color: #fff;
          padding: 30rpx;
          .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .file-item-base {
              display: flex;
              align-items: flex-start;
              flex: 1;
              margin-right: 12px;
              image {
                margin-top: 4rpx;
                width: 56rpx;
                height: 66rpx;
                margin-right: 32rpx;
              }
              .file-value {
                flex: 1;
                .file-name {
                  font-size: 28rpx;
                  font-weight: 400;
                  color: #333333;
                  line-height: 40rpx;
                  word-break: break-all;
                }
                .file-date {
                  margin-top: 4rpx;
                  font-size: 24rpx;
                  font-weight: 400;
                  color: #9499ad;
                  line-height: 34rpx;
                }
              }
            }
          }
        }
        .file-img {
          margin-right: auto;
          image {
            max-width: 500rpx;
            height: 196rpx;
          }
        }
      }
    }
    .message-send{
      padding: 24rpx 40rpx;
      background: #FFF;
      display: flex;
      align-items: flex-end;
      .upload-btn{
        margin-bottom: 10rpx;
        width: 72rpx;
        height: 72rpx;
        margin-right: 16rpx;
      }
      .message-field{
        flex: 1;
        .van-cell{
          border-radius: 20rpx;
          background-color: #eff1f7;
        }
      }
      .send-btn{
        margin-bottom: 10rpx;
        width: 72rpx;
        height: 72rpx;
        margin-left: 16rpx;
      }
    }
  }
}