<!--pages/module-mentorShop/message-detial/message-detial.wxml-->
<view class="message-detail-container">
  <scroll-view 
    scroll-y 
    class="message-detail"
    bindscrolltolower="onScrollToLower"
    enhanced
    show-scrollbar="{{false}}"
  >
      <view class="message-list" wx:if="{{dynamicMessageList.length>0}}">
        <view class="message-item" wx:for="{{dynamicMessageList}}" wx:key="index" bindtap="handleMessageItemTap" data-id="{{item.mentionDynamicDetail.id}}">
          <view class="user-avatar">
            <image src="{{item.triggerUser.avatarPath}}" mode="aspectFill"></image>
          </view>
          <view class="message-content">
            <view class="user-name">{{item.triggerUser.userName}}</view>
            <view class="message-text" wx:if='{{item.businessType===20}}'>
              <image class="icon" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/wxmp/images/home/<USER>"></image>点赞了我
            </view>
            <view class="message-text" wx:elif="{{item.comment.commentContent}}">{{item.comment.commentContent}}</view>
            <view class="message-time">{{item.createdAt}}</view>
          </view>
          <view class="message-image" wx:if='{{!item.course}}'>
            <view wx:if="{{item.mentionDynamicDetail.dynamicContent}}">{{item.mentionDynamicDetail.dynamicContent}}</view>
            <image wx:elif="{{item.mentionDynamicDetail.dynamicImages.length>0}}" src="{{item.mentionDynamicDetail.dynamicImages[0]}}" mode="aspectFill"></image>
          </view>
        </view>
      </view>
      <view class="finishedText">
        <text wx:if="{{loading}}">加载中...</text>
        <text wx:elif="{{finished && dynamicMessageList.length > 0}}">暂无更多</text>
      </view>
      <custom-empty-new padding='66rpx' wx:if="{{!loading && dynamicMessageList.length === 0}}" describe="暂无动态消息" />
    
  </scroll-view>
</view>