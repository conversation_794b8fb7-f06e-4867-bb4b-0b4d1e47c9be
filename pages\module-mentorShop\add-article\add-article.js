// pages/module-mentorShop/add-article/add-article.js
import {
  addCourse
} from '~/api/mentorShop';
import {
  getCourseDetail
} from "~/api/mentorShop";
import { navigateAndAddPage } from '~/utils/pageNavigation';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    form: {
      isIndividualSale: 0, //是否单独售卖
      courseName: '', //名称
      coursePrice: '', //价格
      courseCoverUrl: '', //封面
      isBundleSale: 0, //是否关联专栏
      isTextNoCopy: 0, //是否防复制
      isMarquee: 0, //是否防录屏跑马灯
      isPublishToDynamic: 0, //是否发布至动态
      courseDetail: '', // 课程详情
      bundleColumnsIds: null, // 关联课程
      courseType: 30, //课程类型： 20-图文imageText, 30-文章article
      saleMethod: 20, //售卖方式：10-付费, 20-免费
      source: '', //来源
      author: '', //作者
    },
    mode: 'article', //模式
    showActionSheet: false,
    currentSaleType: '免费',
    showCrop: false,
    imgFile: null,
    id: '',
    autosize: {
      maxHeight: 200,
      minHeight: 40,
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let title = '添加文章';
    if (options.id) {
      if (options.courseType === '30') {
        title = '编辑文章';
      } else if (options.courseType === '20') {
        title = '编辑图文';
      }
    }
    wx.setNavigationBarTitle({
      title
    });
    
    const tempForm = wx.getStorageSync('tempVideoForm');
    if (tempForm) {
      this.setData({
        form: tempForm
      });
      this.setData({
        currentSaleType: tempForm.saleMethod === 10 ? '付费' : '免费',
        mode: tempForm.courseType === 30 ? 'article' : 'imageText'
      });
    } else if (options.id) {
      this.setData({
        id: options.id
      }, () => {
        this.getCourseDetail();
      })
    }
  },
  onShow() {
    // 检查并恢复表单数据
    const tempForm = wx.getStorageSync('tempVideoForm');
    if (tempForm) {
      this.setData({
        form: tempForm
      });
    }
    
    // , () => {
    //   wx.removeStorage({
    //     key: 'tempVideoForm'
    //   });
    // }
  },

  getCourseDetail() {
    getCourseDetail({
      coursesId: this.data.id
    }).then(res => {
      this.setData({
        form: res.data
      });
      this.setData({
        currentSaleType: res.data.saleMethod === 10 ? '付费' : '免费',
        mode: res.data.courseType === 30 ? 'article' : 'imageText'
      });
    })
  },

  showActionSheet() {
    wx.showActionSheet({
      itemList: ['免费', '付费'],
      success: (res) => {
        this.setData({
          currentSaleType: res.tapIndex === 0 ? '免费' : '付费',
          'form.saleMethod': res.tapIndex === 0 ? 20 : 10
        });
        this.updateFormData('saleMethod', res.tapIndex === 0 ? 20 : 10);
        this.updateFormData('currentSaleType', res.tapIndex === 0 ? '免费' : '付费');
      }
    });
  },
  handleArticleNameChange(e) {
    let value = e.detail;
    // 去除换行符
    value = value.replace(/\n/g, '');
    this.updateFormData('courseName', value);
  },
  handleArticleSourceChange(e) {
    let value = e.detail;
    // 去除换行符
    value = value.replace(/\n/g, '');
    this.updateFormData('source', value);
  },
  handleArticleAuthorChange(e) {
    let value = e.detail;
    // 去除换行符
    value = value.replace(/\n/g, '');
    this.updateFormData('author', value);
  },
  handleSwitchChange(e) {
    this.updateFormData('isIndividualSale', e.detail ? 1 : 0);
  },
  handlePriceChange(e) {
    let value = e.detail;
    if (typeof value === 'object' && value.hasOwnProperty('value')) {
      value = value.value;
    }
  
    // 只保留数字和小数点
    let newValue = value.replace(/[^\d.]/g, '');
  
    // 防止多个小数点
    const parts = newValue.split('.');
    if (parts.length > 2) {
      newValue = parts[0] + '.' + parts.slice(1).join('');
    }
  
    // 小数点开头补0
    if (newValue.startsWith('.')) {
      newValue = '0' + newValue;
    }
  
    // 限制小数点后2位
    const match = newValue.match(/^(\d*)(\.?\d{0,2})/);
    const validValue = match ? match[1] + match[2] : '';
  
    // 去掉开头多余的0（比如 0002 -> 2，注意 0.25 要保留）
    let finalValue = validValue;
  
    if (finalValue === '') {
      finalValue = ''; // 如果完全删除了内容，清空
    } else if (finalValue.indexOf('.') === -1) {
      finalValue = String(Number(finalValue)); // 转成数字再转回字符串，去掉无意义0
    }
  
    this.setData({
      'form.coursePrice': finalValue
    });
  },
  
  handlePriceBlur(e) {
    let value = this.data.form.coursePrice;
  
    if (!value) {
      return;
    }
  
    let num = Number(value);
  
    if (isNaN(num) || num <= 0) {
      this.setData({
        'form.coursePrice': ''
      });
      return;
    }
  
    if (num > 100000) {
      wx.showToast({
        title: '价格不能超过100000元',
        icon: 'none'
      });
      this.setData({
        'form.coursePrice': '100000'
      });
      return;
    }
  
    // 失焦时不自动补 .00
    let finalValue = value;
    if (value.includes('.')) {
      // 如果本来就是小数，最多保留2位
      finalValue = num.toFixed(2).replace(/\.?0+$/, ''); 
    }
  
    this.setData({
      'form.coursePrice': finalValue
    });
  },
  
  handleAssociationChange(e) {
    this.updateFormData('isBundleSale', e.detail ? 1 : 0);
  },
  handleCopyChange(e) {
    this.updateFormData('isTextNoCopy', e.detail ? 1 : 0);
  },
  handleDynamicChange(e) {
    this.updateFormData('isPublishToDynamic', e.detail ? 1 : 0);
  },
  updateFormData(key, value) {
    this.setData({
      [`form.${key}`]: value
    });
    // 同步更新缓存
    wx.setStorageSync('tempVideoForm', this.data.form);
  },
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const file = res.tempFiles[0];
        const fileSize = file.size / 1024 / 1024;

        if (fileSize > 5) {
          wx.showToast({
            title: "图片请控制在5MB以内",
            icon: "none",
          });
          return;
        }
        const imgFile = {
          url: file.tempFilePath,
          width: 320,
          height: 180
        };
        // 保持其他表单字段的状态
        this.setData({
          showCrop: true,
          imgFile: imgFile
        })
      },
      fail: (err) => {
        console.log('图片选择失败', err);
      }
    });
  },
  removeImage() {
    this.setData({
      'form.courseCoverUrl': ''
    });
    this.updateFormData('courseCoverUrl', '');
  },
  // 处理裁剪完成事件
  handleCropperDone(e) {
    const {
      url
    } = e.detail;
    this.setData({
      'form.courseCoverUrl': url,
      showCrop: false,
      imgFile: null
    });
    this.updateFormData('courseCoverUrl', url);
  },
  // 取消裁剪
  cancelShowCrop() {
    this.setData({
      showCrop: false,
      imgFile: null
    });
  },
  showTip(e) {
    const tipMessages = {
      association: '该商品放入专栏中进行售卖',
      copy: '详情禁止复制'
    };

    const type = e.currentTarget.dataset.type;
    const message = tipMessages[type];

    if (message) {
      wx.showModal({
        title: '',
        content: message,
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },
  onAddColumn() {
    // 保存当前表单数据到缓存
    wx.setStorageSync('tempVideoForm', this.data.form);
    navigateAndAddPage('/pages/module-mentorShop/all-column/all-column',{
      type:'add'
    })
    // wx.navigateTo({
    //   url: '/pages/module-mentorShop/all-column/all-column?type=add'
    // })
  },
  onEditDetail() {
    wx.setStorageSync('tempVideoForm', this.data.form);
    navigateAndAddPage('/pages/module-mentorShop/edit-detail/edit-detail',{
      type:this.data.mode
    })
    // wx.navigateTo({
    //   url: `/pages/module-mentorShop/edit-detail/edit-detail?type=${this.data.mode}`
    // })
  },
  onSave() {
    if (!this.validateForm()) {
      return;
    }
    const path = '/pages/module-mentorShop/course-detail/course-detail';
    const query = {
      type: this.data.mode
    };
    
    if (this.data.id) {
      query.id = this.data.id;
    }
    
    wx.setStorageSync('tempVideoForm', this.data.form);
    navigateAndAddPage(path, query);
    
    // const url = this.data.id ? `/pages/module-mentorShop/course-detail/course-detail?type=${this.data.mode}&id=${this.data.id}` : `/pages/module-mentorShop/course-detail/course-detail?type=${this.data.mode}`;
    // wx.setStorageSync('tempVideoForm', this.data.form);
    // navigateAndAddPage(url)
    // wx.navigateTo({
    //   url: url
    // })


    // const data = {
    //   ...this.data.form
    // }
    // addCourse(data).then(res => {
    //   if (res.success) {
    //     if (this.data.articleId) {
    //       wx.navigateTo({
    //       })
    //       wx.navigateTo({
    //         url: `/pages/module-mentorShop/course-detail/course-detail?type=${this.data.mode}&id=${this.data.id}`
    //       })
    //     } else {
    //       wx.navigateTo({
    //         url: `/pages/module-mentorShop/course-detail/course-detail?type=${this.data.mode}&id=${res.data}`
    //       })
    //     }
    //   }
    // })

  },

  validateForm() {
    if (!this.data.form.courseName.trim()) {
      wx.showToast({
        title: `请填写${this.data.mode === 'article' ? '文章' : '图文'}名称`,
        icon: 'none'
      });
      return false;
    }

    if (!this.data.form.courseCoverUrl) {
      wx.showToast({
        title: `请上传${this.data.mode === 'article' ? '文章' : '图文'}封面`,
        icon: 'none'
      });
      return false;
    }

    // 检查视频详情
    if (!this.data.form.courseDetail) {
      wx.showToast({
        title: `请填写${this.data.mode === 'article' ? '文章' : '图文'}详情`,
        icon: 'none'
      });
      return false;
    }


    // 检查售卖方式：单独售卖和关联售卖至少选择一种
    if (this.data.form.isIndividualSale === 0 && this.data.form.isBundleSale === 0) {
      wx.showToast({
        title: '请至少选择一种售卖方式',
        icon: 'none'
      });
      return false;
    }

    // 如果选择了单独售卖，检查价格
    if (this.data.form.isIndividualSale === 1) {
      if (this.data.currentSaleType === '付费') {
        if (!this.data.form.coursePrice) {
          wx.showToast({
            title: '请填写商品金额',
            icon: 'none'
          });
          return false;
        }

        const price = parseFloat(this.data.form.coursePrice);
        if (isNaN(price) || price <= 0 || price > 100000) {
          wx.showToast({
            title: '售价范围为0.01-100000元',
            icon: 'none'
          });
          return false;
        }
      }
    }

    // 如果选择了关联售卖，检查关联课程
    // if (this.data.form.isBundleSale === 1 && !this.data.form.bundleColumnsIds) {
    //   wx.showToast({
    //     title: '请选择关联课程',
    //     icon: 'none'
    //   });
    //   return false;
    // }

    return true;
  },

  switchMode(e) {
    const mode = e.currentTarget.dataset.mode;
    const courseType = mode === 'article' ? 30 : 20;
    
    // 重置form对象，但保留courseType
    const newForm = {
      courseType: courseType,
      isIndividualSale: 0,
      courseName: '',
      coursePrice: '',
      courseCoverUrl: '',
      isBundleSale: 0,
      isTextNoCopy: 0,
      isMarquee: 0,
      isPublishToDynamic: 0,
      courseDetail: '',
      bundleColumnsIds: null,
      saleMethod: 20,
      source: '',
      author: ''
    };
    
    this.setData({
      currentSaleType: '免费',
      mode: mode,
      form: newForm
    });
    
    // 更新缓存
    wx.setStorageSync('tempVideoForm', newForm);
  },
  onViewTemplate(e) {
    const mode = e.currentTarget.dataset.mode;
    navigateAndAddPage('/pages/module-mentorShop/article-template/article-template',{
      mode
    })
    // wx.navigateTo({
    //   url: `/pages/module-mentorShop/article-template/article-template?mode=${mode}`
    // })
  }
})