<view class="mentor-apply-welcome">
  <view class="welcome-bg">
    <image class="bg" src="https://leaping-carp.oss-cn-hangzhou.aliyuncs.com/mentor/images/common/mentor-apply-welcome.png" mode="widthFix" />
  </view>
  <view class="process">
    <view class="title">使用流程</view>
    <view class="process-box">
      <view class="process-item">
        <view class="process-item-title">
          01
          <view class="icon"></view>
        </view>
        <view class="process-item-text">提交申请</view>
      </view>
      <view class="process-transition">
        <image class="{{unfoldClose ? '' : 'arrow-top'}}" src="/static/icon/arrow-bottom-blue-double.png" mode="aspectFill"/>
      </view>
      <view class="process-item">
        <view class="process-item-title">
          02
          <view class="icon"></view>
        </view>
        <view class="process-item-text">平台审核</view>
      </view>
      <view class="process-transition">
        <image class="{{unfoldClose ? '' : 'arrow-top'}}" src="/static/icon/arrow-bottom-blue-double.png" mode="aspectFill"/>
      </view>
      <view class="process-item">
        <view class="process-item-title">
          03
          <view class="icon"></view>
        </view>
        <view class="process-item-text">开始咨询</view>
      </view>
    </view>
  </view>
  <view class="btns">
    <button class="login-accredit" bind:tap="toLogin">
      登录
    </button>
    <button class="login-accredit" bind:tap="toAppIntroduction" data-key="login">
      成为企业导师
    </button>
    <view class="tips" bind:tap="toAppIntroduction" data-key="introduction">了解毕师父企业导师</view>
    <view class="to-student" bind:tap="toStudent">前往学生端</view>
  </view>
</view>
